<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Zero Trust Approach Slide</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<script src="https://cdn.tailwindcss.com"></script>
<style>
  body {
    font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f0f2f5;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
  }
  .slide-container {
    width: 1280px;
    height: 720px;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: row;
    position: relative;
    overflow: hidden;
  }
  .slide-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    text-align: left;
    padding: 56px 80px;
    box-sizing: border-box;
  }
  h1 {
    font-size: 2.8em;
    color: #0a2351;
    margin: 0 0 22px 0;
    font-weight: 600;
    line-height: 1.2;
  }
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
  }
  li {
    font-size: 1.2em;
    color: #33475b;
    margin-bottom: 16px;
    line-height: 1.5;
    display: flex;
    align-items: flex-start;
  }
  li::before {
    content: '✓';
    color: #0078d4;
    font-size: 1.4em;
    font-weight: bold;
    margin-right: 14px;
    line-height: 1.1;
    flex: 0 0 auto;
    transform: translateY(2px);
  }
  .image-container {
    flex-basis: 46%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #0a2351;
    height: 100%;
    padding: 24px;
    box-sizing: border-box;
  }
  .diagram {
    width: 92%;
    max-width: 520px;
    height: auto;
  }
  .caption-bg { fill: rgba(255,255,255,0.12); }
  .caption-title { fill: #ffffff; font-weight: 600; font-size: 13px; }
  .caption-text { fill: #e6f2ff; font-size: 12px; }
</style>
</head>
<body>
  <div class="slide-container">
    <div class="slide-content">
      <h1>"Never Trust, Always Verify": Our Zero Trust Approach</h1>
      <ul>
        <li><strong>BeyondCorp Enterprise (Google Inspired):</strong>&nbsp;Every user, device, and application is authenticated and authorized <em>before</em> accessing any resource.</li>
        <li><strong>Identity-Centric Security:</strong>&nbsp;Strong authentication (MFA, passwordless), continuous authorization, and identity governance.</li>
        <li><strong>Microsegmentation:</strong>&nbsp;Granular network segmentation to limit the impact of breaches.</li>
        <li><strong>AI-Powered Threat Detection:</strong>&nbsp;Real-time anomaly detection using User and Entity Behavior Analytics (UEBA).</li>
        <li><strong>DevSecOps Integration:</strong>&nbsp;Embedding security into the software development lifecycle.</li>
        <li><strong>Data Loss Prevention (DLP) Everywhere:</strong>&nbsp;Protecting sensitive data across all endpoints, applications, and cloud services.</li>
      </ul>
    </div>

    <div class="image-container">
      <!-- Circular Zero Trust Diagram -->
      <svg class="diagram" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg" aria-label="Zero Trust core principles circular diagram">
        <!-- Background ring segments -->
        <!-- Common: center (300,300), outer R=220, inner r=160 -->
        <!-- Segment 1: Identity Verification (-90° to -18°) -->
        <path d="M300,80 A220,220 0 0,1 509.2,232.0 L452.2,250.6 A160,160 0 0,0 300,140 Z"
              fill="#1e88e5" stroke="#ffffff" stroke-width="2"/>
        <!-- Segment 2: Device Security (-18° to 54°) -->
        <path d="M509.2,232.0 A220,220 0 0,1 429.3,478.0 L394.0,429.4 A160,160 0 0,0 452.2,250.6 Z"
              fill="#00bcd4" stroke="#ffffff" stroke-width="2"/>
        <!-- Segment 3: Least Privilege (54° to 126°) -->
        <path d="M429.3,478.0 A220,220 0 0,1 170.7,478.0 L205.95,429.44 A160,160 0 0,0 394.0,429.4 Z"
              fill="#43a047" stroke="#ffffff" stroke-width="2"/>
        <!-- Segment 4: Microsegmentation (126° to 198°) -->
        <path d="M170.7,478.0 A220,220 0 0,1 90.8,232.0 L147.8,250.6 A160,160 0 0,0 205.95,429.44 Z"
              fill="#7e57c2" stroke="#ffffff" stroke-width="2"/>
        <!-- Segment 5: Continuous Monitoring (198° to 270°) -->
        <path d="M90.8,232.0 A220,220 0 0,1 300,80 L300,140 A160,160 0 0,0 147.8,250.6 Z"
              fill="#ef6c00" stroke="#ffffff" stroke-width="2"/>

        <!-- Center badge -->
        <circle cx="300" cy="300" r="118" fill="#0a2351" stroke="#e5e7eb" stroke-width="2"/>
        <text x="300" y="292" text-anchor="middle" font-size="22" font-weight="700" fill="#ffffff">Zero Trust</text>
        <text x="300" y="318" text-anchor="middle" font-size="12.5" fill="#cfe8ff">Never Trust, Always Verify</text>

        <!-- Icon positions (precomputed) -->
        <!-- Identity Verification icon at (405.8,154.4) -->
        <circle cx="405.8" cy="154.4" r="22" fill="#ffffff"/>
        <image href="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/web-authentication-api.svg" x="390.8" y="139.4" width="30" height="30" preserveAspectRatio="xMidYMid meet"/>
        <!-- Device Security icon at (471.2,355.6) -->
        <circle cx="471.2" cy="355.6" r="22" fill="#ffffff"/>
        <image href="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/mobile/Azure-mobile-03335-icon-service-Power-Platform.svg" x="456.2" y="340.6" width="30" height="30" preserveAspectRatio="xMidYMid meet"/>
        <!-- Least Privilege icon at (300,480) -->
        <circle cx="300" cy="480" r="22" fill="#ffffff"/>
        <image href="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/keys.svg" x="285" y="465" width="30" height="30" preserveAspectRatio="xMidYMid meet"/>
        <!-- Microsegmentation icon at (128.8,355.6) -->
        <circle cx="128.8" cy="355.6" r="22" fill="#ffffff"/>
        <image href="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/af382f4619a6b8a20527af737456bb800d9ba722/svgs/servers-isometric-icons/cloud-server.svg" x="113.8" y="340.6" width="30" height="30" preserveAspectRatio="xMidYMid meet"/>
        <!-- Continuous Monitoring icon at (194.2,154.4) -->
        <circle cx="194.2" cy="154.4" r="22" fill="#ffffff"/>
        <image href="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/observers.svg" x="179.2" y="139.4" width="30" height="30" preserveAspectRatio="xMidYMid meet"/>

        <!-- Captions (small callouts) -->
        <!-- Identity Verification caption -->
        <g transform="translate(360,98)">
          <rect x="0" y="0" rx="6" ry="6" width="146" height="48" class="caption-bg"/>
          <text x="10" y="18" class="caption-title">Identity Verification</text>
          <text x="10" y="34" class="caption-text">Verify users and devices</text>
        </g>
        <!-- Device Security caption -->
        <g transform="translate(414,392)">
          <rect x="0" y="0" rx="6" ry="6" width="146" height="48" class="caption-bg"/>
          <text x="10" y="18" class="caption-title">Device Security</text>
          <text x="10" y="34" class="caption-text">Compliant, healthy devices</text>
        </g>
        <!-- Least Privilege caption -->
        <g transform="translate(227,512)">
          <rect x="0" y="0" rx="6" ry="6" width="180" height="48" class="caption-bg"/>
          <text x="10" y="18" class="caption-title">Least Privilege Access</text>
          <text x="10" y="34" class="caption-text">Minimum necessary rights</text>
        </g>
        <!-- Microsegmentation caption -->
        <g transform="translate(58,392)">
          <rect x="0" y="0" rx="6" ry="6" width="168" height="48" class="caption-bg"/>
          <text x="10" y="18" class="caption-title">Microsegmentation</text>
          <text x="10" y="34" class="caption-text">Isolate critical assets</text>
        </g>
        <!-- Continuous Monitoring caption -->
        <g transform="translate(60,98)">
          <rect x="0" y="0" rx="6" ry="6" width="196" height="48" class="caption-bg"/>
          <text x="10" y="18" class="caption-title">Continuous Monitoring</text>
          <text x="10" y="34" class="caption-text">Detect anomalies in real time</text>
        </g>
      </svg>
    </div>
  </div>
</body>
</html>