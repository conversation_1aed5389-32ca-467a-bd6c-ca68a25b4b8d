const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 0.9; // Higher start for this layout
    const MAX_CONTENT_Y = SLIDE_HEIGHT - 0.5; // Footer starts at 5.125

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        headerBorder: 'E5E7EB',
        mainTitle: '374151', // text-gray-700
        diagramBg: 'F9FAFB',
        diagramBorder: 'E5E7EB',
        rightColTitle: '14213d',
        rightColBorder: 'fca311',
        itemTitle: '1F2937', // text-gray-800
        itemText: '4B5563', // text-gray-600
        infoBoxBg: 'EFF6FF', // bg-blue-50
        infoBoxBorder: 'BFDBFE', // border-blue-200
        footerBg: 'F3F4F6', // bg-gray-100
        footerText: '4B5563', // text-gray-600
    };

    // Font Sizes (Professional & Consistent)
    const FONT_SIZES = {
        mainTitle: 16, // text-4xl -> 16
        rightColTitle: 12, // text-2xl -> 12
        itemTitle: 10, // text-lg -> 10
        itemText: 9, // text-base -> 9
        infoBoxText: 9,
        footer: 8, // text-sm -> 8
    };

    // Layout Calculations
    const HEADER_H = 0.8;
    const FOOTER_H = 0.4;
    const CONTENT_W = SLIDE_WIDTH - (2 * SAFE_MARGIN);
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = CONTENT_W * (2/3) - 0.15;
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.3;
    const RIGHT_COL_W = CONTENT_W * (1/3) - 0.15;
    const CONTENT_AREA_H = MAX_CONTENT_Y - CONTENT_START_Y;

    // =======================================================================
    // 2. HELPER FUNCTIONS (Robust Edge Case Handling)
    // =======================================================================

    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            console.warn(`⚠️ Image failed, using fallback: ${imagePath}`);
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' }, line: { color: 'D1D5DB', width: 1 },
                rectRadius: 0.05
            });
            slide.addText(fallbackText, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fontSize: 8, color: '6B7280', align: 'center', valign: 'middle'
            });
        }
    }

    // =======================================================================
    // 3. SLIDE BACKGROUND, HEADER & FOOTER (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    // Header
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: 0, w: SLIDE_WIDTH, h: HEADER_H,
        fill: { color: COLORS.background },
        line: { color: COLORS.headerBorder, width: 1 }
    });
    addImageWithFallback(slide, 'https://download.logo.wine/logo/Google_Cloud_Platform/Google_Cloud_Platform-Logo.wine.png', {
        x: SAFE_MARGIN, y: 0.2, w: 1.2, h: 0.4
    }, 'GCP Logo');
    slide.addText('GCP Infrastructure and Tech Stack', {
        x: SAFE_MARGIN + 1.4, y: 0, w: SLIDE_WIDTH - (SAFE_MARGIN * 2) - 1.4, h: HEADER_H,
        fontSize: FONT_SIZES.mainTitle, color: COLORS.mainTitle, bold: true, valign: 'middle'
    });

    // Footer
    const footerY = SLIDE_HEIGHT - FOOTER_H;
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: footerY, w: SLIDE_WIDTH, h: FOOTER_H,
        fill: { color: COLORS.footerBg }
    });
    slide.addText('Unlocking Innovation with Google Cloud', {
        x: SAFE_MARGIN, y: footerY, w: 5.0, h: FOOTER_H,
        fontSize: FONT_SIZES.footer, color: COLORS.footerText, valign: 'middle', bold: true
    });
    slide.addText('Slide 5', {
        x: SLIDE_WIDTH - SAFE_MARGIN - 4.0, y: footerY, w: 4.0, h: FOOTER_H,
        fontSize: FONT_SIZES.footer, color: COLORS.footerText, valign: 'middle', align: 'right', bold: true
    });

    // =======================================================================
    // 4. LEFT COLUMN: DIAGRAM PLACEHOLDER (Smart Sizing)
    // =======================================================================

    // The GoJS diagram is dynamic and cannot be directly rendered.
    // A professional placeholder is created instead.
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: LEFT_COL_X, y: CONTENT_START_Y, w: LEFT_COL_W, h: CONTENT_AREA_H,
        fill: { color: COLORS.diagramBg },
        line: { color: COLORS.diagramBorder, width: 1 },
        rectRadius: 0.1
    });
    slide.addText('GCP E-commerce Architecture Diagram', {
        x: LEFT_COL_X, y: CONTENT_START_Y, w: LEFT_COL_W, h: CONTENT_AREA_H,
        fontSize: 14, color: COLORS.itemText, bold: true,
        align: 'center', valign: 'middle'
    });
    slide.addText('(Visual representation of services like Cloud CDN, Load Balancing, Compute Engine, Cloud SQL, etc.)', {
        x: LEFT_COL_X + 0.2, y: CONTENT_START_Y + (CONTENT_AREA_H / 2), w: LEFT_COL_W - 0.4, h: 1.0,
        fontSize: 9, color: COLORS.itemText,
        align: 'center', valign: 'top'
    });

    // =======================================================================
    // 5. RIGHT COLUMN: KEY COMPONENTS (Content Preservation)
    // =======================================================================

    // Vertical separator line
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X - 0.15, y: CONTENT_START_Y, w: 0, h: CONTENT_AREA_H,
        line: { color: COLORS.headerBorder, width: 1 }
    });

    let currentY = CONTENT_START_Y;

    // Title
    slide.addText('Key Components', {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: 0.3,
        fontSize: FONT_SIZES.rightColTitle, color: COLORS.rightColTitle, bold: true
    });
    currentY += 0.3;
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W * 0.6, h: 0,
        line: { color: COLORS.rightColBorder, width: 2 }
    });
    currentY += 0.3;

    // Item 1: Regions and Zones
    addImageWithFallback(slide, 'https://img.icons8.com/color/48/000000/geography.png', {
        x: RIGHT_COL_X, y: currentY, w: 0.3, h: 0.3
    }, 'Geo');
    slide.addText('Regions and Zones', {
        x: RIGHT_COL_X + 0.4, y: currentY, w: RIGHT_COL_W - 0.4, h: 0.25,
        fontSize: FONT_SIZES.itemTitle, color: COLORS.itemTitle, bold: true
    });
    currentY += 0.25;
    slide.addText('Global coverage for high availability and low latency.', {
        x: RIGHT_COL_X + 0.4, y: currentY, w: RIGHT_COL_W - 0.4, h: 0.4,
        fontSize: FONT_SIZES.itemText, color: COLORS.itemText
    });
    currentY += 0.5;

    // Item 2: Built-in Security
    addImageWithFallback(slide, 'https://img.icons8.com/color/48/000000/security-shield-green.png', {
        x: RIGHT_COL_X, y: currentY, w: 0.3, h: 0.3
    }, 'Sec');
    slide.addText('Built-in Security', {
        x: RIGHT_COL_X + 0.4, y: currentY, w: RIGHT_COL_W - 0.4, h: 0.25,
        fontSize: FONT_SIZES.itemTitle, color: COLORS.itemTitle, bold: true
    });
    currentY += 0.25;
    slide.addText('Multi-layered security, encryption, and compliance.', {
        x: RIGHT_COL_X + 0.4, y: currentY, w: RIGHT_COL_W - 0.4, h: 0.3,
        fontSize: FONT_SIZES.itemDesc, color: COLORS.itemDesc
    });

    return pptx.writeFile({ fileName: 'experimentation/html_to_pptx_experiments/production_v2/scripts/generated/presentations/slide5_general_gcp_infra.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
