const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE DEFAULTS & CONFIGURATION
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8; // Absolute vertical limit for any element

    // Two-column layout coordinates
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 5.0; // Table column
    const RIGHT_COL_X = 5.5;
    const RIGHT_COL_W = 3.3; // ROI list column

    // Colors from HTML
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_BORDER = '1a3a6e';
    const COLOR_TABLE_HEADER_FILL = '1a3a6e'; // Adjusted for visibility

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Add slide title
    slide.addText("Investing in a Secure Future", {
        x: LEFT_COL_X,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16, // Ultra-safe title size
        color: COLOR_ACCENT,
        bold: true,
        align: 'left'
    });

    // Add title divider shape
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X,
        y: 0.85,
        w: 1.2,
        h: 0.03,
        fill: { color: COLOR_ACCENT }
    });

    // --- LEFT COLUMN: COST TABLE ---
    const tableRows = [
        // Header row with ultra-safe styling
        [
            { text: "Category", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL, align: 'left' } },
            { text: "Estimated Cost", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL, align: 'left' } },
            { text: "Notes", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL, align: 'left' } }
        ],
        // Data rows with ultra-safe styling
        [
            { text: "Software & Cloud Services", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$50,000/year", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "Azure AD, Intune, Cloudflare ZTNA, Microsoft Purview", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Hardware", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$5,000", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "Minimal, depends on existing infrastructure", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Personnel Costs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$150,000/year", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "Security team, cloud experts, and training staff", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Training Costs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$10,000", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "Employee and technical training programs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Consulting Fees", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$20,000", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "If using external implementation consultants", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        // Footer row (as a regular row for simplicity and control)
        [
            { text: "Total Estimated Cost", options: { fontSize: 9, color: COLOR_ACCENT, bold: true } },
            { text: "$235,000", options: { fontSize: 9, color: COLOR_ACCENT, bold: true } },
            { text: "", options: { fontSize: 8 } }
        ]
    ];

    slide.addTable(tableRows, {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        colW: [1.5, 1.0, 2.5], // Total width = 5.0 (SAFE)
        border: { type: 'solid', pt: 1, color: COLOR_BORDER },
        autoPage: false, // Critical for preventing overflow
        rowH: 0.35, // Provides consistent spacing
        valign: 'middle'
    });

    // --- RIGHT COLUMN: ROI LIST ---
    const roiItems = [
        { boldText: "Reduced Risk of Data Breaches:", regularText: "Quantify savings from preventing costly breaches." },
        { boldText: "Improved Compliance:", regularText: "Avoid fines and penalties from non-compliance." },
        { boldText: "Increased Productivity:", regularText: "Streamlined access and reduced security-related downtime." },
        { boldText: "Enhanced Reputation:", regularText: "Maintain customer trust and protect brand value." }
    ];

    // Add vertical divider line
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X - 0.1, y: CONTENT_START_Y, w: 0, h: 3.5,
        line: { color: COLOR_BORDER, width: 2 }
    });

    // Add ROI sub-title
    slide.addText("Return on Investment (ROI)", {
        x: RIGHT_COL_X,
        y: CONTENT_START_Y,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: 12,
        color: COLOR_TEXT_SECONDARY,
        bold: true
    });

    let currentY = CONTENT_START_Y + 0.4; // Start below the ROI title
    const iconSize = 0.2;
    const textX = RIGHT_COL_X + iconSize + 0.1;
    const textW = RIGHT_COL_W - (iconSize + 0.1);
    const itemSpacing = 0.7; // Generous spacing to avoid vertical collision

    roiItems.forEach(item => {
        // Add a safe, text-based icon for each list item
        slide.addText("✓", {
            x: RIGHT_COL_X,
            y: currentY,
            w: iconSize,
            h: iconSize,
            fontSize: 14,
            color: COLOR_ACCENT,
            bold: true,
            align: 'center'
        });

        // Add the composite text (bold + regular)
        slide.addText([
            { text: item.boldText + " ", options: { fontSize: 9, color: COLOR_TEXT_SECONDARY, bold: true } },
            { text: item.regularText, options: { fontSize: 9, color: COLOR_TEXT_PRIMARY } }
        ], {
            x: textX,
            y: currentY,
            w: textW,
            h: 0.6, // Allow space for wrapping
            lineSpacing: 12
        });

        currentY += itemSpacing;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_8_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
