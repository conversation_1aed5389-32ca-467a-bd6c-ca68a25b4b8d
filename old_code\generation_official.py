from llm.llmwrapper_official import LLM
from PIL import Image
from htmlrender.renderer import <PERSON><PERSON><PERSON>ender<PERSON>
from utils.utils import find_text_in_between_tags
import textwrap
from loguru import logger
from prompt.experiment_prompts import generationpy_slide_review_prompt_daniel_v1, generationpy_title_slide_prompt_duy_v1, generationpy_agenda_slide_prompt_duy_v1, generationpy_slide_prompt_duy_v1
from llm.few_shot_template import Example_1_inforgraphic, Example_2_inforgraphic, Example_1_agenda, Example_2_agenda, Example_1_title, Example_2_title

class Generator():
    def __init__(self, render_resize_ratio=0.4):
        self.rndr = HTMLRenderer()
        self.render_resize_ratio = render_resize_ratio

    def reviewer(self, html_code : str, html_image : Image.Image, llm : LLM)-> dict:
        slide_review_prompt = generationpy_slide_review_prompt_daniel_v1

        #Sanitizing input html
        html_code = find_text_in_between_tags(html_code, start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

        review_prompt = slide_review_prompt.format(code=html_code)
        response = llm.call_with_images(query=review_prompt, images=[html_image])

        if '<OK>' in response['text']:
            return ({"status" : "unchanged", "html_code" : html_code})
        else:
            modified_html = find_text_in_between_tags(response['text'], start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)
            return ({"status" : "modified", "html_code" : modified_html})


    def generate_title_slide(self, query : str, slide_content : str, generator_llm : LLM, reviewer_llm : LLM, review : bool = True) -> str:
        title_slide_prompt = generationpy_title_slide_prompt_duy_v1.format(query=query, slide_content=slide_content,Example_1 = Example_1_title, Example_2 = Example_2_title)
        
        logger.info("Generating title slide...")
        html_code = \
            find_text_in_between_tags(generator_llm.call(query=title_slide_prompt)['text'], 
                                      start_tag="<!DOCTYPE html>", 
                                      end_tag="</html>", 
                                      inclusive=True)

        if review:
            logger.info("Reviewing generated HTML...")
            html_img = self.rndr.renderHTML(html_str=html_code, resize=True, resize_ratio=self.render_resize_ratio)
            review_response = self.reviewer(html_code, html_img, reviewer_llm)
            html_code = review_response['html_code']

        return html_code
    

    def generate_agenda_slide(self, query : str, slide_content : str, title_slide_html : str, generator_llm : LLM, reviewer_llm : LLM, review : bool = True) -> str:
        agenda_slide_prompt = generationpy_agenda_slide_prompt_duy_v1.format(query=query, title_slide_html=title_slide_html, slide_content=slide_content, Example_1 = Example_1_agenda, Example_2 = Example_2_agenda)

        logger.info("Generating Agenda slide...")

        html_code = \
            find_text_in_between_tags(generator_llm.call(query=agenda_slide_prompt)['text'], 
                                      start_tag="<!DOCTYPE html>", 
                                      end_tag="</html>", 
                                      inclusive=True)

        if review:
            logger.info("Reviewing generated HTML...")
            html_img = self.rndr.renderHTML(html_str=html_code, resize=True, resize_ratio=self.render_resize_ratio)
            review_response = self.reviewer(html_code, html_img, reviewer_llm)
            html_code = review_response['html_code']

        return html_code


    def generate_general_slide(self, query : str, slide_content : str, existing_slide_content : dict, generator_llm : LLM, reviewer_llm : LLM, review : bool = True) -> str:
        existing_slides = "\n".join([f"{slide['name']} html code:\n```html\n{slide['html']}\n```\n" for slide in existing_slide_content])
        slide_prompt = generationpy_slide_prompt_duy_v1.format(query=query, slide_content=slide_content, existing_slides=existing_slides, Example_1 = Example_1_inforgraphic, Example_2 = Example_2_inforgraphic)

        logger.info("Generating slide...")

        # Call LLM with error handling
        try:
            llm_response = generator_llm.call(query=slide_prompt)
            if llm_response is None or 'text' not in llm_response:
                logger.error("LLM returned invalid response")
                raise Exception("LLM returned invalid response")

            llm_text = llm_response['text']
            if llm_text is None:
                logger.error("LLM returned None text")
                raise Exception("LLM returned None text")

            html_code = find_text_in_between_tags(llm_text,
                                                  start_tag="<!DOCTYPE html>",
                                                  end_tag="</html>",
                                                  inclusive=True)

            if not html_code:
                logger.error("No HTML content found in LLM response")
                # Return a basic error slide
                html_code = """<!DOCTYPE html>
                <html>
                <head><style>body { font-family: Arial; padding: 20px; }</style></head>
                <body><h1>Error generating slide content</h1><p>Please try again.</p></body>
                </html>"""

        except Exception as e:
            logger.error(f"Error in LLM call: {e}")
            # Return a basic error slide
            html_code = f"""<!DOCTYPE html>
            <html>
            <head><style>body {{ font-family: Arial; padding: 20px; color: red; }}</style></head>
            <body><h1>Error generating slide</h1><p>Error: {str(e)}</p></body>
            </html>"""

        if review:
            logger.info("Reviewing generated HTML...")
            html_img = self.rndr.renderHTML(html_str=html_code, resize=True, resize_ratio=self.render_resize_ratio)
            review_response = self.reviewer(html_code, html_img, reviewer_llm)
            html_code = review_response['html_code']

        return html_code