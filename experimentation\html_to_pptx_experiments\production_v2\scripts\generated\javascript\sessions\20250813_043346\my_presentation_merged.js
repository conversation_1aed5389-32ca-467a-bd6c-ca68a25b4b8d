
// Consolidated Multi-Slide PowerPoint Generator
// Generated by JavaScript-based merging approach (production_v2)

const PptxGenJS = require('pptxgenjs');


function generateSlide2AgendaUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_2_agenda_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const CONTENT_WIDTH = 10.0 - (2 * SAFE_MARGIN); // 9.4, but we'll use a safer 8.2
    const ICON_TEXT_GAP = 0.3;
    const ICON_SIZE = 0.2;
    const ITEM_SPACING = 0.5; // Vertical space between list items

    // ULTRA-SAFE COLOR PALETTE (from HTML analysis)
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';

    // Set slide background color
    slide.background = { color: COLOR_BACKGROUND };

    // Add Title
    slide.addText("Fortifying Our Defenses: A Zero Trust Approach", {
        x: SAFE_MARGIN,
        y: SAFE_MARGIN,
        w: 8.2,
        h: 0.5,
        fontSize: 16, // Ultra-safe max title size
        color: COLOR_ACCENT,
        bold: true,
    });

    // Add Title Divider (using a RECTANGLE shape for precision)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.2, // 120px is roughly 1.2 inches
        h: 0.03, // 3px is very thin
        fill: { color: COLOR_ACCENT },
    });

    // Agenda content array
    const agendaItems = [
        {
            strong: "The Challenge:",
            text: "Current security relies on outdated \"trust but verify\" models, leaving us vulnerable to sophisticated attacks and insider threats."
        },
        {
            strong: "The Solution:",
            text: "Implement a comprehensive Zero Trust security framework, assuming no user or device is inherently trustworthy."
        },
        {
            strong: "Key Components:",
            text: "Strong Identity & Access Management, Device Security, Network Microsegmentation, and Data Protection."
        },
        {
            strong: "Cloud-First Strategy:",
            text: "Leverage cloud services for scalability, cost-effectiveness, and advanced security features."
        },
        {
            strong: "Benefits:",
            text: "Reduced risk of breaches, improved compliance, enhanced data protection, and increased operational efficiency."
        },
        {
            strong: "Call to Action:",
            text: "Invest in a phased Zero Trust implementation to secure our sensitive data and prevent cyberattacks."
        },
    ];

    // Function to get ultra-safe font size based on content density
    function getUltraSafeFontSize(elementCount, baseSize) {
        let size = baseSize;
        if (elementCount > 15) size = 8;
        else if (elementCount > 12) size = 9;
        else if (elementCount > 8) size = 10;
        else if (elementCount > 5) size = 11;
        return Math.max(size, 8); // Never below 8px
    }

    const FONT_SIZE = getUltraSafeFontSize(agendaItems.length, 10);

    // Dynamically add agenda items with overflow protection
    let currentY = CONTENT_START_Y + 0.3; // Start slightly lower after divider
    const TEXT_X = SAFE_MARGIN + ICON_SIZE + ICON_TEXT_GAP;
    const TEXT_W = 8.2 - (ICON_SIZE + ICON_TEXT_GAP);

    agendaItems.forEach(item => {
        // VERTICAL SPACE MONITORING: Stop if the next item will overflow
        if (currentY + ITEM_SPACING > MAX_CONTENT_Y) {
            console.warn("Skipping remaining agenda items to prevent vertical overflow.");
            return;
        }

        // Add Icon (using a text-based checkmark for maximum safety)
        slide.addText("✓", {
            x: SAFE_MARGIN,
            y: currentY,
            w: ICON_SIZE,
            h: ICON_SIZE,
            fontSize: 12,
            color: COLOR_ACCENT,
            bold: true,
            align: 'center',
            valign: 'top'
        });

        // Add Text with Rich Text Formatting for bold parts
        slide.addText(
            [
                { text: item.strong + " ", options: { color: COLOR_TEXT_SECONDARY, bold: true, fontSize: FONT_SIZE } },
                { text: item.text, options: { color: COLOR_TEXT_PRIMARY, fontSize: FONT_SIZE } }
            ],
            {
                x: TEXT_X,
                y: currentY,
                w: TEXT_W,
                h: ITEM_SPACING - 0.1, // Height for the text box
                lineSpacing: FONT_SIZE + 4, // Auto-adjust line spacing based on font size
            }
        );

        currentY += ITEM_SPACING;
    });

    return slide;
}

function generateSlide3GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_3_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS (GENERIC)
    const SAFE_MARGIN = 0.3;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Two-column layout constants
    const LEFT_COL_X = 0.5;
    const LEFT_COL_W = 3.5;
    const RIGHT_COL_X = 4.5;
    const RIGHT_COL_W = 4.5;

    // Color Palette (from HTML analysis)
    const BG_COLOR = '0a192f';
    const ACCENT_COLOR = '64ffda';
    const TEXT_COLOR_PRIMARY = 'ccd6f6';
    const TEXT_COLOR_SECONDARY = 'a8b2d1';

    // Set slide background
    slide.background = { color: BG_COLOR };

    // 1. Title
    slide.addText("The Weaknesses of Our Current Security Posture", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16, // Ultra-safe max title size
        color: ACCENT_COLOR,
        bold: true,
        align: 'left'
    });

    // 2. Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.9,
        w: 1.2, // 120px equivalent
        h: 0.03, // 3px equivalent
        fill: { color: ACCENT_COLOR }
    });

    // 3. Left Column: Visual Representation
    let currentY = CONTENT_START_Y + 1.0; // Start lower for visual centering

    // Recreate "Castle" icon using basic shapes (ULTRA-SAFE)
    // Main structure
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X + 0.75, y: currentY + 0.5, w: 2.0, h: 1.5,
        fill: { color: ACCENT_COLOR }
    });
    // Turrets
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X + 0.75, y: currentY, w: 0.5, h: 0.7,
        fill: { color: ACCENT_COLOR }
    });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X + 2.25, y: currentY, w: 0.5, h: 0.7,
        fill: { color: ACCENT_COLOR }
    });

    // Recreate "Unlocked" icon using basic shapes (ULTRA-SAFE)
    // Lock body
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X + 1.4, y: currentY + 1.2, w: 0.7, h: 0.5,
        fill: { color: TEXT_COLOR_SECONDARY }
    });
    // Lock shackle (arc)
    slide.addShape(pptx.shapes.ARC, {
        x: LEFT_COL_X + 1.2, y: currentY + 0.7, w: 0.7, h: 0.7,
        line: { color: TEXT_COLOR_SECONDARY, width: 3 },
        angleRange: [180, 360]
    });

    // Visual Label
    slide.addText("Perimeter Security", {
        x: LEFT_COL_X,
        y: currentY + 2.3,
        w: LEFT_COL_W,
        h: 0.4,
        fontSize: 12,
        color: ACCENT_COLOR,
        bold: true,
        align: 'center',
        border: { type: 'solid', pt: 2, color: ACCENT_COLOR },
        margin: 4
    });

    // 4. Right Column: Weakness List
    const weaknesses = [
        { bold: "Increased Sophistication of Cyberattacks:", text: "Attackers are bypassing traditional perimeter defenses." },
        { bold: "Insider Threats:", text: "Malicious or negligent employees can compromise sensitive data." },
        { bold: "Complex IT Environment:", text: "Cloud adoption, remote work, and BYOD create new attack vectors." },
        { bold: "Compliance Requirements:", text: "Regulations like GDPR and CCPA demand stronger data protection measures." },
        { bold: "Lack of Visibility:", text: "Difficult to track user activity and identify suspicious behavior across the network." },
        { bold: "The Cost of Inaction:", text: "Breaches result in significant financial, reputational, and legal liabilities." }
    ];

    // ULTRA-SAFE DYNAMIC FONT SIZING
    function getUltraSafeFontSize(elementCount, baseSize) {
        let size = baseSize;
        if (elementCount > 15) size = 8;
        else if (elementCount > 12) size = 9;
        else if (elementCount > 8) size = 10;
        else if (elementCount > 5) size = 11;
        return Math.max(size, 8);
    }
    const contentFontSize = getUltraSafeFontSize(weaknesses.length, 11);

    // ULTRA-SAFE VERTICAL POSITIONING
    currentY = CONTENT_START_Y + 0.2;
    const itemHeight = 0.6;

    weaknesses.forEach(item => {
        // Vertical overflow check
        if ((currentY + itemHeight) > MAX_CONTENT_Y) {
            console.warn("Skipping element to prevent vertical overflow.");
            return;
        }

        // Icon (recreated as a basic shape for safety)
        slide.addShape(pptx.shapes.RIGHT_TRIANGLE, {
            x: RIGHT_COL_X, y: currentY + 0.1, w: 0.25, h: 0.25,
            fill: { color: ACCENT_COLOR }, rotate: 90
        });
        slide.addText("!", {
            x: RIGHT_COL_X, y: currentY + 0.05, w: 0.25, h: 0.25,
            fontSize: 14, bold: true, color: BG_COLOR, align: 'center'
        });

        // Text content
        slide.addText([
            { text: item.bold + " ", options: { fontSize: contentFontSize, color: TEXT_COLOR_SECONDARY, bold: true } },
            { text: item.text, options: { fontSize: contentFontSize, color: TEXT_COLOR_PRIMARY } }
        ], {
            x: RIGHT_COL_X + 0.4,
            y: currentY,
            w: RIGHT_COL_W - 0.4,
            h: itemHeight,
            lineSpacing: contentFontSize + 4
        });

        currentY += itemHeight;
    });

    return slide;
}

function generateSlide4GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_4_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer column widths

    // Two-column layout constants
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.5; // 55% of available width, rounded for safety
    const RIGHT_COL_X = 5.2; // Left col (0.3 + 4.5) + 0.4 gap
    const RIGHT_COL_W = 3.3; // 45% of available width, rounded for safety

    // Color Palette (from CSS)
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_BORDER = '1a3a6e';
    const COLOR_CARD_FILL = '1a2c46'; // Approximated from rgba(42, 68, 110, 0.3) on dark bg

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Main Title
    slide.addText("Zero Trust: Never Trust, Always Verify", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: CONTENT_WIDTH,
        h: 0.4,
        fontSize: 16,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2,
        h: 0.03,
        fill: { color: COLOR_PRIMARY_ACCENT },
    });

    // --- Left Column Content ---
    let currentY = CONTENT_START_Y;
    const leftContent = [
        { strong: "Identity-Centric Security:", text: "Verify every user and device before granting access." },
        { strong: "Microsegmentation:", text: "Isolate applications and data to limit the blast radius of a breach." },
        { strong: "Continuous Monitoring:", text: "Constantly monitor user activity, device health, and network traffic for suspicious behavior." },
        { strong: "Data-Centric Protection:", text: "Protect sensitive data at rest and in transit with encryption and DLP." },
        { strong: "Automation & Orchestration:", text: "Automate security tasks and workflows to improve efficiency and reduce response times." },
    ];

    const ICON_SIZE = 0.22;
    const ICON_MARGIN_RIGHT = 0.15;
    const TEXT_START_X = LEFT_COL_X + ICON_SIZE + ICON_MARGIN_RIGHT;
    const TEXT_WIDTH = LEFT_COL_W - ICON_SIZE - ICON_MARGIN_RIGHT;
    const ITEM_SPACING = 0.5;

    leftContent.forEach(item => {
        if (currentY + ITEM_SPACING > MAX_CONTENT_Y) return; // Overflow check

        // Icon (using a safe, basic shape as a placeholder)
        slide.addShape(pptx.shapes.OVAL, {
            x: LEFT_COL_X,
            y: currentY + 0.04, // Align vertically
            w: ICON_SIZE,
            h: ICON_SIZE,
            fill: { color: COLOR_PRIMARY_ACCENT, transparency: 30 },
            line: { color: COLOR_PRIMARY_ACCENT, width: 1 }
        });

        // List Item Text
        slide.addText([
            { text: item.strong + " ", options: { color: COLOR_TEXT_SECONDARY, bold: true } },
            { text: item.text, options: { color: COLOR_TEXT_PRIMARY } }
        ], {
            x: TEXT_START_X,
            y: currentY,
            w: TEXT_WIDTH,
            h: ITEM_SPACING - 0.1, // Reserve small gap
            fontSize: 9,
            lineSpacing: 12
        });

        currentY += ITEM_SPACING;
    });

    // Conclusion Text (at the bottom of the left column)
    const CONCLUSION_H = 0.6;
    const CONCLUSION_Y = MAX_CONTENT_Y - CONCLUSION_H;
    // Border line for conclusion
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X,
        y: CONCLUSION_Y,
        w: 0.03,
        h: CONCLUSION_H,
        fill: { color: COLOR_PRIMARY_ACCENT }
    });
    slide.addText([
        { text: "Why it Works: ", options: { color: COLOR_PRIMARY_ACCENT, bold: true } },
        { text: "Zero Trust eliminates implicit trust, making it significantly harder for attackers to gain access to sensitive data.", options: { color: COLOR_TEXT_SECONDARY } }
    ], {
        x: LEFT_COL_X + 0.2,
        y: CONCLUSION_Y,
        w: LEFT_COL_W - 0.2,
        h: CONCLUSION_H,
        fontSize: 9,
        lineSpacing: 13
    });

    // --- Right Column Content ---
    currentY = CONTENT_START_Y;
    const rightContent = [
        { title: "Verify Explicitly", desc: "Authenticate and authorize based on all available data points." },
        { title: "Least Privilege Access", desc: "Limit user access with just-in-time and just-enough-access (JIT/JEA)." },
        { title: "Assume Breach", desc: "Minimize blast radius and segment access. Verify all sessions are encrypted." },
    ];

    const CARD_H = 1.1;
    const CARD_SPACING = 0.2;
    const CARD_ICON_SIZE = 0.5;
    const CARD_TEXT_X_OFFSET = CARD_ICON_SIZE + 0.2;

    rightContent.forEach(card => {
        if (currentY + CARD_H > MAX_CONTENT_Y) return; // Overflow check

        // Diagram Card (using a rounded rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: RIGHT_COL_X,
            y: currentY,
            w: RIGHT_COL_W,
            h: CARD_H,
            fill: { color: COLOR_CARD_FILL },
            line: { color: COLOR_BORDER, width: 1 },
            rectRadius: 0.08
        });

        // Diagram Icon (using a safe shape)
        slide.addShape(pptx.shapes.OVAL, {
            x: RIGHT_COL_X + 0.2,
            y: currentY + (CARD_H - CARD_ICON_SIZE) / 2, // Center vertically
            w: CARD_ICON_SIZE,
            h: CARD_ICON_SIZE,
            fill: { color: COLOR_PRIMARY_ACCENT, transparency: 50 }
        });

        // Diagram Text
        slide.addText(card.title, {
            x: RIGHT_COL_X + CARD_TEXT_X_OFFSET,
            y: currentY + 0.2,
            w: RIGHT_COL_W - CARD_TEXT_X_OFFSET - 0.2,
            h: 0.3,
            fontSize: 10,
            color: COLOR_TEXT_PRIMARY,
            bold: true
        });
        slide.addText(card.desc, {
            x: RIGHT_COL_X + CARD_TEXT_X_OFFSET,
            y: currentY + 0.5,
            w: RIGHT_COL_W - CARD_TEXT_X_OFFSET - 0.2,
            h: 0.4,
            fontSize: 9,
            color: COLOR_TEXT_SECONDARY
        });

        currentY += CARD_H + CARD_SPACING;
    });

    return slide;
}

function generateSlide5GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_5_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ------------------- ULTRA-SAFE DEFAULTS & HELPERS -------------------

    // Colors from CSS analysis
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_SHAPE_FILL = '2a4365'; // Derived from rgba(42, 67, 101, 0.5)
    const COLOR_SHAPE_BORDER = '1d3b66';

    // Ultra-safe canvas boundaries
    const SAFE_MARGIN = 0.3;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Two-column layout dimensions
    const LEFT_COL_X = 0.4;
    const LEFT_COL_W = 4.2;
    const RIGHT_COL_X = 4.9;
    const RIGHT_COL_W = 4.5;

    // Function to add a lock icon using valid shapes
    function addLockIcon(x, y) {
        const iconSize = 0.2;
        // Body (Rounded Rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: x,
            y: y + 0.05,
            w: iconSize,
            h: iconSize * 0.75,
            fill: { color: COLOR_PRIMARY_ACCENT },
            rectRadius: 0.05
        });
        // Shackle (Arc)
        slide.addShape(pptx.shapes.ARC, {
            x: x + 0.025,
            y: y,
            w: iconSize * 0.75,
            h: iconSize * 0.75,
            line: { color: COLOR_PRIMARY_ACCENT, width: 1.5 },
            angleRange: [180, 360]
        });
    }

    // ------------------- SLIDE CONTENT -------------------

    // Set slide background color
    slide.background = { color: COLOR_BACKGROUND };

    // 1. Slide Title
    slide.addText("Building a Secure Foundation with Cloud Technologies", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        align: 'left'
    });

    // 2. Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.5, // 120px is ~1.25", using 1.5" for visual balance
        h: 0,
        line: { color: COLOR_PRIMARY_ACCENT, width: 2 }
    });

    // ------------------- LEFT COLUMN (DIAGRAM) -------------------
    const diagramLayers = [
        "Identity: Azure AD Conditional Access",
        "Application: Web Apps / Microservices",
        "Data: S3 Buckets / Azure Blob Storage",
        "Compute: EC2 Instances / Azure VMs",
        "Network: Azure Virtual Network / NSGs",
        "Infrastructure: Cloud Provider"
    ];

    let currentY = CONTENT_START_Y + 0.2;
    const layerHeight = 0.45;
    const layerSpacing = 0.1;

    diagramLayers.forEach((text, index) => {
        const layerY = currentY + index * (layerHeight + layerSpacing);
        if ((layerY + layerHeight) > MAX_CONTENT_Y) return; // Overflow prevention

        // Layer Box
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: LEFT_COL_X,
            y: layerY,
            w: LEFT_COL_W,
            h: layerHeight,
            fill: { color: COLOR_SHAPE_FILL },
            line: { color: COLOR_SHAPE_BORDER, width: 1 },
            rectRadius: 0.1
        });

        // Layer Text
        slide.addText(text, {
            x: LEFT_COL_X + 0.15,
            y: layerY,
            w: LEFT_COL_W - 0.7,
            h: layerHeight,
            fontSize: 9,
            color: COLOR_TEXT_PRIMARY,
            valign: 'middle'
        });

        // Icons
        const iconX = LEFT_COL_X + LEFT_COL_W - 0.5;
        const iconY = layerY + (layerHeight - 0.2) / 2;
        if (index < 5) { // First 5 layers have a lock icon
            addLockIcon(iconX, iconY);
        } else { // Last layer has provider icons
            slide.addText("AWS", { x: iconX - 0.6, y: layerY, w: 0.5, h: layerHeight, color: 'FF9900', bold: true, fontSize: 8, valign: 'middle' });
            slide.addText("Azure", { x: iconX - 0.2, y: layerY, w: 0.5, h: layerHeight, color: '0078D4', bold: true, fontSize: 8, valign: 'middle' });
            addLockIcon(iconX + 0.2, iconY);
        }
    });

    // ------------------- RIGHT COLUMN (TECHNOLOGIES) -------------------
    const techItems = [
        { title: "Identity & Access Management (IAM)", details: "Azure AD Conditional Access + Passwordless Auth" },
        { title: "Device Security & Endpoint Management", details: "Microsoft Endpoint Manager (Intune) + DPA + RBI" },
        { title: "Network Security & Microsegmentation", details: "Azure VNets/NSGs + Cloudflare ZTNA + AI Anomaly" },
        { title: "Data Security & Protection", details: "Microsoft Purview + Cloud-Native Key Management" },
        { title: "Automation & Orchestration", details: "Azure Logic Apps + Ansible / Terraform (IaC)" }
    ];

    currentY = CONTENT_START_Y + 0.1;
    const itemHeight = 0.6;
    const itemSpacing = 0.15;

    techItems.forEach((item, index) => {
        const itemY = currentY + index * (itemHeight + itemSpacing);
        if ((itemY + itemHeight) > MAX_CONTENT_Y) return; // Overflow prevention

        // Tech Item Title
        slide.addText(item.title, {
            x: RIGHT_COL_X,
            y: itemY,
            w: RIGHT_COL_W,
            h: 0.2,
            fontSize: 10,
            color: COLOR_PRIMARY_ACCENT,
            bold: true
        });

        // Tech Item Details
        slide.addText(item.details, {
            x: RIGHT_COL_X,
            y: itemY + 0.2,
            w: RIGHT_COL_W,
            h: 0.4,
            fontSize: 9,
            color: COLOR_TEXT_SECONDARY,
            valign: 'top'
        });
    });

    return slide;
}

function generateSlide6GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_6_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE DEFAULTS & CONSTANTS
    const SAFE_X = 0.3;
    const SAFE_W = 8.2;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Color Palette (from HTML analysis)
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_TEXT_TERTIARY = '8892b0';

    // Slide Background
    slide.background = { color: COLOR_BACKGROUND };

    // Title
    slide.addText('Assembling the Right Team for Success', {
        x: SAFE_X,
        y: TITLE_Y,
        w: 7.0, // Keep title width reasonable
        h: TITLE_H,
        fontSize: 16, // Ultra-safe max title size
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        align: 'left'
    });

    // Title Divider (using a valid RECTANGLE shape)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_X,
        y: 0.85,
        w: 1.2,
        h: 0.04,
        fill: { color: COLOR_PRIMARY_ACCENT }
    });

    // Two-Column Layout Constants
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.5;
    const RIGHT_COL_X = 5.0;
    const RIGHT_COL_W = 3.5;

    // Left Column: Team List
    let currentY = CONTENT_START_Y + 0.2; // Start slightly lower
    const iconSize = 0.25;
    const textIndentX = LEFT_COL_X + iconSize + 0.15;
    const textIndentW = LEFT_COL_W - (iconSize + 0.15);
    const itemSpacing = 0.7; // Vertical space between each main list item

    // Helper function to add list items safely
    function addListItem(iconShape, title, description) {
        if (currentY + 0.5 > MAX_CONTENT_Y) return; // Prevent overflow

        // Icon (using valid OVAL shape as a placeholder)
        slide.addShape(iconShape, {
            x: LEFT_COL_X,
            y: currentY + 0.05,
            w: iconSize,
            h: iconSize,
            fill: { color: COLOR_PRIMARY_ACCENT }
        });

        // Title Text
        slide.addText(title, {
            x: textIndentX,
            y: currentY,
            w: textIndentW,
            h: 0.2,
            fontSize: 10,
            color: COLOR_TEXT_SECONDARY,
            bold: true
        });

        // Description Text
        slide.addText(description, {
            x: textIndentX,
            y: currentY + 0.2,
            w: textIndentW,
            h: 0.3,
            fontSize: 9,
            color: COLOR_TEXT_PRIMARY
        });

        currentY += itemSpacing;
    }

    // Add list items
    addListItem(pptx.shapes.ROUNDED_RECTANGLE, 'Dedicated Security Team', 'Requires experienced security engineers, architects, and analysts to lead the implementation.');
    addListItem(pptx.shapes.OVAL, 'Cloud Expertise', 'Personnel with deep knowledge of chosen cloud platforms (Azure, AWS, or Google Cloud).');
    addListItem(pptx.shapes.RECTANGLE, 'IAM Specialists', 'Experts in identity and access management solutions are crucial for the core of Zero Trust.');

    // Special handling for the item with a sub-list
    if (currentY + 0.8 <= MAX_CONTENT_Y) {
        // Icon
        slide.addShape(pptx.shapes.RIGHT_TRIANGLE, {
            x: LEFT_COL_X,
            y: currentY + 0.05,
            w: iconSize,
            h: iconSize,
            fill: { color: COLOR_PRIMARY_ACCENT }
        });
        // Title
        slide.addText('Training & Awareness Programs', {
            x: textIndentX,
            y: currentY,
            w: textIndentW,
            h: 0.2,
            fontSize: 10,
            color: COLOR_TEXT_SECONDARY,
            bold: true
        });
        // Sub-list items
        slide.addText([
            { text: '• Educate all employees on Zero Trust principles.', options: { breakLine: true } },
            { text: '• Provide in-depth technical training for IT staff.' }
        ], {
            x: textIndentX + 0.2,
            y: currentY + 0.25,
            w: textIndentW - 0.2,
            h: 0.4,
            fontSize: 8,
            color: COLOR_TEXT_TERTIARY
        });
        currentY += itemSpacing + 0.1;
    }

    // Add final list item
    addListItem(pptx.shapes.RECTANGLE, 'Change Management', 'A dedicated team to manage the cultural and operational transition, ensuring user adoption.');

    // Right Column: Large Placeholder Icon (using CUSTOM_GEOMETRY for a team/group representation)
    // This creates a simple, safe representation of a group icon.
    const iconCenterX = RIGHT_COL_X + RIGHT_COL_W / 2;
    const iconCenterY = CONTENT_START_Y + (MAX_CONTENT_Y - CONTENT_START_Y) / 2;
    const headSize = 0.6;
    const bodySizeW = 0.8;
    const bodySizeH = 0.7;

    // Central figure
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - headSize / 2,
        y: iconCenterY - headSize - 0.2,
        w: headSize,
        h: headSize,
        fill: { color: COLOR_PRIMARY_ACCENT, transparency: 80 }
    });
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: iconCenterX - bodySizeW / 2,
        y: iconCenterY - 0.2,
        w: bodySizeW,
        h: bodySizeH,
        fill: { color: COLOR_PRIMARY_ACCENT, transparency: 80 },
        rectRadius: 0.2
    });

    // Side figures (smaller)
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - headSize / 2 - 0.8,
        y: iconCenterY - headSize / 2,
        w: headSize * 0.8,
        h: headSize * 0.8,
        fill: { color: COLOR_PRIMARY_ACCENT, transparency: 80 }
    });
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: iconCenterX - bodySizeW / 2 - 0.8,
        y: iconCenterY,
        w: bodySizeW * 0.8,
        h: bodySizeH * 0.8,
        fill: { color: COLOR_PRIMARY_ACCENT, transparency: 80 },
        rectRadius: 0.2
    });
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - headSize / 2 + 0.8,
        y: iconCenterY - headSize / 2,
        w: headSize * 0.8,
        h: headSize * 0.8,
        fill: { color: COLOR_PRIMARY_ACCENT, transparency: 80 }
    });
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: iconCenterX - bodySizeW / 2 + 0.8,
        y: iconCenterY,
        w: bodySizeW * 0.8,
        h: bodySizeH * 0.8,
        fill: { color: COLOR_PRIMARY_ACCENT, transparency: 80 },
        rectRadius: 0.2
    });

    return slide;
}

function generateSlide7GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_7_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE CANVAS BOUNDARIES & GENERIC CONSTANTS
    const SAFE_MARGIN = 0.3;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer values
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Color Palette (from HTML analysis)
    const BG_COLOR = '0a192f';
    const ACCENT_COLOR = '64ffda';
    const TEXT_COLOR_PRIMARY = 'ccd6f6';
    const TEXT_COLOR_SECONDARY = 'a8b2d1';
    const TEXT_COLOR_ON_ACCENT = '0a192f';
    const BORDER_COLOR = '1a3a6e'; // Approximated from border-gray-700

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Main Title
    slide.addText("A Strategic Roadmap to Zero Trust", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16, // Ultra-safe title size
        color: ACCENT_COLOR,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.2, // 120px equivalent
        h: 0.04, // 3px equivalent
        fill: { color: ACCENT_COLOR }
    });

    // Gantt Chart Layout Constants
    const GANTT_START_Y = 1.4;
    const PHASE_COL_X = SAFE_MARGIN;
    const PHASE_COL_W = 2.2;
    const TIMELINE_COL_X = PHASE_COL_X + PHASE_COL_W + 0.1; // 2.6
    const TIMELINE_COL_W = SLIDE_WIDTH - TIMELINE_COL_X - SAFE_MARGIN; // 10 - 2.6 - 0.3 = 7.1
    const ROW_HEIGHT = 0.7;

    // Timeline Header
    const timelineLabels = ["3 Months", "6 Months", "9 Months", "12 Months"];
    const timelineLabelWidth = TIMELINE_COL_W / timelineLabels.length; // 7.1 / 4 = 1.775

    timelineLabels.forEach((label, index) => {
        slide.addText(label, {
            x: TIMELINE_COL_X + (index * timelineLabelWidth),
            y: GANTT_START_Y - 0.3,
            w: timelineLabelWidth,
            h: 0.2,
            fontSize: 9,
            color: TEXT_COLOR_SECONDARY,
            align: 'center'
        });
    });

    // Timeline Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: TIMELINE_COL_X,
        y: GANTT_START_Y - 0.05,
        w: TIMELINE_COL_W,
        h: 0,
        line: { color: BORDER_COLOR, width: 1 }
    });

    // Gantt Chart Data
    const phases = [
        {
            title: "Phase 1: Assessment & Planning",
            desc: "Conduct security assessment, define policies, select technologies.",
            barWidth: 1, // Corresponds to 1/4 of timeline width
            barText: "3 Months",
            barOpacity: 80
        },
        {
            title: "Phase 2: Identity & Access",
            desc: "Implement passwordless auth, conditional access, behavioral biometrics.",
            barWidth: 2, // Corresponds to 2/4 of timeline width
            barText: "6 Months",
            barOpacity: 70
        },
        {
            title: "Phase 3: Device Security",
            desc: "Deploy endpoint management, enforce device posture, implement RBI.",
            barWidth: 3, // Corresponds to 3/4 of timeline width
            barText: "9 Months",
            barOpacity: 60
        },
        {
            title: "Phase 4: Network Microsegmentation",
            desc: "Implement SDP, configure security groups, deploy AI anomaly detection.",
            barWidth: 4, // Corresponds to 4/4 of timeline width
            barText: "12 Months",
            barOpacity: 50
        },
        {
            title: "Phase 5: Data Security & Monitoring",
            desc: "Implement DLP, encrypt data, and continuously monitor environment.",
            barWidth: 4, // Corresponds to 4/4 of timeline width
            barText: "Ongoing",
            barOpacity: 40
        }
    ];

    let currentY = GANTT_START_Y;

    // Function to add text safely and prevent overflow
    function addTextSafely(slide, text, options) {
        if ((options.y + options.h) > MAX_CONTENT_Y) {
            console.warn(`Skipping element to prevent overflow: ${text.substring(0, 30)}...`);
            return false;
        }
        slide.addText(text, options);
        return true;
    }

    phases.forEach(phase => {
        // Add Phase Title and Description
        addTextSafely(slide, phase.title, {
            x: PHASE_COL_X,
            y: currentY,
            w: PHASE_COL_W,
            h: 0.25,
            fontSize: 10,
            color: TEXT_COLOR_SECONDARY,
            bold: true
        });
        addTextSafely(slide, phase.desc, {
            x: PHASE_COL_X,
            y: currentY + 0.2,
            w: PHASE_COL_W,
            h: 0.4,
            fontSize: 8,
            color: TEXT_COLOR_PRIMARY
        });

        // Add Gantt Bar (using ROUNDED_RECTANGLE for styling)
        const barW = (timelineLabelWidth * phase.barWidth) - 0.1; // Subtract small margin
        const barH = 0.4;
        const barY = currentY + (ROW_HEIGHT / 2) - (barH / 2) - 0.1;

        if ((barY + barH) <= MAX_CONTENT_Y) {
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: TIMELINE_COL_X,
                y: barY,
                w: barW,
                h: barH,
                fill: { color: ACCENT_COLOR, transparency: 100 - phase.barOpacity },
                rectRadius: 0.1
            });

            // Add Text on Gantt Bar
            slide.addText(phase.barText, {
                x: TIMELINE_COL_X,
                y: barY,
                w: barW,
                h: barH,
                fontSize: 9,
                color: TEXT_COLOR_ON_ACCENT,
                bold: true,
                align: 'center',
                valign: 'middle'
            });
        } else {
            console.warn(`Skipping Gantt bar for "${phase.title}" to prevent overflow.`);
        }

        currentY += ROW_HEIGHT;
    });

    return slide;
}

function generateSlide8GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_8_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE CONSTANTS
    const SAFE_MARGIN = 0.3;
    const MAX_CONTENT_Y = 4.8;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;

    // Color Palette (from HTML analysis)
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda'; // Title, highlights
    const COLOR_TEXT_PRIMARY = 'ccd6f6';   // Main text
    const COLOR_TEXT_SECONDARY = 'a8b2d1'; // Sub-headers, strong text
    const COLOR_BORDER = '1a3a6e';
    const COLOR_TABLE_HEADER_FILL = '1a2b47'; // Approximated from rgba(42, 75, 120, 0.3) on dark bg

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Slide Title
    slide.addText("Investing in a Secure Future", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2,
        h: 0.03,
        fill: { color: COLOR_PRIMARY_ACCENT }
    });

    // --- TWO-COLUMN LAYOUT ---

    // Left Column: Cost Table
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.8;

    const tableRows = [
        // Header Row
        [
            { text: "Category", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL } },
            { text: "Estimated Cost", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL } },
            { text: "Notes", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL } }
        ],
        // Data Rows
        [
            { text: "Software & Cloud Services", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$50,000/year", options: { fontSize: 8, color: COLOR_PRIMARY_ACCENT, bold: true } },
            { text: "Azure AD, Intune, Cloudflare ZTNA, Microsoft Purview", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Hardware", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$5,000", options: { fontSize: 8, color: COLOR_PRIMARY_ACCENT, bold: true } },
            { text: "Minimal, depends on existing infrastructure", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Personnel Costs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$150,000/year", options: { fontSize: 8, color: COLOR_PRIMARY_ACCENT, bold: true } },
            { text: "Security team, cloud experts, and training staff", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Training Costs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$10,000", options: { fontSize: 8, color: COLOR_PRIMARY_ACCENT, bold: true } },
            { text: "Employee and technical training programs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Consulting Fees", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$20,000", options: { fontSize: 8, color: COLOR_PRIMARY_ACCENT, bold: true } },
            { text: "If using external implementation consultants", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        // Footer Row
        [
            { text: "Total Estimated Cost", options: { fontSize: 9, color: COLOR_PRIMARY_ACCENT, bold: true } },
            { text: "$235,000", options: { fontSize: 9, color: COLOR_PRIMARY_ACCENT, bold: true } },
            { text: "", options: { fontSize: 9 } }
        ]
    ];

    slide.addTable(tableRows, {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        colW: [1.5, 1.0, 2.3], // Total = 4.8 (SAFE!)
        border: { type: 'solid', pt: 1, color: COLOR_BORDER },
        autoPage: false, // Ensure it doesn't create new slides
        rowH: 0.4, // Generous row height to prevent text clipping
        valign: 'middle'
    });

    // Right Column: ROI List
    const RIGHT_COL_X = 5.4;
    const RIGHT_COL_W = 3.1;

    // Vertical Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: 5.2, y: CONTENT_START_Y, w: 0, h: 3.5,
        line: { color: COLOR_BORDER, width: 2 }
    });

    // ROI Title
    slide.addText("Return on Investment (ROI)", {
        x: RIGHT_COL_X,
        y: CONTENT_START_Y,
        w: RIGHT_COL_W,
        h: 0.4,
        fontSize: 12,
        color: COLOR_TEXT_SECONDARY,
        bold: true
    });

    const roiItems = [
        { strong: "Reduced Risk of Data Breaches:", text: "Quantify savings from preventing costly breaches." },
        { strong: "Improved Compliance:", text: "Avoid fines and penalties from non-compliance." },
        { strong: "Increased Productivity:", text: "Streamlined access and reduced security-related downtime." },
        { strong: "Enhanced Reputation:", text: "Maintain customer trust and protect brand value." }
    ];

    let currentY = CONTENT_START_Y + 0.5;
    const itemSpacing = 0.8;
    const iconSize = 0.2;
    const iconTextGap = 0.05;

    roiItems.forEach(item => {
        if ((currentY + itemSpacing) > MAX_CONTENT_Y) return; // Overflow prevention check

        // Safe Icon (using a simple shape)
        slide.addShape(pptx.shapes.OVAL, {
            x: RIGHT_COL_X,
            y: currentY + 0.05, // Align with text
            w: iconSize,
            h: iconSize,
            fill: { color: COLOR_PRIMARY_ACCENT }
        });

        // ROI Item Text (using rich text for bolding)
        slide.addText(
            [
                { text: item.strong + " ", options: { bold: true, color: COLOR_TEXT_SECONDARY } },
                { text: item.text, options: { color: COLOR_TEXT_PRIMARY } }
            ],
            {
                x: RIGHT_COL_X + iconSize + iconTextGap,
                y: currentY,
                w: RIGHT_COL_W - (iconSize + iconTextGap),
                h: itemSpacing - 0.1,
                fontSize: 9,
                lineSpacing: 14
            }
        );

        currentY += itemSpacing;
    });

    return slide;
}

// Main presentation creation function
function createPresentation() {
    const pptx = new PptxGenJS();

    console.log('Creating multi-slide presentation with 7 slides...');

    generateSlide2AgendaUltraSafeAutoConvert(pptx);
    generateSlide3GeneralUltraSafeAutoConvert(pptx);
    generateSlide4GeneralUltraSafeAutoConvert(pptx);
    generateSlide5GeneralUltraSafeAutoConvert(pptx);
    generateSlide6GeneralUltraSafeAutoConvert(pptx);
    generateSlide7GeneralUltraSafeAutoConvert(pptx);
    generateSlide8GeneralUltraSafeAutoConvert(pptx);

    console.log(`✅ Created presentation with ${pptx.slides.length} slides`);

    return pptx.writeFile({ fileName: 'my_presentation.pptx' });
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('✅ Multi-slide PowerPoint generated successfully!');
        console.log('📄 File saved as: my_presentation.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('❌ Error generating presentation:', error);
        console.error(error.stack);
        process.exit(1);
    });
