# Slide6 PowerPoint Generation Diagnostic Report

## Executive Summary
**CRITICAL ISSUE IDENTIFIED**: PowerPoint corruption in slide6_general_economics is caused by **incorrect chart data structure** for PptxGenJS line charts.

## 1. HTML Source Analysis (slide6_general_economics.html)

### Content Elements Identified:
- ✅ **Main Title**: "The Evolving Economics of Renewables & Policy Influence"
- ✅ **Chart Title**: "Levelized Cost of Energy (LCOE) Trends (2010-2023)"
- ✅ **SVG Chart**: Complex polyline chart with 5 data series
- ✅ **Right Column Content**: Insights, policy text, styled containers
- ✅ **Legend**: 5 items with colors and line styles
- ✅ **Source Attribution**: Footer text

### Complex HTML Structures:
```html
<!-- SVG Chart with multiple polylines -->
<svg class="absolute top-0 left-0 w-full h-full" preserveAspectRatio="none" viewBox="0 0 100 200">
    <polyline points="0,50 33.3,90 66.6,120 83.3,140 100,160" fill="none" stroke="#facc15" stroke-width="1.5"/>
    <polyline points="0,120 33.3,135 66.6,150 83.3,160 100,165" fill="none" stroke="#3b82f6" stroke-width="1.5"/>
    <!-- ... more polylines -->
</svg>

<!-- CSS Grid Background -->
<div class="chart-grid">
    background-image: linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                     linear-gradient(to bottom, #e5e7eb 1px, transparent 1px);
    background-size: 25% 12.5%;
</div>
```

## 2. Generated JavaScript Analysis

### ✅ Positioning Analysis - WITHIN BOUNDS:
```javascript
const CHART_X = 0.5;        // ✅ Within 0.3-8.5 range
const CHART_Y = 1.4;        // ✅ Within 0.3-4.8 range  
const CHART_W = 5.0;        // ✅ 0.5 + 5.0 = 5.5 < 8.5
const CHART_H = 3.2;        // ✅ 1.4 + 3.2 = 4.6 < 4.8
```

### ❌ CRITICAL ISSUE: Incorrect Chart Data Structure
**Current (INCORRECT) Structure:**
```javascript
const chartData = [
    {
        name: 'Solar PV',
        labels: ['2010', '2013', '2016', '2019', '2023'],  // ❌ INVALID for line charts
        values: [150, 110, 80, 60, 40]
    },
    {
        name: 'Onshore Wind',
        labels: ['2010', '2013', '2016', '2019', '2023'],  // ❌ INVALID for line charts
        values: [80, 65, 50, 40, 35]
    }
    // ... more series with duplicate labels
];
```

**Root Cause**: Each data series contains its own `labels` array, which is **invalid for PptxGenJS line charts**. This causes PowerPoint to report "couldn't read content" and require repair.

### ❌ Additional Issues Identified:

#### 1. Invalid Chart Options:
```javascript
chartArea: { fill: { color: 'FFFFFF', transparency: 100 } },  // ❌ transparency: 100 invalid
plotArea: {
    layout: { x: 0.1, y: 0.1, w: 0.85, h: 0.8 },  // ❌ Invalid plotArea syntax
    border: { pt: 2, color: '9CA3AF' }
},
lineDash: [null, null, null, 'dash', null]  // ❌ Mixing null and 'dash' invalid
```

#### 2. Missing Color Prefixes:
```javascript
chartColors: ['facc15', '3b82f6', '1e3a8a', '1f2937', '9ca3af'],  // ❌ Missing '#' prefix
```

## 3. Comparison with Working slide4_general_moneyflows

### ✅ Working Chart Structure (Pie Chart):
```javascript
const chartData = [
    {
        name: 'Investment Distribution',
        labels: ['Solar PV', 'Wind', 'Hydro', 'Bioenergy', 'Geothermal', 'Energy Storage'],
        values: [45, 35, 10, 5, 3, 2],
    },
];

slide.addChart(pptx.ChartType.pie, chartData, {
    x: LEFT_COL_X + 0.45,
    y: CONTENT_START_Y + 0.4,
    w: 3.5,
    h: 2.0,
    showLegend: true,
    chartColors: ['FEF08A', 'BAE6FD', '3B82F6', 'D2B48C', 'FB923C', '9CA3AF']
});
```

**Key Differences:**
- ✅ **Pie charts**: Can have `labels` in data structure
- ❌ **Line charts**: Should NOT have `labels` in each data series
- ✅ **Simple options**: Working chart uses minimal, valid options
- ❌ **Complex options**: Problematic chart uses invalid advanced options

## 4. Root Cause Analysis

### Why This Happened:
1. **SVG Conversion Complexity**: The ultra_safe.txt prompt v3.0 attempted to convert complex SVG polylines to PptxGenJS charts
2. **Incorrect Chart Format**: The prompt generated line chart data using pie chart format (with labels in each series)
3. **Advanced Options Misuse**: The prompt tried to use advanced chart options that are invalid or unsupported

### Ultra Safe Prompt v3.0 Assessment:
- ✅ **Successfully extracted**: Chart data values from SVG polylines
- ✅ **Successfully extracted**: Chart colors and styling
- ❌ **Incorrectly formatted**: Chart data structure for line charts
- ❌ **Used invalid options**: Advanced chart formatting options

## 5. Specific Recommendations

### IMMEDIATE FIX: Correct Chart Data Structure
```javascript
// ✅ CORRECT format for PptxGenJS line charts
const chartData = [
    {
        name: 'Solar PV',
        values: [150, 110, 80, 60, 40]  // ❌ Remove labels array
    },
    {
        name: 'Onshore Wind',
        values: [80, 65, 50, 40, 35]    // ❌ Remove labels array
    },
    {
        name: 'Offshore Wind',
        values: [180, 140, 110, 90, 70]
    },
    {
        name: 'Coal',
        values: [100, 100, 100, 100, 100]
    },
    {
        name: 'Natural Gas',
        values: [60, 70, 80, 75, 75]
    }
];

// ✅ CORRECT chart options
const chartOptions = {
    x: CHART_X,
    y: CHART_Y,
    w: CHART_W,
    h: CHART_H,
    valAxisTitle: 'LCOE (USD/MWh)',
    valAxisMaxVal: 200,
    valAxisMinVal: 0,
    valAxisMajorUnit: 25,
    showLegend: false,
    lineSize: 2,
    chartColors: ['#facc15', '#3b82f6', '#1e3a8a', '#1f2937', '#9ca3af'],  // ✅ Add # prefix
    // ❌ Remove invalid options: chartArea, plotArea, lineDash
};
```

### PROMPT ENHANCEMENT: Add Chart Type Specific Instructions
The ultra_safe.txt prompt needs a new section:

```
## ULTRA-SAFE CHART HANDLING

**LINE CHART DATA FORMAT:**
const chartData = [
    { name: 'Series1', values: [1, 2, 3, 4, 5] },
    { name: 'Series2', values: [2, 3, 4, 5, 6] }
];
// ❌ NEVER include 'labels' array in line chart data series

**PIE CHART DATA FORMAT:**
const chartData = [
    {
        name: 'Chart Name',
        labels: ['Label1', 'Label2', 'Label3'],
        values: [10, 20, 30]
    }
];
// ✅ Pie charts CAN have 'labels' array

**VALID CHART OPTIONS ONLY:**
- x, y, w, h (positioning)
- valAxisTitle, valAxisMaxVal, valAxisMinVal, valAxisMajorUnit
- showLegend, lineSize, chartColors
- ❌ AVOID: chartArea, plotArea, lineDash, transparency
```

## 6. Testing Plan

### Phase 1: Fix Current JavaScript
1. ✅ Remove `labels` arrays from each data series
2. ✅ Add `#` prefix to chart colors
3. ✅ Remove invalid chart options
4. ✅ Test PowerPoint generation

### Phase 2: Enhance Ultra Safe Prompt
1. ✅ Add chart type specific data format instructions
2. ✅ Add valid chart options whitelist
3. ✅ Add chart corruption prevention guidelines
4. ✅ Test with slide6_general_economics.html

## 7. Expected Outcome

**After implementing fixes:**
- ✅ PowerPoint file will generate without corruption
- ✅ Line chart will display correctly with 5 data series
- ✅ Chart colors and styling will be preserved
- ✅ No "couldn't read content" errors
- ✅ Slide will open normally without requiring repair
