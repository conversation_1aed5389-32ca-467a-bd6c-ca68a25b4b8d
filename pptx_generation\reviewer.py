from llm.llmwrapper import LLM
from PIL import Image
from htmlrender.renderer import HTM<PERSON>enderer
from utils.utils import find_text_in_between_tags
import textwrap
from loguru import logger
from prompt.experiment_prompts import reviewerpy_slide_review_prompt_daniel_v1, reviewerpy_slide_fix_prompt_daniel_v1
from pptx_generation.overflow_checker import check_slide_overflow, check_slide_overflow_async

class Reviewer():

    def reviewer_v0(self, html_code : str, html_image : Image.Image, llm : LLM, custom_prompt : str = None)-> dict:
        slide_review_prompt = reviewerpy_slide_review_prompt_daniel_v1

        if custom_prompt:
            slide_review_prompt = custom_prompt

        #Sanitizing input html
        html_code = find_text_in_between_tags(html_code, start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

        review_prompt = slide_review_prompt.format(code=html_code)
        response = llm.call_with_images(query=review_prompt, images=[html_image])

        if '<OK>' in response['text']:
            return ({"status" : "unchanged", "html_code" : html_code})
        else:
            modified_html = find_text_in_between_tags(response['text'], start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)
            return ({"status" : "modified", "html_code" : modified_html})

    def reviewer_v1(self, html_code : str, html_image : Image.Image, llm : LLM, custom_prompt : str = None)-> dict:

        slide_fix_prompt = reviewerpy_slide_fix_prompt_daniel_v1

        if custom_prompt:
            slide_fix_prompt = custom_prompt

        #Sanitizing input html
        html_code = find_text_in_between_tags(html_code, start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

        # Step 1: Algorithmic review
        round_1_review = check_slide_overflow(html_code)

        if round_1_review['status'] == 'no_overflow':
            output_html = html_code
            logger.info(f"Round 1 review status: {round_1_review['message']}")
            return ({"status" : "unchanged", "html_code" : output_html})
        
        elif round_1_review['status'] == 'resolved_overflow':
            output_html = round_1_review['modified_html']
            logger.info(f"Round 1 review status: {round_1_review['message']}")
            return ({"status" : "modified", "html_code" : output_html})

        elif round_1_review['status'] == 'critical_overflow':
            output_html = round_1_review['modified_html']
            logger.info(f"Round 1 review status: {round_1_review['message']}")
        
        else:
            raise Exception

        review_prompt = slide_fix_prompt.format(code=output_html)
        logger.info(f"Round 2 review...")
        response = llm.call_with_images(query=review_prompt, images=[html_image])
        logger.info(f"Round 2 review complete!")

        final_html = find_text_in_between_tags(response['text'], start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)
        return ({"status" : "modified", "html_code" : final_html})


class Reviewer_Async():

    async def reviewer_v0(self, html_code : str, html_image : Image.Image, llm : LLM, custom_prompt : str = None)-> dict:

        slide_review_prompt = reviewerpy_slide_review_prompt_daniel_v1

        if custom_prompt:
            slide_review_prompt = custom_prompt

        #Sanitizing input html
        html_code = find_text_in_between_tags(html_code, start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

        review_prompt = slide_review_prompt.format(code=html_code)
        response = await llm.call_with_images(query=review_prompt, images=[html_image])

        if '<OK>' in response['text']:
            return ({"status" : "unchanged", "html_code" : html_code})
        else:
            modified_html = find_text_in_between_tags(response['text'], start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)
            return ({"status" : "modified", "html_code" : modified_html})


    async def reviewer_v1(self, html_code : str, html_image : Image.Image, llm : LLM, custom_prompt : str = None)-> dict:
    
        slide_fix_prompt = reviewerpy_slide_fix_prompt_daniel_v1

        if custom_prompt:
            slide_fix_prompt = custom_prompt

        #Sanitizing input html
        html_code = find_text_in_between_tags(html_code, start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

        # Step 1: Algorithmic review
        round_1_review = await check_slide_overflow_async(html_code)

        if round_1_review['status'] == 'no_overflow':
            output_html = html_code
            logger.info(f"Round 1 review status: {round_1_review['message']}")
            return ({"status" : "unchanged", "html_code" : output_html})
        
        elif round_1_review['status'] == 'resolved_overflow':
            output_html = round_1_review['modified_html']
            logger.info(f"Round 1 review status: {round_1_review['message']}")
            return ({"status" : "modified", "html_code" : output_html})

        elif round_1_review['status'] == 'critical_overflow':
            output_html = round_1_review['modified_html']
            logger.info(f"Round 1 review status: {round_1_review['message']}")
        
        else:
            raise Exception

        review_prompt = slide_fix_prompt.format(code=output_html)
        logger.info(f"Round 2 review...")
        response = await llm.call_with_images(query=review_prompt, images=[html_image])
        logger.info(f"Round 2 review complete!")

        final_html = find_text_in_between_tags(response['text'], start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)
        return ({"status" : "modified", "html_code" : final_html})
    

    async def reviewer_v2(self, html_code : str, html_image : Image.Image, llm : LLM, custom_prompt : str = None)-> dict:
    
        slide_fix_prompt = reviewerpy_slide_fix_prompt_daniel_v1

        if custom_prompt:
            slide_fix_prompt = custom_prompt

        #Sanitizing input html
        html_code = find_text_in_between_tags(html_code, start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

        # Step 1: Algorithmic review
        round_1_review = await check_slide_overflow_async(html_code, enable_auto_adjust=False)

        output_html = html_code
        if round_1_review['status'] == 'no_overflow':
            logger.info(f"Round 1 review status: {round_1_review['message']}")
            return ({"status" : "unchanged", "html_code" : output_html})

        elif round_1_review['status'] == 'initial_overflow_detected':
            logger.info(f"Round 1 review status: {round_1_review['message']}")
        
        else:
            raise Exception

        review_prompt = slide_fix_prompt.format(code=output_html)
        logger.info(f"Round 2 review...")
        response = await llm.call_with_images(query=review_prompt, images=[html_image], thinking=-1)
        logger.info(f"Round 2 review complete!")

        final_html = find_text_in_between_tags(response['text'], start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)
        return ({"status" : "modified", "html_code" : final_html})


    async def reviewer_v3(self, html_code : str, html_image : Image.Image, llm : LLM, custom_prompt : str = None, num_rounds=2)-> dict:
    
        slide_fix_prompt = reviewerpy_slide_fix_prompt_daniel_v1

        if custom_prompt:
            slide_fix_prompt = custom_prompt

        round = 1
        while round <= num_rounds:
            #Sanitizing input html
            html_code = find_text_in_between_tags(html_code, start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

            # Step 1: Algorithmic review
            round_1_review = await check_slide_overflow_async(html_code, enable_auto_adjust=False)
            logger.info(f"[Review] Round {round} of {num_rounds}: Stage 1 review status: {round_1_review['message']}")

            if round_1_review['status'] == 'no_overflow':
                return ({"status" : "unchanged", "html_code" : html_code})
            elif round_1_review['status'] != 'initial_overflow_detected':
                raise Exception

            review_prompt = slide_fix_prompt.format(code=html_code)
            logger.info(f"[Review] Round {round} of {num_rounds}: Stage 2 review beginning...")
            response = await llm.call_with_images(query=review_prompt, images=[html_image], thinking=-1)
            html_code = find_text_in_between_tags(response['text'], start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

            logger.info(f"[Review] Round {round} of {num_rounds}: Stage 2 review completed")
            round += 1

        return ({"status" : "modified", "html_code" : html_code})