import os
import logging
from typing import List, Union, Optional, Dict, Any
from PIL import Image 
import asyncio 

from llm.providers.gemini import Gemini_LLM
from llm.providers.vertex_gemini import Vertex_Gemini_LLM
from llm.providers.gemini_html_translator import Gemini_HTML_Translator_LLM
from llm.providers.openai_models import OpenAI_LLM
from llm.providers.deepseek import DeepSeek_LLM
from llm.providers.kimik2 import Kimik2_LLM
from llm.providers.claude import Anthropic_LLM

# Conditional import for Vertex AI (may not be available in all environments)
try:
    from llm.providers.vertex_html_translator import Vertex_HTML_Translator_LLM
    VERTEX_AVAILABLE = True
except ImportError:
    VERTEX_AVAILABLE = False
    Vertex_HTML_Translator_LLM = None

from dotenv import load_dotenv

logger = logging.getLogger(__name__) 

logging.basicConfig(level=logging.INFO)

env_paths = ['local.env', '../local.env', '../../local.env']
for env_path in env_paths:
    if os.path.exists(env_path):
        load_dotenv(env_path)
        logger.info(f"Loaded environment variables from {env_path}")
        break
else:
    logger.warning("No local.env file found in common paths. Ensure API keys are set as environment variables.")

class LLM:
    def __init__(self, provider: str, model: Optional[str] = None, temperature: float = 0,
                 site_url: Optional[str] = None, site_name: Optional[str] = None, 
                 **provider_config: Any): # <--- ADDED THIS FOR FLEXIBILITY
        """
        Initializes an LLM client based on the specified provider.

        Args:
            provider (str): The LLM provider (e.g., "gemini", "gemini_html_translator", "vertex_html_translator", "openai", "deepseek", "kimik2", "anthropic").
            model (Optional[str]): The specific model to use. If None, the provider's
                                    default model will be used.
            temperature (float): The sampling temperature for the model.
            site_url (Optional[str]): Optional. Your site URL for OpenRouter rankings (for OpenAI/DeepSeek/Kimik2 via OpenRouter).
            site_name (Optional[str]): Optional. Your site title for OpenRouter rankings (for OpenAI/DeepSeek/Kimik2 via OpenRouter).
            **provider_config (Any): Arbitrary keyword arguments that will be passed directly
                                     to the constructor of the specific LLM provider class.
                                     Use this for provider-specific parameters like 'max_tokens' for Anthropic,
                                     'base_url' for OpenAI_LLM if not handled internally, etc.
        """
        # Handle API key mapping for specialized providers
        if provider == "gemini":
            # Use the same API key as regular Gemini
            api_key_env_var = "GEMINI_API_KEY"
        if provider == "gemini_html_translator":
            # Use the same API key as regular Gemini
            api_key_env_var = "GEMINI_API_KEY"
        elif provider == "vertex_html_translator":
            # Vertex AI uses service account authentication, no API key needed
            api_key_env_var = None
        elif provider == "vertex_gemini":
            # Vertex AI Gemini provider - no API key needed
            api_key_env_var = None
        else:
            api_key_env_var = f"{provider.upper()}_API_KEY"

        # Handle API key retrieval
        if api_key_env_var is not None:
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise Exception(f"{api_key_env_var} not found in environment variables.")
        else:
            api_key = None  # For providers that don't need API keys (like Vertex AI)

        self.provider = provider.lower() 

        # Prepare common arguments
        common_args = {
            "model": model,
            "temperature": temperature,
        }

        # Add location for Vertex AI providers (Gemini models support global location)
        if self.provider in ["vertex_gemini", "vertex_html_translator"]:
            common_args["location"] = "global"  # Best performance and availability

        # Add API key only if needed
        if api_key is not None:
            common_args["api_key"] = api_key

        # Add OpenRouter-specific arguments if they apply and are provided
        if self.provider in ["openai", "deepseek", "kimik2"]:
            if site_url is not None:
                common_args["site_url"] = site_url
            if site_name is not None:
                common_args["site_name"] = site_name
        
        # Merge common arguments with provider_config, with provider_config taking precedence
        final_config = {**common_args, **provider_config}

        if self.provider == "gemini":
            self.llm = Gemini_LLM(**final_config)
        elif self.provider == "vertex_gemini":
            self.llm = Vertex_Gemini_LLM(**final_config)
        elif self.provider == "gemini_html_translator":
            self.llm = Gemini_HTML_Translator_LLM(**final_config)
        elif self.provider == "vertex_html_translator":
            if not VERTEX_AVAILABLE:
                raise Exception("Vertex AI is not available. Please install google-cloud-aiplatform or use 'gemini_html_translator' instead.")
            self.llm = Vertex_HTML_Translator_LLM(**final_config)
        elif self.provider == "openai":
            self.llm = OpenAI_LLM(**final_config)
        elif self.provider == "deepseek":
            self.llm = DeepSeek_LLM(**final_config)
        elif self.provider == "kimik2":
            self.llm = Kimik2_LLM(**final_config)
        elif self.provider == "anthropic":
            # Anthropic models explicitly need max_tokens, if not provided via provider_config,
            # Anthropic_LLM's default will be used.
            self.llm = Anthropic_LLM(**final_config)
        else:
            raise Exception(f"LLM provider '{provider}' is not supported.")
        
        logger.info("LLM initialized successfully.")
        logger.info(f"Provider: {self.provider}")
        logger.info(f"Model: {getattr(self.llm, 'model', 'N/A')}") 
        logger.info(f"Temperature: {getattr(self.llm, 'temperature', 'N/A')}")
        # Optionally log other key config from final_config if desired
        
    async def call(self, query: str, **kwargs: Any) -> Dict[str, Any]:
        """
        Makes an asynchronous text generation call to the underlying LLM.

        Args:
            query (str): The user's prompt.
            **kwargs (Any): Additional keyword arguments to pass to the
                            underlying LLM's 'call' method.

        Returns:
            Dict[str, Any]: The model's response, typically {'text': <generated_text>}.
        """
        return await self.llm.call(query, **kwargs)
    
    async def call_with_images(self, query: str, images: Union[List[Image.Image], Image.Image], **kwargs: Any) -> Dict[str, Any]:
        """
        Makes an asynchronous multimodal generation call (text + images) to the underlying LLM.

        Args:
            query (str): The user's text prompt.
            images (Union[List[PIL.Image.Image], PIL.Image.Image]): One or more PIL Image objects.
            **kwargs (Any): Additional keyword arguments to pass to the
                            underlying LLM's 'call_with_images' method.

        Returns:
            Dict[str, Any]: The model's response, typically {'text': <generated_text>}.
        """
        if not hasattr(self.llm, 'call_with_images'):
            raise NotImplementedError(f"Provider '{self.provider}' does not support 'call_with_images'.")
            
        return await self.llm.call_with_images(query, images, **kwargs)