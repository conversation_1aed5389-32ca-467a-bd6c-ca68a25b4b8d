
// Consolidated Multi-Slide PowerPoint Generator
// Generated by JavaScript-based merging approach (production_v2)

const PptxGenJS = require('pptxgenjs');


function generateSlide2AgendaUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_2_agenda_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & STYLE CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const CONTENT_WIDTH = 10.0 - (2 * SAFE_MARGIN); // 9.4, but we'll use a safer 8.2
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const DIVIDER_Y = 0.8;
    const DIVIDER_H = 0.03; // 3px height is very thin, using a safe value
    const DIVIDER_W = 1.2; // 120px is ~1.2 inches
    const AGENDA_START_Y = 1.2;
    const ICON_TEXT_MARGIN = 0.25;
    const ICON_SIZE = 0.2;
    const ITEM_SPACING = 0.55; // Vertical space between each agenda item

    // ULTRA-SAFE COLOR PALETTE
    const BG_COLOR = '0a192f';
    const PRIMARY_ACCENT_COLOR = '64ffda';
    const PRIMARY_TEXT_COLOR = 'ccd6f6';
    const BOLD_TEXT_COLOR = 'a8b2d1';

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Add Title - ULTRA-SAFE
    slide.addText("Fortifying Our Defenses: A Zero Trust Approach", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16, // Ultra-safe max title size
        color: PRIMARY_ACCENT_COLOR,
        bold: true,
        valign: 'top'
    });

    // Add Title Divider - ULTRA-SAFE
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: DIVIDER_Y,
        w: DIVIDER_W,
        h: DIVIDER_H,
        fill: { color: PRIMARY_ACCENT_COLOR }
    });

    // Agenda Content - structured for safety
    const agendaItems = [
        {
            title: "The Challenge:",
            text: "Current security relies on outdated \"trust but verify\" models, leaving us vulnerable to sophisticated attacks and insider threats."
        },
        {
            title: "The Solution:",
            text: "Implement a comprehensive Zero Trust security framework, assuming no user or device is inherently trustworthy."
        },
        {
            title: "Key Components:",
            text: "Strong Identity & Access Management, Device Security, Network Microsegmentation, and Data Protection."
        },
        {
            title: "Cloud-First Strategy:",
            text: "Leverage cloud services for scalability, cost-effectiveness, and advanced security features."
        },
        {
            title: "Benefits:",
            text: "Reduced risk of breaches, improved compliance, enhanced data protection, and increased operational efficiency."
        },
        {
            title: "Call to Action:",
            text: "Invest in a phased Zero Trust implementation to secure our sensitive data and prevent cyberattacks."
        },
    ];

    // Function to dynamically calculate font size based on content density
    function getUltraSafeFontSize(elementCount, baseSize) {
        let size = baseSize;
        if (elementCount > 15) size = 8;
        else if (elementCount > 12) size = 9;
        else if (elementCount > 8) size = 10;
        else if (elementCount > 5) size = 11;
        return Math.max(size, 8); // Absolute minimum
    }

    // Function to add text safely and prevent overflow
    function addTextSafely(slide, text, options) {
        const wouldOverflow = (options.y + options.h) > MAX_CONTENT_Y;
        if (wouldOverflow) {
            console.warn(`Skipping element to prevent vertical overflow: ${text.substring(0, 30)}...`);
            return false;
        }
        slide.addText(text, options);
        return true;
    }

    let currentY = AGENDA_START_Y;
    const contentFontSize = getUltraSafeFontSize(agendaItems.length, 10);
    const iconX = SAFE_MARGIN;
    const textX = iconX + ICON_TEXT_MARGIN;
    const textW = 8.2 - ICON_TEXT_MARGIN; // Ensure text box does not exceed safe area

    agendaItems.forEach(item => {
        // Check if the entire block will fit before adding any part of it
        if ((currentY + ITEM_SPACING) > MAX_CONTENT_Y) {
            console.warn("Skipping agenda item to prevent overflow.");
            return;
        }

        // Add Icon (using a safe text character)
        addTextSafely(slide, "✓", {
            x: iconX,
            y: currentY,
            w: ICON_SIZE,
            h: ICON_SIZE,
            fontSize: 12,
            color: PRIMARY_ACCENT_COLOR,
            bold: true,
            valign: 'top'
        });

        // Add Rich Text (Bold Title + Regular Text)
        addTextSafely(slide,
            [
                { text: item.title + " ", options: { fontSize: contentFontSize, bold: true, color: BOLD_TEXT_COLOR } },
                { text: item.text, options: { fontSize: contentFontSize, bold: false, color: PRIMARY_TEXT_COLOR } }
            ],
            {
                x: textX,
                y: currentY,
                w: textW,
                h: ITEM_SPACING - 0.1, // Height for the text block
                lineSpacing: contentFontSize + 4 // Dynamic line spacing for readability
            }
        );

        currentY += ITEM_SPACING;
    });

    return slide;
}

function generateSlide3GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_3_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS (GENERIC)
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we use smaller cols

    // Two-column layout constants
    const LEFT_COL_X = 0.5;
    const LEFT_COL_W = 3.8;
    const RIGHT_COL_X = 4.7;
    const RIGHT_COL_W = 4.5;

    // ULTRA-SAFE FONT SIZES
    const TITLE_FONT_SIZE = 16;
    const LABEL_FONT_SIZE = 11;
    const CONTENT_FONT_SIZE = 9;
    const CONTENT_BOLD_FONT_SIZE = 10;

    // ULTRA-SAFE COLOR PALETTE
    const BG_COLOR = '0a192f';
    const PRIMARY_COLOR = '64ffda';
    const TEXT_COLOR = 'ccd6f6';
    const TEXT_HIGHLIGHT_COLOR = 'a8b2d1';
    const BORDER_COLOR = '1a3a6e';

    // Set slide background
    slide.background = { color: BG_COLOR };

    // 1. Slide Title
    slide.addText("The Weaknesses of Our Current Security Posture", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: CONTENT_WIDTH,
        h: 0.5,
        fontSize: TITLE_FONT_SIZE,
        color: PRIMARY_COLOR,
        bold: true,
        align: 'left'
    });

    // 2. Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.5, // 120px is ~1.5 inches
        h: 0,
        line: { color: PRIMARY_COLOR, width: 2 }
    });

    // 3. Left Column: Visual Representation
    // Using CUSTOM_GEOMETRY for the castle icon as it's a valid shape type.
    // This is a simplified representation to avoid image dependencies.
    const castlePoints = [
        { x: 0.0, y: 1.0 }, { x: 0.0, y: 0.4 }, { x: 0.1, y: 0.4 },
        { x: 0.1, y: 0.2 }, { x: 0.3, y: 0.2 }, { x: 0.3, y: 0.4 },
        { x: 0.4, y: 0.4 }, { x: 0.4, y: 0.0 }, { x: 0.6, y: 0.0 },
        { x: 0.6, y: 0.4 }, { x: 0.7, y: 0.4 }, { x: 0.7, y: 0.2 },
        { x: 0.9, y: 0.2 }, { x: 0.9, y: 0.4 }, { x: 1.0, y: 0.4 },
        { x: 1.0, y: 1.0 }, { x: 0.0, y: 1.0 }
    ];
    slide.addShape(pptx.shapes.CUSTOM_GEOMETRY, {
        x: LEFT_COL_X + 0.65, y: CONTENT_START_Y + 0.5, w: 2.5, h: 2.5,
        fill: { color: PRIMARY_COLOR },
        points: castlePoints
    });

    // Unlocked icon (simplified as a circle with a line)
    slide.addShape(pptx.shapes.OVAL, {
        x: LEFT_COL_X + 1.4, y: CONTENT_START_Y + 1.5, w: 1.0, h: 1.0,
        fill: { color: TEXT_HIGHLIGHT_COLOR }
    });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X + 1.15, y: CONTENT_START_Y + 1.2, w: 1.5, h: 0.5,
        fill: { color: TEXT_HIGHLIGHT_COLOR }
    });

    // Visual Label
    slide.addText("Perimeter Security", {
        x: LEFT_COL_X,
        y: CONTENT_START_Y + 3.3,
        w: LEFT_COL_W,
        h: 0.4,
        align: 'center',
        fontSize: LABEL_FONT_SIZE,
        color: PRIMARY_COLOR,
        bold: true,
        border: { type: 'solid', pt: 2, color: PRIMARY_COLOR }
    });

    // 4. Right Column: Weakness List
    const weaknesses = [
        { bold: "Increased Sophistication of Cyberattacks:", text: "Attackers are bypassing traditional perimeter defenses." },
        { bold: "Insider Threats:", text: "Malicious or negligent employees can compromise sensitive data." },
        { bold: "Complex IT Environment:", text: "Cloud adoption, remote work, and BYOD create new attack vectors." },
        { bold: "Compliance Requirements:", text: "Regulations like GDPR and CCPA demand stronger data protection measures." },
        { bold: "Lack of Visibility:", text: "Difficult to track user activity and identify suspicious behavior across the network." },
        { bold: "The Cost of Inaction:", text: "Breaches result in significant financial, reputational, and legal liabilities." }
    ];

    let currentY = CONTENT_START_Y;
    const verticalGap = 0.6; // Space between list items
    const iconTextGap = 0.05;
    const iconSize = 0.25;

    weaknesses.forEach(item => {
        // Vertical overflow check
        if ((currentY + verticalGap) > MAX_CONTENT_Y) {
            console.warn("Skipping element to prevent vertical overflow.");
            return;
        }

        // Icon (using a text-based triangle as a safe warning symbol)
        slide.addText("!", {
            x: RIGHT_COL_X,
            y: currentY,
            w: iconSize,
            h: iconSize,
            align: 'center',
            fontFace: 'Arial', // A font where '!' is distinct
            fontSize: 18,
            bold: true,
            color: PRIMARY_COLOR
        });

        // Text content
        slide.addText([
            { text: item.bold + " ", options: { fontSize: CONTENT_BOLD_FONT_SIZE, color: TEXT_HIGHLIGHT_COLOR, bold: true } },
            { text: item.text, options: { fontSize: CONTENT_FONT_SIZE, color: TEXT_COLOR } }
        ], {
            x: RIGHT_COL_X + iconSize + iconTextGap,
            y: currentY,
            w: RIGHT_COL_W - (iconSize + iconTextGap),
            h: verticalGap - 0.1, // Height for the text box
            lineSpacing: 14 // Ultra-safe line spacing
        });

        currentY += verticalGap;
    });

    return slide;
}

function generateSlide4GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_4_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const MAX_CONTENT_Y = 4.8;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;

    // Two-column layout constants
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.5; // 55% of available width, rounded
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.4; // 0.4" gutter
    const RIGHT_COL_W = 3.6; // 45% of available width, rounded

    // Color Palette
    const BG_COLOR = '0a192f';
    const PRIMARY_ACCENT_COLOR = '64ffda';
    const PRIMARY_TEXT_COLOR = 'ccd6f6';
    const SECONDARY_TEXT_COLOR = 'a8b2d1';
    const BORDER_COLOR = '1a3a6e';
    const CARD_FILL_COLOR = '1a2b4a'; // A slightly lighter, safe fill

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Title
    slide.addText("Zero Trust: Never Trust, Always Verify", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16,
        color: PRIMARY_ACCENT_COLOR,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.5, // 120px is approx 1.5 inches
        h: 0,
        line: { color: PRIMARY_ACCENT_COLOR, width: 2 }
    });

    // --- Left Column Content ---
    const principles = [
        { title: "Identity-Centric Security:", text: "Verify every user and device before granting access." },
        { title: "Microsegmentation:", text: "Isolate applications and data to limit the blast radius of a breach." },
        { title: "Continuous Monitoring:", text: "Constantly monitor user activity, device health, and network traffic for suspicious behavior." },
        { title: "Data-Centric Protection:", text: "Protect sensitive data at rest and in transit with encryption and DLP." },
        { title: "Automation & Orchestration:", text: "Automate security tasks and workflows to improve efficiency and reduce response times." }
    ];

    let currentY = CONTENT_START_Y;
    const itemHeight = 0.55; // Calculated height for icon + text block

    principles.forEach(item => {
        if ((currentY + itemHeight) > MAX_CONTENT_Y) return; // Overflow prevention

        // Icon (using a safe, basic shape)
        slide.addShape(pptx.shapes.OVAL, {
            x: LEFT_COL_X,
            y: currentY + 0.05,
            w: 0.2,
            h: 0.2,
            fill: { color: PRIMARY_ACCENT_COLOR }
        });

        // Text content for the principle
        slide.addText([
            { text: item.title, options: { bold: true, color: SECONDARY_TEXT_COLOR } },
            { text: ` ${item.text}`, options: { color: PRIMARY_TEXT_COLOR } }
        ], {
            x: LEFT_COL_X + 0.3,
            y: currentY,
            w: LEFT_COL_W - 0.3,
            h: itemHeight,
            fontSize: 9,
            lineSpacing: 12
        });

        currentY += itemHeight;
    });

    // Conclusion Text (at the bottom of the left column)
    const conclusionY = 4.2;
    const conclusionHeight = 0.5;
    if ((conclusionY + conclusionHeight) <= MAX_CONTENT_Y) {
        // Decorative line
        slide.addShape(pptx.shapes.LINE, {
            x: LEFT_COL_X,
            y: conclusionY,
            w: 0,
            h: conclusionHeight,
            line: { color: PRIMARY_ACCENT_COLOR, width: 2 }
        });

        slide.addText([
            { text: "Why it Works: ", options: { bold: true, color: PRIMARY_ACCENT_COLOR } },
            { text: "Zero Trust eliminates implicit trust, making it significantly harder for attackers to gain access to sensitive data.", options: { color: SECONDARY_TEXT_COLOR } }
        ], {
            x: LEFT_COL_X + 0.2,
            y: conclusionY,
            w: LEFT_COL_W - 0.2,
            h: conclusionHeight,
            fontSize: 9,
            lineSpacing: 12
        });
    }

    // --- Right Column Content ---
    const diagramCards = [
        { title: "Verify Explicitly", text: "Authenticate and authorize based on all available data points." },
        { title: "Least Privilege Access", text: "Limit user access with just-in-time and just-enough-access (JIT/JEA)." },
        { title: "Assume Breach", text: "Minimize blast radius and segment access. Verify all sessions are encrypted." }
    ];

    currentY = CONTENT_START_Y + 0.2; // Start slightly lower for visual balance
    const cardHeight = 1.0;
    const cardGutter = 0.2;

    diagramCards.forEach(card => {
        if ((currentY + cardHeight) > MAX_CONTENT_Y) return; // Overflow prevention

        // Card background shape
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: RIGHT_COL_X,
            y: currentY,
            w: RIGHT_COL_W,
            h: cardHeight,
            fill: { color: CARD_FILL_COLOR },
            line: { color: BORDER_COLOR, width: 1 },
            rectRadius: 0.1
        });

        // Icon (using a safe, text-based symbol)
        slide.addText("✓", {
            x: RIGHT_COL_X + 0.2,
            y: currentY + 0.3,
            w: 0.5,
            h: 0.5,
            fontSize: 24,
            bold: true,
            color: PRIMARY_ACCENT_COLOR,
            align: 'center'
        });

        // Card text
        slide.addText([
            { text: card.title, options: { bold: true, fontSize: 10, color: PRIMARY_TEXT_COLOR, breakLine: true } },
            { text: card.text, options: { fontSize: 9, color: SECONDARY_TEXT_COLOR } }
        ], {
            x: RIGHT_COL_X + 0.8,
            y: currentY + 0.15,
            w: RIGHT_COL_W - 1.0,
            h: cardHeight - 0.3,
            lineSpacing: 12
        });

        currentY += cardHeight + cardGutter;
    });

    return slide;
}

function generateSlide5GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_5_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE DEFAULTS & CONSTANTS
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const TITLE_DIVIDER_Y = 0.8;

    // Colors from HTML
    const BG_COLOR = '0a192f';
    const PRIMARY_COLOR = '64ffda';
    const TEXT_COLOR_LIGHT = 'ccd6f6';
    const TEXT_COLOR_MEDIUM = 'a8b2d1';
    const SHAPE_FILL_COLOR = '2a4365'; // rgba(42, 67, 101, 0.5) -> solid
    const SHAPE_BORDER_COLOR = '1d3b66';

    // Two-column layout constants
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.2;
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.4; // 4.9
    const RIGHT_COL_W = SLIDE_WIDTH - RIGHT_COL_X - SAFE_MARGIN - 0.3; // 4.5

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Slide Title
    slide.addText("Building a Secure Foundation with Cloud Technologies", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: SLIDE_WIDTH - (2 * SAFE_MARGIN),
        h: TITLE_H,
        fontSize: 16,
        color: PRIMARY_COLOR,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: TITLE_DIVIDER_Y,
        w: 1.2,
        h: 0.03,
        fill: { color: PRIMARY_COLOR }
    });

    // --- LEFT COLUMN: Architecture Diagram ---
    const architectureLayers = [
        { text: "Identity: Azure AD Conditional Access" },
        { text: "Application: Web Apps / Microservices" },
        { text: "Data: S3 Buckets / Azure Blob Storage" },
        { text: "Compute: EC2 Instances / Azure VMs" },
        { text: "Network: Azure Virtual Network / NSGs" },
        { text: "Infrastructure: Cloud Provider" }
    ];

    let currentY = CONTENT_START_Y + 0.5; // Start lower for visual centering
    const layerHeight = 0.45;
    const layerSpacing = 0.1;
    const iconSize = 0.2;

    architectureLayers.forEach(layer => {
        // Check for vertical overflow before adding a layer
        if ((currentY + layerHeight) > MAX_CONTENT_Y) return;

        // Layer Box (Rounded Rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: LEFT_COL_X,
            y: currentY,
            w: LEFT_COL_W,
            h: layerHeight,
            fill: { color: SHAPE_FILL_COLOR },
            line: { color: SHAPE_BORDER_COLOR, width: 1 },
            rectRadius: 0.08
        });

        // Layer Text
        slide.addText(layer.text, {
            x: LEFT_COL_X + 0.15,
            y: currentY,
            w: LEFT_COL_W - 0.6, // Leave space for icon
            h: layerHeight,
            fontSize: 9,
            color: TEXT_COLOR_LIGHT,
            valign: 'middle'
        });

        // Lock Icon (using a safe custom shape)
        // A simple "lock" shape: a square base with an arc on top.
        const iconX = LEFT_COL_X + LEFT_COL_W - 0.4;
        const iconY = currentY + (layerHeight - iconSize) / 2;
        // Arc for the lock shackle
        slide.addShape(pptx.shapes.ARC, {
            x: iconX,
            y: iconY,
            w: iconSize,
            h: iconSize,
            line: { color: PRIMARY_COLOR, width: 1.5 },
            angleRange: [180, 360]
        });
        // Rectangle for the lock body
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: iconX,
            y: iconY + (iconSize / 2),
            w: iconSize,
            h: iconSize / 2,
            fill: { color: PRIMARY_COLOR }
        });

        currentY += layerHeight + layerSpacing;
    });

    // --- RIGHT COLUMN: Technology Details ---
    const techItems = [
        { title: "Identity & Access Management (IAM)", details: "Azure AD Conditional Access + Passwordless Auth + Behavioral Biometrics" },
        { title: "Device Security & Endpoint Management", details: "Microsoft Endpoint Manager (Intune) + DPA + Remote Browser Isolation" },
        { title: "Network Security & Microsegmentation", details: "Azure VNets/NSGs + Cloudflare ZTNA + AI Anomaly Detection" },
        { title: "Data Security & Protection", details: "Microsoft Purview + Cloud-Native Key Management (KMS)" },
        { title: "Automation & Orchestration", details: "Azure Logic Apps + Ansible / Terraform (IaC)" }
    ];

    currentY = CONTENT_START_Y + 0.2; // Reset Y for the right column
    const itemHeight = 0.6;
    const itemSpacing = 0.15;

    techItems.forEach(item => {
        // Check for vertical overflow before adding an item
        if ((currentY + itemHeight) > MAX_CONTENT_Y) return;

        // Tech Item Title
        slide.addText(item.title, {
            x: RIGHT_COL_X,
            y: currentY,
            w: RIGHT_COL_W,
            h: 0.2,
            fontSize: 10,
            color: PRIMARY_COLOR,
            bold: true
        });

        // Tech Item Details
        slide.addText(item.details, {
            x: RIGHT_COL_X,
            y: currentY + 0.2,
            w: RIGHT_COL_W,
            h: 0.4,
            fontSize: 9,
            color: TEXT_COLOR_MEDIUM,
            lineSpacing: 12 // Ultra-safe line spacing in points
        });

        currentY += itemHeight + itemSpacing;
    });

    return slide;
}

function generateSlide6GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_6_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// Set the slide layout to 16:9 to match the HTML's aspect ratio.
// ULTRA-SAFE CONSTANTS - NO SLIDE-SPECIFIC NAMING
    const SAFE_MARGIN = 0.3;
    const SLIDE_WIDTH = 10.0;
    const SLIDE_HEIGHT = 5.625;

    // Content Area Boundaries
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer column widths

    // Two-Column Layout Dimensions
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.8; // Increased width for text content
    const RIGHT_COL_X = 5.3;
    const RIGHT_COL_W = 3.2; // Reserved for the large icon shape

    // Colors from HTML
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY = '64ffda';
    const COLOR_TEXT_HEADING = 'a8b2d1';
    const COLOR_TEXT_BODY = 'ccd6f6';
    const COLOR_TEXT_SUBTLE = '8892b0';

    // Set slide background color
    slide.background = { color: COLOR_BACKGROUND };

    // 1. Slide Title
    slide.addText("Assembling the Right Team for Success", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16, // Ultra-safe title size
        color: COLOR_PRIMARY,
        bold: true,
        align: 'left'
    });

    // 2. Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2,
        h: 0.04,
        fill: { color: COLOR_PRIMARY }
    });

    // 3. Content Data
    const teamItems = [
        {
            title: "Dedicated Security Team",
            desc: "Requires experienced security engineers, architects, and analysts to lead the implementation."
        },
        {
            title: "Cloud Expertise",
            desc: "Personnel with deep knowledge of chosen cloud platforms (Azure, AWS, or Google Cloud)."
        },
        {
            title: "IAM Specialists",
            desc: "Experts in identity and access management solutions are crucial for the core of Zero Trust."
        },
        {
            title: "Training & Awareness Programs",
            subItems: [
                "Educate all employees on Zero Trust principles and secure work practices.",
                "Provide in-depth technical training for IT staff on new technologies."
            ]
        },
        {
            title: "Change Management",
            desc: "A dedicated team to manage the cultural and operational transition, ensuring user adoption."
        }
    ];

    // 4. Vertical Position Tracking
    let currentY = CONTENT_START_Y;
    const ITEM_SPACING = 0.15; // Space between major list items
    const LINE_HEIGHT_TITLE = 0.2;
    const LINE_HEIGHT_DESC = 0.18;
    const LINE_HEIGHT_SUB = 0.18;
    const ICON_SIZE = 0.25;
    const ICON_TEXT_GAP = 0.1;

    // 5. Left Column: Team List
    teamItems.forEach(item => {
        const itemHeightEstimate = 0.6; // A safe estimate for each block
        if (currentY + itemHeightEstimate > MAX_CONTENT_Y) {
            console.warn(`Skipping item "${item.title}" to prevent vertical overflow.`);
            return; // Skip this item if it risks overflow
        }

        // Icon (using a safe, basic shape as a placeholder)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: LEFT_COL_X,
            y: currentY + 0.05, // Vertically center icon slightly
            w: ICON_SIZE,
            h: ICON_SIZE,
            fill: { color: COLOR_PRIMARY },
            rectRadius: 0.05
        });

        const textX = LEFT_COL_X + ICON_SIZE + ICON_TEXT_GAP;
        const textW = LEFT_COL_W - (ICON_SIZE + ICON_TEXT_GAP);

        // Item Title
        slide.addText(item.title, {
            x: textX,
            y: currentY,
            w: textW,
            h: LINE_HEIGHT_TITLE,
            fontSize: 10, // Ultra-safe content size
            color: COLOR_TEXT_HEADING,
            bold: true
        });
        currentY += LINE_HEIGHT_TITLE;

        // Item Description or Sub-list
        if (item.desc) {
            slide.addText(item.desc, {
                x: textX,
                y: currentY,
                w: textW,
                h: LINE_HEIGHT_DESC * 2, // Allow for two lines
                fontSize: 9, // Ultra-safe data size
                color: COLOR_TEXT_BODY,
                lineSpacing: 12 // Tighter line spacing
            });
            currentY += (LINE_HEIGHT_DESC * 2) + ITEM_SPACING;
        } else if (item.subItems) {
            item.subItems.forEach(subItem => {
                if (currentY + LINE_HEIGHT_SUB > MAX_CONTENT_Y) return;
                // Use a text-based bullet for maximum safety
                slide.addText("• " + subItem, {
                    x: textX + 0.2, // Indent sub-item
                    y: currentY,
                    w: textW - 0.2,
                    h: LINE_HEIGHT_SUB * 2, // Allow for two lines
                    fontSize: 9,
                    color: COLOR_TEXT_SUBTLE,
                    lineSpacing: 12
                });
                currentY += LINE_HEIGHT_SUB;
            });
            currentY += ITEM_SPACING;
        }
    });

    // 6. Right Column: Large Icon Placeholder
    // Using CUSTOM_GEOMETRY to create a simple "team" icon representation.
    // This is a safe, valid shape that avoids external images.
    const iconPoints = [
        // Head 1
        { x: 0.35, y: 0.20 }, { x: 0.65, y: 0.20 }, { x: 0.65, y: 0.35 }, { x: 0.35, y: 0.35 },
        // Body 1
        { x: 0.25, y: 0.40 }, { x: 0.75, y: 0.40 }, { x: 0.75, y: 0.80 }, { x: 0.25, y: 0.80 },
    ];

    slide.addShape(pptx.shapes.CUSTOM_GEOMETRY, {
        x: RIGHT_COL_X + 0.8, // Center the shape within the right column
        y: 1.8,
        w: 1.5,
        h: 1.5,
        points: iconPoints,
        fill: { color: COLOR_PRIMARY, transparency: 80 } // High transparency as in HTML
    });

    return slide;
}

function generateSlide7GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_7_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & STYLE CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer values

    // Colors from HTML
    const BG_COLOR = '0a192f';
    const PRIMARY_COLOR = '64ffda';
    const TEXT_COLOR_LIGHT = 'ccd6f6';
    const TEXT_COLOR_MEDIUM = 'a8b2d1';
    const TEXT_COLOR_DARK = '0a192f';

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Title
    slide.addText("A Strategic Roadmap to Zero Trust", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16,
        color: PRIMARY_COLOR,
        bold: true,
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2, // 120px is ~1.2 inches
        h: 0.03, // 3px is ~0.03 inches
        fill: { color: PRIMARY_COLOR },
    });

    // Gantt Chart Area Setup
    const GANTT_START_Y = 1.2;
    const PHASE_COL_X = SAFE_MARGIN;
    const PHASE_COL_W = 2.2;
    const TIMELINE_COL_X = PHASE_COL_X + PHASE_COL_W + 0.1; // 2.6
    const TIMELINE_COL_W = 6.0; // Ends at 8.6, safely within bounds
    const QUARTER_W = TIMELINE_COL_W / 4; // 1.5 inches per quarter

    // Timeline Header Text
    const timelineHeaders = ["3 Months", "6 Months", "9 Months", "12 Months"];
    timelineHeaders.forEach((header, index) => {
        slide.addText(header, {
            x: TIMELINE_COL_X + (index * QUARTER_W),
            y: GANTT_START_Y,
            w: QUARTER_W,
            h: 0.2,
            align: 'center',
            fontSize: 9,
            color: TEXT_COLOR_MEDIUM,
        });
    });

    // Timeline Header Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: TIMELINE_COL_X,
        y: GANTT_START_Y + 0.25,
        w: TIMELINE_COL_W,
        h: 0,
        line: { color: '1a3a6e', width: 1 }, // Mapped from border-gray-700
    });

    // Gantt Chart Data
    const phases = [
        {
            title: "Phase 1: Assessment & Planning",
            desc: "Conduct security assessment, define policies, select technologies.",
            duration: 1, // in quarters
            opacity: 'CC' // 80%
        },
        {
            title: "Phase 2: Identity & Access",
            desc: "Implement passwordless auth, conditional access, behavioral biometrics.",
            duration: 2, // in quarters
            opacity: 'B3' // 70%
        },
        {
            title: "Phase 3: Device Security",
            desc: "Deploy endpoint management, enforce device posture, implement RBI.",
            duration: 3, // in quarters
            opacity: '99' // 60%
        },
        {
            title: "Phase 4: Network Microsegmentation",
            desc: "Implement SDP, configure security groups, deploy AI anomaly detection.",
            duration: 4, // in quarters
            opacity: '80' // 50%
        },
        {
            title: "Phase 5: Data Security & Monitoring",
            desc: "Implement DLP, encrypt data, and continuously monitor environment.",
            duration: 4, // in quarters
            opacity: '66', // 40%
            ongoing: true
        }
    ];

    let currentY = GANTT_START_Y + 0.4;
    const ROW_H = 0.8; // Height for each phase row

    phases.forEach(phase => {
        // Check for vertical overflow before adding a new row
        if (currentY + ROW_H > MAX_CONTENT_Y) {
            console.warn(`Skipping phase "${phase.title}" to prevent vertical overflow.`);
            return;
        }

        // Phase Title and Description (Left Column)
        slide.addText(phase.title, {
            x: PHASE_COL_X,
            y: currentY,
            w: PHASE_COL_W,
            h: 0.3,
            fontSize: 10,
            bold: true,
            color: TEXT_COLOR_MEDIUM,
        });
        slide.addText(phase.desc, {
            x: PHASE_COL_X,
            y: currentY + 0.25,
            w: PHASE_COL_W,
            h: 0.4,
            fontSize: 8,
            color: TEXT_COLOR_LIGHT,
        });

        // Timeline Bar (Right Column)
        const barWidth = phase.duration * QUARTER_W;
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: TIMELINE_COL_X,
            y: currentY + 0.1,
            w: barWidth,
            h: 0.4,
            fill: { color: PRIMARY_COLOR, transparency: 100 - parseInt(phase.opacity, 16) * 100 / 255 },
            rectRadius: 0.1
        });

        // Bar Text
        let barText = `${phase.duration * 3} Months`;
        if (phase.ongoing) {
            barText = "Ongoing";
        }
        slide.addText(barText, {
            x: TIMELINE_COL_X + 0.1,
            y: currentY + 0.1,
            w: barWidth - 0.2,
            h: 0.4,
            align: phase.ongoing ? 'left' : 'center',
            fontSize: 9,
            bold: true,
            color: TEXT_COLOR_DARK,
        });

        currentY += ROW_H;
    });

    return slide;
}

function generateSlide8GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_8_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE DEFAULTS & CONFIGURATION
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8; // Absolute vertical limit for any element

    // Two-column layout coordinates
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 5.0; // Table column
    const RIGHT_COL_X = 5.5;
    const RIGHT_COL_W = 3.3; // ROI list column

    // Colors from HTML
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_BORDER = '1a3a6e';
    const COLOR_TABLE_HEADER_FILL = '1a3a6e'; // Adjusted for visibility

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Add slide title
    slide.addText("Investing in a Secure Future", {
        x: LEFT_COL_X,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16, // Ultra-safe title size
        color: COLOR_ACCENT,
        bold: true,
        align: 'left'
    });

    // Add title divider shape
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X,
        y: 0.85,
        w: 1.2,
        h: 0.03,
        fill: { color: COLOR_ACCENT }
    });

    // --- LEFT COLUMN: COST TABLE ---
    const tableRows = [
        // Header row with ultra-safe styling
        [
            { text: "Category", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL, align: 'left' } },
            { text: "Estimated Cost", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL, align: 'left' } },
            { text: "Notes", options: { bold: true, fontSize: 9, color: COLOR_TEXT_SECONDARY, fill: COLOR_TABLE_HEADER_FILL, align: 'left' } }
        ],
        // Data rows with ultra-safe styling
        [
            { text: "Software & Cloud Services", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$50,000/year", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "Azure AD, Intune, Cloudflare ZTNA, Microsoft Purview", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Hardware", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$5,000", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "Minimal, depends on existing infrastructure", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Personnel Costs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$150,000/year", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "Security team, cloud experts, and training staff", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Training Costs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$10,000", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "Employee and technical training programs", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        [
            { text: "Consulting Fees", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } },
            { text: "$20,000", options: { fontSize: 8, color: COLOR_ACCENT, bold: true } },
            { text: "If using external implementation consultants", options: { fontSize: 8, color: COLOR_TEXT_PRIMARY } }
        ],
        // Footer row (as a regular row for simplicity and control)
        [
            { text: "Total Estimated Cost", options: { fontSize: 9, color: COLOR_ACCENT, bold: true } },
            { text: "$235,000", options: { fontSize: 9, color: COLOR_ACCENT, bold: true } },
            { text: "", options: { fontSize: 8 } }
        ]
    ];

    slide.addTable(tableRows, {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        colW: [1.5, 1.0, 2.5], // Total width = 5.0 (SAFE)
        border: { type: 'solid', pt: 1, color: COLOR_BORDER },
        autoPage: false, // Critical for preventing overflow
        rowH: 0.35, // Provides consistent spacing
        valign: 'middle'
    });

    // --- RIGHT COLUMN: ROI LIST ---
    const roiItems = [
        { boldText: "Reduced Risk of Data Breaches:", regularText: "Quantify savings from preventing costly breaches." },
        { boldText: "Improved Compliance:", regularText: "Avoid fines and penalties from non-compliance." },
        { boldText: "Increased Productivity:", regularText: "Streamlined access and reduced security-related downtime." },
        { boldText: "Enhanced Reputation:", regularText: "Maintain customer trust and protect brand value." }
    ];

    // Add vertical divider line
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X - 0.1, y: CONTENT_START_Y, w: 0, h: 3.5,
        line: { color: COLOR_BORDER, width: 2 }
    });

    // Add ROI sub-title
    slide.addText("Return on Investment (ROI)", {
        x: RIGHT_COL_X,
        y: CONTENT_START_Y,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: 12,
        color: COLOR_TEXT_SECONDARY,
        bold: true
    });

    let currentY = CONTENT_START_Y + 0.4; // Start below the ROI title
    const iconSize = 0.2;
    const textX = RIGHT_COL_X + iconSize + 0.1;
    const textW = RIGHT_COL_W - (iconSize + 0.1);
    const itemSpacing = 0.7; // Generous spacing to avoid vertical collision

    roiItems.forEach(item => {
        // Add a safe, text-based icon for each list item
        slide.addText("✓", {
            x: RIGHT_COL_X,
            y: currentY,
            w: iconSize,
            h: iconSize,
            fontSize: 14,
            color: COLOR_ACCENT,
            bold: true,
            align: 'center'
        });

        // Add the composite text (bold + regular)
        slide.addText([
            { text: item.boldText + " ", options: { fontSize: 9, color: COLOR_TEXT_SECONDARY, bold: true } },
            { text: item.regularText, options: { fontSize: 9, color: COLOR_TEXT_PRIMARY } }
        ], {
            x: textX,
            y: currentY,
            w: textW,
            h: 0.6, // Allow space for wrapping
            lineSpacing: 12
        });

        currentY += itemSpacing;
    });

    return slide;
}

// Main presentation creation function
function createPresentation() {
    const pptx = new PptxGenJS();

    console.log('Creating multi-slide presentation with 7 slides...');

    generateSlide2AgendaUltraSafeAutoConvert(pptx);
    generateSlide3GeneralUltraSafeAutoConvert(pptx);
    generateSlide4GeneralUltraSafeAutoConvert(pptx);
    generateSlide5GeneralUltraSafeAutoConvert(pptx);
    generateSlide6GeneralUltraSafeAutoConvert(pptx);
    generateSlide7GeneralUltraSafeAutoConvert(pptx);
    generateSlide8GeneralUltraSafeAutoConvert(pptx);

    console.log(`✅ Created presentation with ${pptx.slides.length} slides`);

    return pptx.writeFile({ fileName: 'presentation_zero_trust.pptx' });
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('✅ Multi-slide PowerPoint generated successfully!');
        console.log('📄 File saved as: presentation_zero_trust.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('❌ Error generating presentation:', error);
        console.error(error.stack);
        process.exit(1);
    });
