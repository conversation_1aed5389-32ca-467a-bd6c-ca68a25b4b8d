const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    pptx.layout = 'LAYOUT_16x9'; // Matches the 1280x720 aspect ratio

    const slide = pptx.addSlide({ masterName: "slide_3_general" });

    // --- Color Palette Extraction ---
    const COLOR_BACKGROUND = '0A192F';
    const COLOR_ACCENT = '64FFDA';
    const COLOR_TEXT_PRIMARY = 'CCD6F6';
    const COLOR_TEXT_SECONDARY = 'A8B2D1';

    // --- Slide Background ---
    slide.background = { color: COLOR_BACKGROUND };

    // --- Content Analysis & Smart Sizing ---
    const titleText = "The Weaknesses of Our Current Security Posture";
    const bulletPoints = [
        { text: "Increased Sophistication of Cyberattacks:", detail: "Attackers are bypassing traditional perimeter defenses." },
        { text: "Insider Threats:", detail: "Malicious or negligent employees can compromise sensitive data." },
        { text: "Complex IT Environment:", detail: "Cloud adoption, remote work, and BYOD create new attack vectors." },
        { text: "Compliance Requirements:", detail: "Regulations like GDPR and CCPA demand stronger data protection measures." },
        { text: "Lack of Visibility:", detail: "Difficult to track user activity and identify suspicious behavior across the network." },
        { text: "The Cost of Inaction:", detail: "Breaches result in significant financial, reputational, and legal liabilities." }
    ];

    // Total elements = Title (1) + Divider (1) + Visuals (3) + Bullets (6) = 11
    const total_elements = 1 + 1 + 3 + bulletPoints.length;

    let title_size = 22;
    let bullet_size = 11;
    let line_spacing = 0.35;

    // Adjust based on content density
    if (total_elements > 8) {
        title_size = 20; // Reduce by 2
        bullet_size = 10; // Reduce by 1
        line_spacing = 0.32; // Slightly tighten spacing
    } else if (total_elements > 6) {
        title_size = 21; // Reduce by 1
        bullet_size = 10; // Reduce by 1
    }

    // Helper function for responsive font sizing based on text length
    function getOptimalFontSize(text, baseSize) {
        let size = baseSize;
        if (text.length > 120) size -= 3;
        else if (text.length > 80) size -= 2;
        return Math.max(size, 9); // Never go below 9px
    }

    // --- Slide Content ---

    // 1. Title
    const finalTitleSize = getOptimalFontSize(titleText, title_size);
    slide.addText(titleText, {
        x: 0.4,
        y: 0.4,
        w: 9.2,
        h: 0.7,
        fontSize: finalTitleSize,
        color: COLOR_ACCENT,
        bold: true,
        align: 'left',
        valign: 'top'
    });

    // 2. Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: 0.4,
        y: 1.1,
        w: 1.5, // 120px on a 1280px wide slide is ~1.17", rounding up for visibility
        h: 0,
        line: { color: COLOR_ACCENT, width: 2 }
    });

    let currentY = 1.5; // Start content below the divider

    // --- Main Layout (2 Columns) ---
    const VISUAL_COL_X = 0.4;
    const VISUAL_COL_W = 3.8;
    const TEXT_COL_X = 4.5;
    const TEXT_COL_W = 5.1;

    // 3. Visual Column
    // Castle Icon (using a large shape as a placeholder for the complex SVG)
    slide.addImage({
        path: 'https://www.svgrepo.com/show/514338/castle.svg',
        x: VISUAL_COL_X + 0.65, // Centered in the column
        y: currentY + 0.2,
        w: 2.5,
        h: 2.5,
        hyperlink: {
            url: 'https://www.svgrepo.com/show/514338/castle.svg',
            tooltip: 'Castle Icon'
        }
    });

    // Unlocked Icon (overlay)
    slide.addImage({
        path: 'https://www.svgrepo.com/show/315900/unlocked-padlock.svg',
        x: VISUAL_COL_X + 1.5, // Positioned in the center of the castle
        y: currentY + 0.9,
        w: 0.8,
        h: 0.8,
        hyperlink: {
            url: 'https://www.svgrepo.com/show/315900/unlocked-padlock.svg',
            tooltip: 'Unlocked Icon'
        }
    });

    // Visual Label
    slide.addText("Perimeter Security", {
        x: VISUAL_COL_X + 0.4,
        y: currentY + 3.0,
        w: 3.0,
        h: 0.5,
        fontSize: 14,
        color: COLOR_ACCENT,
        bold: true,
        align: 'center',
        valign: 'middle',
        border: { type: 'solid', color: COLOR_ACCENT, pt: 1.5 }
    });

    // 4. Text Column (Bullet Points)
    bulletPoints.forEach(bullet => {
        // Add bullet icon
        slide.addImage({
            path: 'https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg',
            x: TEXT_COL_X,
            y: currentY + 0.05, // Align icon slightly with text
            w: 0.25,
            h: 0.25,
            hyperlink: {
                url: 'https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg',
                tooltip: 'Warning Icon'
            }
        });

        // Add bullet text (using rich text for bolding)
        const fullText = `${bullet.text} ${bullet.detail}`;
        const finalBulletSize = getOptimalFontSize(fullText, bullet_size);

        slide.addText([
            { text: bullet.text, options: { fontSize: finalBulletSize, color: COLOR_TEXT_SECONDARY, bold: true } },
            { text: ` ${bullet.detail}`, options: { fontSize: finalBulletSize, color: COLOR_TEXT_PRIMARY } }
        ], {
            x: TEXT_COL_X + 0.4,
            y: currentY,
            w: TEXT_COL_W - 0.4,
            h: 0.6, // Allow height for wrapping
            lineSpacing: finalBulletSize * 1.5 // Proportional line spacing
        });

        // Increment Y position for the next bullet point
        currentY += line_spacing;
    });

    return pptx.writeFile({ fileName: 'scripts/generated/presentations/slide_3_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
