#!/usr/bin/env python3
"""
Test script to verify the migrated Vertex AI HTML Translator provider
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from llm.providers.vertex_html_translator import Vertex_HTML_Translator_LLM

async def test_vertex_html_translator_migration():
    """Test the migrated Vertex AI HTML Translator provider."""
    
    print("🧪 Testing migrated Vertex AI HTML Translator provider...")
    
    try:
        # Initialize the provider
        llm = Vertex_HTML_Translator_LLM(
            model="gemini-2.5-flash",
            temperature=0.1,
            thinking_budget=1000
        )
        
        print(f"✅ Provider initialized successfully")
        print(f"🔧 Model path: {llm._model_path()}")
        print(f"🧠 ThinkingConfig: {'enabled' if llm.thinking_config else 'disabled'}")
        
        # Get model info
        model_info = llm.get_model_info()
        print(f"📋 Model info: {model_info}")
        
        # Test basic text generation
        print("\n📝 Testing basic HTML translation...")
        html_query = """
        Convert this content to HTML slide format:
        
        Title: Welcome to Our Company
        Content: We are a leading technology company focused on innovation and excellence.
        """
        
        response = await llm.call(html_query)
        
        print(f"✅ Response received: {len(response['text'])} characters")
        print(f"📊 Input tokens: {response.get('input_token_count', 'N/A')}")
        print(f"📊 Output tokens: {response.get('output_token_count', 'N/A')}")
        print(f"📄 Response preview: {response['text'][:300]}...")
        
        # Test validation
        print("\n🔍 Testing input validation...")
        try:
            await llm.call("")  # Should raise ValueError
            print("❌ Empty query validation failed")
            return False
        except ValueError as e:
            print(f"✅ Empty query validation passed: {e}")
        
        try:
            await llm.call("x" * 200000)  # Should raise ValueError for too long query
            print("❌ Long query validation failed")
            return False
        except ValueError as e:
            print(f"✅ Long query validation passed: {e}")
        
        print("\n🎉 All migration tests completed successfully!")
        print("🔄 The provider has been successfully migrated from deprecated vertexai.generative_models")
        print("   to the modern google.genai SDK while maintaining full API compatibility!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_vertex_html_translator_migration())
    sys.exit(0 if success else 1)
