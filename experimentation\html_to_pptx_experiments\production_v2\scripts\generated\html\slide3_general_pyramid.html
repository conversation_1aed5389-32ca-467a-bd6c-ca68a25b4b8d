



<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COBOL to AWS Migration: Phased Modernization</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Roboto', sans-serif;
            background-color: #f0f0f0;
        }
        .slide-container {
            width: 1280px;
            height: 720px;
            background-color: #ffffff;
            position: relative;
            overflow: hidden;
        }
        .slide-content {
            width: 100%;
            height: 100%;
            padding: 40px 60px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }
        .slide-title {
            font-size: 2.5em; /* 40px */
            font-weight: 700;
            color: #1a237e; /* Dark Blue */
            margin-bottom: 8px;
        }
        .slide-subtitle {
            font-size: 0.9em; /* 14.4px */
            font-weight: 400;
            color: #5f6368; /* Medium Gray */
            margin-bottom: 16px;
        }
        .takeaway-bar {
            background-color: #e8f0fe; /* Lighter Blue */
            border-left: 4px solid #1a73e8; /* Stronger Blue */
            padding: 10px 16px;
            font-size: 0.85em;
            font-weight: 500;
            color: #1a237e;
            border-radius: 4px;
        }
    </style>
</head>
<body>

<div class="slide-container">
    <div class="slide-content">
        <h1 class="slide-title">Phased Modernization Pyramid</h1>
        <p class="slide-subtitle">From COBOL Monolith to Cloud-Native at Controlled Risk. Width = Scope/Effort; Vertical = Maturity/Time.</p>
        
        <div class="grid grid-cols-12 gap-x-6 flex-grow min-h-0">
            <!-- Left Column: Pyramid Chart -->
            <div class="col-span-7 flex flex-col">
                <div id="pyramid-chart" class="w-full h-full flex-grow"></div>
            </div>

            <!-- Right Column: Insights & Metrics -->
            <div class="col-span-5 flex flex-col justify-between space-y-4">
                <!-- Insight Bullets -->
                <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500 flex-grow">
                    <h3 class="text-lg font-bold text-indigo-900 mb-2">Key Insights</h3>
                    <ul class="space-y-2 text-sm text-gray-700">
                        <li class="flex items-start"><span class="text-blue-600 mr-2 mt-1">&#9656;</span><span><strong>Strangler pattern</strong> ensures zero/near-zero downtime by isolating and replacing capabilities incrementally.</span></li>
                        <li class="flex items-start"><span class="text-blue-600 mr-2 mt-1">&#9656;</span><span><strong>Data-first discipline</strong> (CDC, contract tests) preserves integrity while services peel away.</span></li>
                        <li class="flex items-start"><span class="text-blue-600 mr-2 mt-1">&#9656;</span><span><strong>Blue/green & canary releases</strong> de-risk cutovers; synthetic monitoring validates critical paths.</span></li>
                        <li class="flex items-start"><span class="text-blue-600 mr-2 mt-1">&#9656;</span><span>Reuse what works (EC2/RDS) before reimagining; focus re-architecture on <strong>high-ROI domains</strong>.</span></li>
                        <li class="flex items-start"><span class="text-blue-600 mr-2 mt-1">&#9656;</span><span><strong>Measurable KPIs</strong> (error budgets, lead time, cost/transaction) guide pace and scope.</span></li>
                    </ul>
                </div>

                <!-- Metric Cards -->
                <div class="grid grid-cols-3 gap-3 text-center">
                    <div class="bg-indigo-50 p-2 rounded-md">
                        <p class="text-xs font-semibold text-indigo-800">Time-to-First-Value</p>
                        <p class="text-xl font-bold text-indigo-900">6-12 wks</p>
                    </div>
                    <div class="bg-indigo-50 p-2 rounded-md">
                        <p class="text-xs font-semibold text-indigo-800">Availability Target</p>
                        <p class="text-xl font-bold text-indigo-900">&ge;99.95%</p>
                    </div>
                    <div class="bg-indigo-50 p-2 rounded-md">
                        <p class="text-xs font-semibold text-indigo-800">TCO Reduction</p>
                        <p class="text-xl font-bold text-indigo-900">30-50%</p>
                    </div>
                    <div class="bg-indigo-50 p-2 rounded-md">
                        <p class="text-xs font-semibold text-indigo-800">Test Coverage</p>
                        <p class="text-xl font-bold text-indigo-900">&ge;80%</p>
                    </div>
                    <div class="bg-indigo-50 p-2 rounded-md">
                        <p class="text-xs font-semibold text-indigo-800">Rollback Time</p>
                        <p class="text-xl font-bold text-indigo-900">&lt;15 min</p>
                    </div>
                    <div class="bg-indigo-50 p-2 rounded-md">
                        <p class="text-xs font-semibold text-indigo-800">Cutover Strategy</p>
                        <p class="text-xl font-bold text-indigo-900">Blue/Green</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4 takeaway-bar">
            <strong>Takeaway:</strong> Stabilize on AWS first, then progressively strangle and re-architect until COBOL is safely retired—achieving cloud-native speed, resilience, and lower TCO without business interruption.
        </div>
    </div>
</div>

<script>
    var chartDom = document.getElementById('pyramid-chart');
    var myChart = echarts.init(chartDom);
    var option;

    option = {
        tooltip: {
            trigger: 'item',
            formatter: function (params) {
                return `<b>${params.name}</b><br/>${params.data.description.replace(/\n/g, '<br/>')}`;
            },
            textStyle: {
                fontSize: 12
            },
            backgroundColor: 'rgba(26, 35, 126, 0.9)', // Dark Blue
            borderColor: '#4285f4',
            textStyle: {
                color: '#FFFFFF'
            }
        },
        series: [
            {
                name: 'Modernization Phase',
                type: 'funnel',
                top: 10,
                bottom: 10,
                width: '85%',
                minSize: '10%',
                maxSize: '100%',
                sort: 'descending',
                gap: 2,
                label: {
                    show: true,
                    position: 'inside',
                    formatter: '{b}',
                    color: '#fff',
                    fontSize: 13,
                    fontWeight: 'bold',
                    textShadowColor: 'rgba(0, 0, 0, 0.5)',
                    textShadowBlur: 3
                },
                labelLine: {
                    length: 10,
                    lineStyle: {
                        width: 1,
                        type: 'solid'
                    }
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 1,
                    color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [{
                            offset: 0, color: '#1a73e8' // Stronger Blue
                        }, {
                            offset: 1, color: '#1a237e' // Dark Blue
                        }]
                    }
                },
                emphasis: {
                    label: {
                        fontSize: 16
                    },
                    itemStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [{
                                offset: 0, color: '#4285f4' // Google Blue
                            }, {
                                offset: 1, color: '#1a73e8' // Stronger Blue
                            }]
                        }
                    }
                },
                data: [
                    { 
                        value: 100, 
                        name: '5. Foundation & Assessment',
                        description: 'Establish AWS Landing Zone (Control Tower, VPC, IAM), connectivity, observability (CloudWatch), and CI/CD. Create golden regression test suite; define SLOs/RTO/RPO. Plan data migration via DMS.'
                    },
                    { 
                        value: 80, 
                        name: '4. Replatform & Stabilize',
                        description: 'Lift COBOL runtime to EC2 Auto Scaling. Migrate databases to RDS/Aurora (Multi-AZ) via DMS+CDC. Move files to EFS. Implement blue/green environments for immediate reliability wins.'
                    },
                    { 
                        value: 60, 
                        name: '3. Strangler Refactor',
                        description: 'Front legacy with API Gateway. Carve out business capabilities into microservices (ECS/Lambda) and data domains (Aurora/DynamoDB). Use SQS/EventBridge for decoupling.'
                    },
                    { 
                        value: 40, 
                        name: '2. Re-architect Cloud-Native',
                        description: 'Retire COBOL modules. Adopt serverless-first, SAGA patterns, and pervasive IaC (CDK/Terraform). Enforce SLIs/SLOs and automated chaos/resiliency testing.'
                    },
                    { 
                        value: 20, 
                        name: '1. Optimize & Govern',
                        description: 'Performance/cost tuning (Graviton, Savings Plans). Implement Multi-Region DR. Use FinOps (Cost Explorer) and security (Security Hub) dashboards for continuous verification.'
                    }
                ]
            }
        ]
    };

    option && myChart.setOption(option);
    window.addEventListener('resize', myChart.resize);
</script>

</body>
</html>