You are an expert JavaScript developer specializing in PptxGenJS and COLOR/ICON fidelity.
Convert this HTML to PptxGenJS with maximum color accuracy and icon/shape recreation.

GOALS:
- Preserve exact colors from HTML (hex/rgb/hsl/CSS variables → hex without #)
- Recreate visual elements (icons/emoji/SVG/CSS shapes) using addText/addShape
- Keep positions within safe bounds (x:0.4-9.2, y:0.4-5.0)
- Maintain readable fonts, let visuals carry emphasis

STEP 1: HTML COLOR ANALYSIS (USE THE PROVIDED ANALYSIS DATA IF PRESENT)
If variables {{analysis}} object is present, use its colors.palette_hex, class_styles, and visuals.
Otherwise, extract from:
- Inline styles: color, background(-color)
- <style> blocks (class selectors)
- rgb()/rgba(), hsl()/hsla(), #hex, var(--token)
- SVG fill/stroke, gradients (use dominant solid approximations)

COLOR NORMALIZATION RULES:
- Always strip '#': '#FF69B4' → 'FF69B4'
- rgb/rgba → hex; hsl/hsla → hex
- If var(--token) is used and a value is known, resolve to hex; otherwise fallback to closest palette color
- Use consistent palette across the slide: primary, secondary, accent, background, text

STEP 2: VISUAL ELEMENT RECREATION
- Emojis/icons (like 🏰, ✓, ⚠️) → either text glyphs or shape compositions
- FontAwesome class hints (fa-*) → approximate with icons or shapes
- SVG paths → if complex, approximate via rectangles/ovals/triangles with fill colors
- CSS-drawn shapes (borders, rounded boxes) → use addShape with fill/line colors

PPTXGENJS SHAPE GUARDRAILS (CRITICAL):
- Every slide.addShape(<ShapeName>, { x, y, w, h, ... }) MUST include w and h
- Use valid shape names (RECTANGLE, OVAL, RIGHT_TRIANGLE, LINE, ARC, ROUNDED_RECTANGLE)
- When uncertain, prefer RECTANGLE with explicit w and h

STEP 3: LAYOUT GUARDRAILS
- Title: y≈0.6; content starts y≈1.4; visuals placed left or right within bounds
- Prefer w<=8.6 for text blocks, increment y by 0.3–0.4 per line

OUTPUT REQUIREMENTS:
- DO NOT include imports
- Function signature:
  function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();
    // ...
    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
  }

IMPLEMENTATION HINTS:
- Build a palette object first using analysis data when available
- Recreate key visual (e.g., a pink castle) using shapes with correct fills
- Use addText for bullet glyphs that encode visuals

HTML to convert:
{HTML_CONTENT}

Slide name: {SLIDE_NAME}
Output directory: {OUTPUT_DIRECTORY}

