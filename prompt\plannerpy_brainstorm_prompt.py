import textwrap


#######Daniel's Prompt#######
plannerpy_brainstorm_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:

        "{query}"

        Before starting work on the request, you need to brainstorm.
        From a technical perspective, how could something like this be done? 
        Please use the following pointers to guide your thought process:
        - What is the most cutting-edge way to do this?
        - How can this be done using cloud services?
        """
        )


#######Duy's Prompt#######
plannerpy_brainstorm_prompt_duy_v1 = textwrap.dedent(
    """ You are a professional UI designer, and you have been given the following request:

        "{query}"

        You are tasked to plan out the elements of an HTTPS presentation, including both Design Features and Technical Specifications.

        # One Shot
        Format your output like this:

        "{Example_1}"
        # Chain of thought
        - Once you receive the request, you need to think what does this topic best fit color ?
        for example, with google you think about Blue: #4285F4 Red: #DB4437 Yellow: #F4B400 Green: #0F9D5 and White: #FFFFFF
        - Then you need to think about what are the word's color ? Which one contrast the best with the background color ? You usually make mistakes on this part, so be careful.
        - Then you need to think about what are the background color ? Again will it contrast well with the word's color ? You usually make mistakes on this part, so be careful.
        - Then you need to think about what are the font family ? Which one is the best fit for this topic ?
        ....

        # Constraints:
        
        Please use the following pointers to guide your thought process
        """)
plannerpy_brainstorm_prompt_duy_v2 = textwrap.dedent(
    """You are tasked to choose color for a presentation about "{query}" what will you choose for

    0. A suitable color Pallete
    1. Title, subtitle, text Size, Font & Color 
    2. Background color, Background Image (If applicable) # Use image_tavily to search for this. Choose the appropriate links based on description.
    4. Visualization chart, inforgraphic using chart.js (Simple and clean is better than complicated)
    5. Pictures using unsplash (If applicable) # Use image_tavily to search for this. Choose the appropriate links based on description.
    6. Icons using Font Awesome (Creative use)
    7. Logos using only 1 Wiki public Link. For example: https://upload.wikimedia.org/wikipedia/commons/a/a8/Microsoft_Azure_Logo.svg # Use image_tavily to search for this. Choose the appropriate links based on description.

    Beyond all your mission is to make this BEAUTIFUL. Answer this concise and to the point.
"""
)

#######Hai's Prompt#######
