#!/bin/bash

# Cloud Run Deployment Script for PPTX Planner
# This script deploys the app using the default service account (Option 2)

PROJECT_ID="gen-lang-client-**********"
SERVICE_NAME="pptx-planner-api"
REGION="us-central1"  # Choose region close to your users
IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"

echo "🚀 Deploying PPTX Planner to Cloud Run..."

# Set the project
gcloud config set project $PROJECT_ID

# Build the container image
echo "🔨 Building container image..."
gcloud builds submit --tag $IMAGE_NAME

# Deploy to Cloud Run with environment variables
echo "☁️ Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_NAME \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 1 \
    --timeout 300 \
    --concurrency 80 \
    --max-instances 10 \
    --set-env-vars "GCS_BUCKET_NAME=pptx-planner-storage" \
    --set-env-vars "FIREBASE_PROJECT_ID=$PROJECT_ID" \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=$PROJECT_ID" \
    --set-env-vars "GEMINI_API_KEY=$GEMINI_API_KEY" \
    --set-env-vars "TAVILY_API_KEY=$TAVILY_API_KEY"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

echo "✅ Deployment complete!"
echo "🌐 Service URL: $SERVICE_URL"
echo ""
echo "📋 Next steps:"
echo "1. Update your frontend to use the new API URL"
echo "2. Test the authentication flow"
echo "3. Verify file upload/download functionality"
echo ""
echo "🔧 To update environment variables later:"
echo "gcloud run services update $SERVICE_NAME --region=$REGION --set-env-vars KEY=VALUE"
