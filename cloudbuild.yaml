steps:
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '--no-cache'
      - '-t'
      - >-
        $_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA
      - .
      - '-f'
      - Dockerfile
    id: Build
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - >-
        $_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA
    id: Push
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:slim'
    args:
      - run
      - services
      - update
      - $_SERVICE_NAME
      - '--platform=managed'
      - >-
        --image=$_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA
      - >-
        --labels=managed-by=gcp-cloud-build-deploy-cloud-run,commit-sha=$COMMIT_SHA,gcb-build-id=$BUILD_ID,gcb-trigger-id=$_TRIGGER_ID
      - '--region=$_DEPLOY_REGION'
      - '--set-env-vars=TAVILY_API_KEY1=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,TAVILY_API_KEY=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,GCS_BUCKET_NAME=pptx-planner-storage,FIREBASE_PROJECT_ID=gen-lang-client-**********,GOOGLE_CLOUD_PROJECT=gen-lang-client-**********'
      - '--service-account=<EMAIL>'
      - '--quiet'
    id: Deploy
    entrypoint: gcloud

images:
  - >-
    $_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA
options:
  substitutionOption: ALLOW_LOOSE
  logging: CLOUD_LOGGING_ONLY
substitutions:
  _TRIGGER_ID: 87b6256e-1f73-4759-a555-593752bd8bc7
  _AR_PROJECT_ID: gen-lang-client-**********
  _PLATFORM: managed
  _SERVICE_NAME: pptx-planner
  _DEPLOY_REGION: asia-northeast1
  _AR_HOSTNAME: asia-northeast1-docker.pkg.dev
  _AR_REPOSITORY: cloud-run-source-deploy
tags:
  - gcp-cloud-build-deploy-cloud-run
  - gcp-cloud-build-deploy-cloud-run-managed
  - pptx-planner
