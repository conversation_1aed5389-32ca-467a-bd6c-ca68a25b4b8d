const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = TITLE_Y + TITLE_H + 0.2; // Start content at 1.0

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        titleText: '374151', // text-gray-800
        subtitleText: '4B5563', // text-gray-600
        bodyText: '374151', // text-gray-700
        bodyTextLight: '4B5563', // text-gray-600
        insightTitle: '0E7490', // text-cyan-700
        insightSubTitle: '164E63', // text-cyan-800
        insightBorder: 'A5F3FC', // border-cyan-200
        dividerLine: 'E5E7EB', // border-gray-200
        footerText: '6B7280', // text-gray-500
        iconSun: 'F59E0B', // text-yellow-500
        iconWind: '3B82F6', // text-blue-500
        iconBattery: '4B5563', // text-gray-600
        pie: {
            solar: 'FEF08A',
            wind: 'BAE6FD',
            hydro: '3B82F6',
            bioenergy: 'D2B48C',
            geothermal: 'FB923C',
            storage: '9CA3AF'
        }
    };

    // Font Sizes (Professional & Consistent, translated from HTML)
    const FONT_SIZES = {
        mainTitle: 16, // text-4xl -> 16
        sectionTitle: 11, // text-xl -> 11
        insightTitle: 12, // text-2xl -> 12
        insightSubTitle: 10, // h3 -> 10
        body: 9, // p -> 9
        legend: 9, // 1.1em -> 9
        footer: 8 // text-xs -> 8
    };

    // Layout Calculations
    const CONTENT_W = SLIDE_WIDTH - (2 * SAFE_MARGIN);
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.4;
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.6;
    const RIGHT_COL_W = SLIDE_WIDTH - RIGHT_COL_X - SAFE_MARGIN;

    // =======================================================================
    // 2. SLIDE BACKGROUND & HEADER (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    slide.addText("Investment Trends: Where the Money is Flowing", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: CONTENT_W,
        h: TITLE_H,
        fontSize: FONT_SIZES.mainTitle,
        color: COLORS.titleText,
        bold: true,
        valign: 'middle'
    });

    // =======================================================================
    // 3. LEFT COLUMN: PIE CHART & LEGEND
    // =======================================================================

    // Section Title
    slide.addText("2023 Investment Distribution", {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        h: 0.4,
        fontSize: FONT_SIZES.sectionTitle,
        color: COLORS.subtitleText,
        bold: true,
        align: 'center'
    });

    // Pie Chart Data (extracted from HTML legend)
    const pieChartData = [{
        name: '2023 Investment Distribution',
        labels: ['Solar PV', 'Wind', 'Hydro', 'Bioenergy', 'Geothermal', 'Energy Storage'],
        values: [45, 35, 10, 5, 3, 2]
    }];

    // Pie Chart Options (using proven safe options)
    const pieChartOptions = {
        x: LEFT_COL_X + (LEFT_COL_W - 3.0) / 2, // Centered in left column
        y: CONTENT_START_Y + 0.4,
        w: 3.0,
        h: 3.0,
        chartColors: [
            COLORS.pie.solar,
            COLORS.pie.wind,
            COLORS.pie.hydro,
            COLORS.pie.bioenergy,
            COLORS.pie.geothermal,
            COLORS.pie.storage
        ],
        showLegend: false, // Custom legend will be built
        dataLabelColor: '000000',
        dataLabelFontSize: 9,
        showValue: true,
        dataLabelPosition: 'outEnd',
        dataLabelFormatCode: '#"%"' // Add percentage sign
    };

    // Add the Pie Chart
    slide.addChart(pptx.ChartType.pie, pieChartData, pieChartOptions);

    // Custom Legend (recreating the HTML legend)
    const legendItems = [
        { text: "Solar PV:", value: "45%", color: COLORS.pie.solar },
        { text: "Wind:", value: "35%", color: COLORS.pie.wind },
        { text: "Hydro:", value: "10%", color: COLORS.pie.hydro },
        { text: "Bioenergy:", value: "5%", color: COLORS.pie.bioenergy },
        { text: "Geothermal:", value: "3%", color: COLORS.pie.geothermal },
        { text: "Energy Storage:", value: "2%", color: COLORS.pie.storage }
    ];

    const legendStartY = CONTENT_START_Y + 3.0;
    const legendCol1X = LEFT_COL_X + 0.2;
    const legendCol2X = LEFT_COL_X + LEFT_COL_W / 2 + 0.1;
    const legendItemH = 0.25;

    legendItems.forEach((item, index) => {
        const isLeftCol = index < 3;
        const currentX = isLeftCol ? legendCol1X : legendCol2X;
        const currentY = legendStartY + (index % 3) * legendItemH;

        // Legend color icon
        slide.addShape(pptx.shapes.OVAL, {
            x: currentX,
            y: currentY + 0.07,
            w: 0.15,
            h: 0.15,
            fill: { color: item.color },
            line: { color: 'D1D5DB', width: 0.5 }
        });

        // Legend text
        slide.addText(item.text, {
            x: currentX + 0.2,
            y: currentY,
            w: 1.2,
            h: legendItemH,
            fontSize: FONT_SIZES.legend,
            color: COLORS.bodyText,
            bold: true,
            valign: 'middle'
        });

        // Legend value
        slide.addText(item.value, {
            x: currentX + 1.4,
            y: currentY,
            w: 0.5,
            h: legendItemH,
            fontSize: FONT_SIZES.legend,
            color: COLORS.bodyText,
            bold: true,
            valign: 'middle',
            align: 'right'
        });
    });

    // =======================================================================
    // 4. RIGHT COLUMN: KEY INSIGHTS
    // =======================================================================

    // Vertical Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X - 0.3,
        y: CONTENT_START_Y,
        w: 0,
        h: 4.0,
        line: { color: COLORS.dividerLine, width: 2 }
    });

    // Section Title with bottom border
    slide.addText("Key Investment Insights", {
        x: RIGHT_COL_X,
        y: CONTENT_START_Y,
        w: RIGHT_COL_W,
        h: 0.4,
        fontSize: FONT_SIZES.insightTitle,
        color: COLORS.insightTitle,
        bold: true
    });
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X,
        y: CONTENT_START_Y + 0.35,
        w: RIGHT_COL_W,
        h: 0,
        line: { color: COLORS.insightBorder, width: 2 }
    });

    let currentY = CONTENT_START_Y + 0.6;
    const insightSpacing = 1.1;

    // Insight 1: Solar Dominance
    slide.addShape(pptx.shapes.OVAL, { x: RIGHT_COL_X, y: currentY, w: 0.4, h: 0.4, fill: { color: COLORS.iconSun, transparency: 85 } });
    slide.addText("S", { x: RIGHT_COL_X, y: currentY, w: 0.4, h: 0.4, align: 'center', valign: 'middle', bold: true, color: COLORS.iconSun, fontSize: 14 });
    slide.addText("Solar Dominance", { x: RIGHT_COL_X + 0.5, y: currentY, w: RIGHT_COL_W - 0.5, h: 0.3, fontSize: FONT_SIZES.insightSubTitle, color: COLORS.insightSubTitle, bold: true });
    slide.addText("Solar PV continues to attract the largest share of investment, driven by its declining costs and widespread applicability across scales.", { x: RIGHT_COL_X + 0.5, y: currentY + 0.25, w: RIGHT_COL_W - 0.5, h: 0.6, fontSize: FONT_SIZES.body, color: COLORS.bodyTextLight });
    currentY += insightSpacing;

    // Insight 2: Offshore Wind Growth
    slide.addShape(pptx.shapes.OVAL, { x: RIGHT_COL_X, y: currentY, w: 0.4, h: 0.4, fill: { color: COLORS.iconWind, transparency: 85 } });
    slide.addText("W", { x: RIGHT_COL_X, y: currentY, w: 0.4, h: 0.4, align: 'center', valign: 'middle', bold: true, color: COLORS.iconWind, fontSize: 14 });
    slide.addText("Offshore Wind Growth", { x: RIGHT_COL_X + 0.5, y: currentY, w: RIGHT_COL_W - 0.5, h: 0.3, fontSize: FONT_SIZES.insightSubTitle, color: COLORS.insightSubTitle, bold: true });
    slide.addText("Offshore wind is experiencing rapid growth in capital allocation, with significant investments in large-scale, high-capacity projects.", { x: RIGHT_COL_X + 0.5, y: currentY + 0.25, w: RIGHT_COL_W - 0.5, h: 0.6, fontSize: FONT_SIZES.body, color: COLORS.bodyTextLight });
    currentY += insightSpacing;

    // Insight 3: Emerging Storage Sector
    slide.addShape(pptx.shapes.OVAL, { x: RIGHT_COL_X, y: currentY, w: 0.4, h: 0.4, fill: { color: COLORS.iconBattery, transparency: 85 } });
    slide.addText("B", { x: RIGHT_COL_X, y: currentY, w: 0.4, h: 0.4, align: 'center', valign: 'middle', bold: true, color: COLORS.iconBattery, fontSize: 14 });
    slide.addText("Emerging Storage Sector", { x: RIGHT_COL_X + 0.5, y: currentY, w: RIGHT_COL_W - 0.5, h: 0.3, fontSize: FONT_SIZES.insightSubTitle, color: COLORS.insightSubTitle, bold: true });
    slide.addText("Energy storage is a critical emerging area of investment, essential for enhancing grid stability and enabling higher renewable penetration.", { x: RIGHT_COL_X + 0.5, y: currentY + 0.25, w: RIGHT_COL_W - 0.5, h: 0.6, fontSize: FONT_SIZES.body, color: COLORS.bodyTextLight });

    // =======================================================================
    // 5. FOOTER
    // =======================================================================
    slide.addText("Source: BloombergNEF 2024 Renewable Energy Investment Report", {
        x: RIGHT_COL_X,
        y: SLIDE_HEIGHT - SAFE_MARGIN - 0.2,
        w: RIGHT_COL_W,
        h: 0.2,
        fontSize: FONT_SIZES.footer,
        color: COLORS.footerText,
        align: 'right'
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide4_general_moneyflows.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
