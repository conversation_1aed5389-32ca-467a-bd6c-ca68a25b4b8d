const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const FOOTER_Y = SLIDE_HEIGHT - 0.4;
    const FOOTER_H = 0.4;

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        headerBorder: 'E5E7EB',
        headerText: '374151',
        cardBackground: 'F8F9FA',
        cardBorder: 'DEE2E6',
        cardTitle: '14213D',
        bodyText: '495057',
        serviceText: '6C757D',
        benefitBg: 'E9F5FF',
        benefitText: '005A9E',
        benefitStrongText: '003C6B',
        footerBg: 'F3F4F6',
        footerText: '4B5563',
        accentBlue: '4285F4',
        accentRed: 'DB4437',
        accentYellow: 'F4B400',
        accentGreen: '0F9D58',
    };

    // Font Sizes (Professional & Consistent)
    const FONT_SIZES = {
        mainTitle: 16,
        cardTitle: 12,
        cardBody: 9,
        cardService: 8,
        benefit: 9,
        footer: 8,
    };

    // Layout Calculations (Clean & Efficient)
    const CONTENT_W = SLIDE_WIDTH - (2 * SAFE_MARGIN);
    const CONTENT_H = FOOTER_Y - CONTENT_START_Y;
    const GAP = 0.4;
    const CARD_W = (CONTENT_W - GAP) / 2;
    const CARD_H = (CONTENT_H - GAP) / 2;

    // =======================================================================
    // 2. HELPER FUNCTIONS (Robust Edge Case Handling)
    // =======================================================================

    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            console.warn(`⚠️ Image failed, using fallback: ${imagePath}`);
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' }, line: { color: 'D1D5DB', width: 1 },
                rectRadius: 0.05
            });
            slide.addText(fallbackText, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fontSize: 8, color: '6B7280', align: 'center', valign: 'middle'
            });
        }
    }

    // =======================================================================
    // 3. SLIDE BACKGROUND, HEADER & FOOTER (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    // Header
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: 0, w: SLIDE_WIDTH, h: 0.8,
        fill: { color: COLORS.background },
        line: { color: COLORS.headerBorder, width: 1 }
    });

    addImageWithFallback(slide, 'https://download.logo.wine/logo/Google_Cloud_Platform/Google_Cloud_Platform-Logo.wine.png', {
        x: SAFE_MARGIN, y: 0.2, w: 1.5, h: 0.4
    }, 'GCP Logo');

    slide.addText('GCP: A Comprehensive Suite of Services', {
        x: SAFE_MARGIN + 1.7, y: 0, w: CONTENT_W - 1.7, h: 0.8,
        fontSize: FONT_SIZES.mainTitle, color: COLORS.headerText, bold: true, valign: 'middle'
    });

    // Footer
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: FOOTER_Y, w: SLIDE_WIDTH, h: FOOTER_H,
        fill: { color: COLORS.footerBg }
    });

    slide.addText('Unlocking Innovation with Google Cloud', {
        x: SAFE_MARGIN, y: FOOTER_Y, w: CONTENT_W / 2, h: FOOTER_H,
        fontSize: FONT_SIZES.footer, color: COLORS.footerText, valign: 'middle', bold: true
    });

    slide.addText('Slide 4', {
        x: SLIDE_WIDTH - SAFE_MARGIN - (CONTENT_W / 2), y: FOOTER_Y, w: CONTENT_W / 2, h: FOOTER_H,
        fontSize: FONT_SIZES.footer, color: COLORS.footerText, valign: 'middle', align: 'right', bold: true
    });

    // =======================================================================
    // 4. CONTENT CARDS (Clean Grid Layout)
    // =======================================================================

    const cardData = [
        {
            title: 'Compute',
            icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Compute-Engine.svg',
            iconAlt: 'Compute',
            accentColor: COLORS.accentBlue,
            body: 'Flexible and scalable compute options for running virtual machines, containers, and serverless applications.',
            services: [
                { text: 'Services:', options: { bold: true } },
                { text: ' Compute Engine, Cloud Run, Kubernetes Engine (GKE)' }
            ],
            benefit: [
                { text: 'Benefit:', options: { bold: true, color: COLORS.benefitStrongText } },
                { text: ' Cost-effective, high-performance, and easy to manage.', options: { color: COLORS.benefitText } }
            ]
        },
        {
            title: 'Data Storage & Databases',
            icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Cloud-SQL.svg',
            iconAlt: 'Database',
            accentColor: COLORS.accentRed,
            body: 'Secure and reliable storage solutions for all types of data, from structured databases to unstructured objects.',
            services: [
                { text: 'Services:', options: { bold: true } },
                { text: ' Cloud Storage, Cloud SQL, Cloud Spanner, BigQuery' }
            ],
            benefit: [
                { text: 'Benefit:', options: { bold: true, color: COLORS.benefitStrongText } },
                { text: ' Highly durable, scalable, and optimized for analytics.', options: { color: COLORS.benefitText } }
            ]
        },
        {
            title: 'AI & Machine Learning',
            icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Vertex-AI.svg',
            iconAlt: 'AI',
            accentColor: COLORS.accentYellow,
            body: 'Powerful AI and ML tools for building intelligent applications and gaining insights from data.',
            services: [
                { text: 'Services:', options: { bold: true } },
                { text: ' Vertex AI, Vision API, Natural Language API' }
            ],
            benefit: [
                { text: 'Benefit:', options: { bold: true, color: COLORS.benefitStrongText } },
                { text: ' Accelerate innovation, automate tasks, and improve decision-making.', options: { color: COLORS.benefitText } }
            ]
        },
        {
            title: 'Networking',
            icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Virtual-Private-Cloud.svg',
            iconAlt: 'Networking',
            accentColor: COLORS.accentGreen,
            body: 'Secure and reliable networking infrastructure for connecting your applications and users globally.',
            services: [
                { text: 'Services:', options: { bold: true } },
                { text: ' Virtual Private Cloud (VPC), Cloud Load Balancing, Cloud CDN' }
            ],
            benefit: [
                { text: 'Benefit:', options: { bold: true, color: COLORS.benefitStrongText } },
                { text: ' High performance, low latency, and enhanced security.', options: { color: COLORS.benefitText } }
            ]
        }
    ];

    function createServiceCard(slide, data, x, y) {
        // Card container
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: x, y: y, w: CARD_W, h: CARD_H,
            fill: { color: COLORS.cardBackground },
            line: { color: COLORS.cardBorder, width: 1 }
        });
        // Accent line
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: x, y: y, w: 0.05, h: CARD_H,
            fill: { color: data.accentColor }
        });

        let currentY = y + 0.2;
        const contentX = x + 0.2;
        const contentW = CARD_W - 0.4;

        // Card Header
        addImageWithFallback(slide, data.icon, {
            x: contentX, y: currentY, w: 0.4, h: 0.4
        }, data.iconAlt);
        slide.addText(data.title, {
            x: contentX + 0.5, y: currentY, w: contentW - 0.5, h: 0.4,
            fontSize: FONT_SIZES.cardTitle, color: COLORS.cardTitle, bold: true, valign: 'middle'
        });
        currentY += 0.4;
        slide.addShape(pptx.shapes.LINE, { x: contentX, y: currentY, w: contentW, h: 0, line: { color: COLORS.cardBorder, width: 1 } });
        currentY += 0.2;

        // Card Body
        slide.addText(data.body, {
            x: contentX, y: currentY, w: contentW, h: 0.5,
            fontSize: FONT_SIZES.cardBody, color: COLORS.bodyText,
        });
        currentY += 0.6;

        // Services List
        slide.addText(data.services, {
            x: contentX, y: currentY, w: contentW, h: 0.4,
            fontSize: FONT_SIZES.cardService, color: COLORS.serviceText,
        });

        // Benefit Box (positioned from bottom)
        const benefitH = 0.5;
        const benefitY = y + CARD_H - benefitH - 0.15;
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: contentX, y: benefitY, w: contentW, h: benefitH,
            fill: { color: COLORS.benefitBg }, rectRadius: 0.1
        });
        slide.addText(data.benefit, {
            x: contentX + 0.1, y: benefitY, w: contentW - 0.2, h: benefitH,
            fontSize: FONT_SIZES.benefit, valign: 'middle',
        });
    }

    const cardPositions = [
        { x: SAFE_MARGIN, y: CONTENT_START_Y },
        { x: SAFE_MARGIN + CARD_W + GAP, y: CONTENT_START_Y },
        { x: SAFE_MARGIN, y: CONTENT_START_Y + CARD_H + GAP },
        { x: SAFE_MARGIN + CARD_W + GAP, y: CONTENT_START_Y + CARD_H + GAP },
    ];

    cardData.forEach((data, index) => {
        if (cardPositions[index]) {
            createServiceCard(slide, data, cardPositions[index].x, cardPositions[index].y);
        }
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide4_general_gcp.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
