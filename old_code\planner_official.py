import sys
from loguru import logger
from llm.llmwrapper_official import LLM
from utils.utils import find_text_in_between_tags
import textwrap
from prompt.experiment_prompts import plannerpy_outline_prompt_duy_v1, plannerpy_brainstorm_prompt_duy_v1, plannerpy_slide_content_prompt_duy_v1
from llm.few_shot_template import Example_1_outline, Example_2_outline
class Planner():
    
    def brainstorm(self, query : str, llm : LLM) -> str:
        
        brainstorm_prompt = plannerpy_brainstorm_prompt_duy_v1.format(
            query=query
        )

        logger.info(
            textwrap.dedent(
                f"""PPTX Planning STEP 1: Brainstorming implementation approach based on user query...
                    QUERY: "{query}"
                """
            )
        )

        brainstorm_response = llm.call(query=brainstorm_prompt)['text']

        logger.info("PPTX Planning STEP 1: Brainstorming complete!")

        return {'prompt' : brainstorm_prompt, 'response' : brainstorm_response}


    def outline(self, query : str, llm : LLM) -> str:
        # Extract slide count from query if specified
        slide_count = self._extract_slide_count_from_query(query)

        # Generate dynamic template based on requested slide count
        template_slides = self._generate_slide_template(slide_count)
        example_slides = self._generate_example_slides(slide_count)

        outline_prompt = plannerpy_outline_prompt_duy_v1.format(
                        query=query,
                        template_slides=template_slides,
                        example_slides=example_slides,
                        Example_1 = Example_1_outline,
                        Example_2 = Example_2_outline
                    )

    
        logger.info("PPTX Planning STEP 2: Creating high-level Presentation outline...")

        outline_response = llm.call(query=outline_prompt)['text']

        logger.info("PPTX Planning STEP 2: Outline creation complete!")

        return {'prompt' : outline_prompt, 'response' : outline_response}


    def slide_content(self, query : str, brainstorm_response : str, outline_response : str, llm : LLM) -> str:
        slide_content_prompt = plannerpy_slide_content_prompt_duy_v1.format(query=query, brainstorm_response=brainstorm_response, outline_response=outline_response, Example_1 = Example_1_outline, Example_2 = Example_2_outline)

        logger.info("PPTX Planning STEP 3: Defining the content to appear on each slide...")

        slide_content_response =  llm.call(query=slide_content_prompt)['text']

        logger.info("PPTX Planning STEP 3: Slide content creation complete!")

        return {'prompt' : slide_content_prompt, 'response' : slide_content_response} 


    def extract_flags(self, text: str, startflag : str = '<', endflag : str = '>' ) -> list:
        """
        Get all <Slide 1 START> <Slide 1 END> Flags
        """
        flags = []
        start = 0
        while True:
            start = text.find(startflag, start)
            if start == -1:
                break
            end = text.find(endflag, start)
            if end == -1:
                break

            flags.append(text[start:end+1])
            start = end + 1
        
        if len(flags) % 2 != 0:
            raise Exception(f"Uneven number of separation flags. Start and End flags come in pairs")

        return flags

    def extract_slide_content(self, slide_content_response : str) -> dict:
        logger.info("PPTX Planning STEP 4: Extracting slide content...")

        flags = self.extract_flags(slide_content_response)

        template_master, content = [], {}
        slidenum = 1

        for i in range(0,len(flags),2):

            slide_content = find_text_in_between_tags(slide_content_response, flags[i], flags[i + 1])
            content[f"slide_{slidenum}"] = slide_content

            if slidenum in (1,2) or 'conclusion' in slide_content.lower():
                template_master.append(f"slide_{slidenum}")
            slidenum += 1

        logger.info("PPTX Planning STEP 4: Slide content extracted!")
        return {'slide_content' : content, 'template_slides' : template_master}


    def plan_content(self,  query : str, llm_1 : LLM, llm_2 : LLM = None, llm_3 : LLM = None) -> dict:
        """
        Main planning function
        """

        brainstorm_llm = llm_1
        outline_llm = llm_2 if llm_2 is not None else llm_1
        slide_content_llm = llm_3 if llm_3 is not None else llm_1

        # 1. Brainstorm
        brainstorm_output = self.brainstorm(query=query, llm=brainstorm_llm) 

        # 2. Create high-level slide outline
        outline_output = self.outline(query=query, llm=outline_llm)

        # 3. Create detailed slide content at a slide granularity
        slide_content_output = self.slide_content(query=query, 
                                                    brainstorm_response=brainstorm_output['response'], 
                                                    outline_response=outline_output['response'],
                                                    llm=slide_content_llm
                                                    )
        
        # 4. Extract slide content
        processed_slide_content_output = self.extract_slide_content(slide_content_response=slide_content_output['response'])

        return {'brainstorm' : brainstorm_output,
                'outline' : outline_output,
                'unprocessed_slide_content' : slide_content_output,
                'processed_slide_content' : processed_slide_content_output
                }

    def _extract_slide_count_from_query(self, query: str) -> int:
        """Extract the requested number of slides from the user query"""
        import re

        # Convert to lowercase for easier matching
        query_lower = query.lower()

        # Look for patterns like "1 slide", "3 slides", "single slide", "one slide"
        patterns = [
            r'(\d+)\s+slides?',  # "3 slides", "1 slide"
            r'(one|single)\s+slide',  # "one slide", "single slide"
            r'only\s+(\d+)\s+slides?',  # "only 1 slide"
            r'just\s+(\d+)\s+slides?',  # "just 2 slides"
        ]

        for pattern in patterns:
            match = re.search(pattern, query_lower)
            if match:
                number_str = match.group(1)
                if number_str in ['one', 'single']:
                    return 1
                try:
                    return int(number_str)
                except ValueError:
                    continue

        # Default to 5 slides if no specific count is mentioned
        return 5

    def _generate_slide_template(self, slide_count: int) -> str:
        """Generate the slide template based on requested count"""
        template = ""
        for i in range(1, slide_count + 1):
            template += f"""**Slide {i}**: Slide {i} Title
- Bullet point 1
- Bullet point 2

"""
        return template.strip()

    def _generate_example_slides(self, slide_count: int) -> str:
        """Generate example slides based on requested count"""
        if slide_count == 1:
            return """**Slide 1**: Website Redesign Proposal
- Complete redesign with modern UI
- Jane Doe, March 2025"""

        elif slide_count == 2:
            return """**Slide 1**: Title
- Website Redesign Proposal
- Jane Doe, March 2025

**Slide 2**: Solution & Next Steps
- Modern UI with improved navigation
- Design prototype and user testing"""

        elif slide_count == 3:
            return """**Slide 1**: Title
- Website Redesign Proposal
- Jane Doe, March 2025

**Slide 2**: Challenge & Solution
- Outdated design harming user engagement
- Modern UI with improved navigation

**Slide 3**: Next Steps
- Design prototype, user testing, Q&A"""

        else:
            # Default 4+ slides example
            return """**Slide 1**: Title
- Website Redesign Proposal
- Jane Doe, March 2025

**Slide 2**: Challenge
- Outdated design harming user engagement

**Slide 3**: Solution
- Modern UI with improved navigation

**Slide 4**: Next Steps
- Design prototype, user testing

**Slide 5**: Q&A / Call to Action
- Thank you for your time—questions?"""




