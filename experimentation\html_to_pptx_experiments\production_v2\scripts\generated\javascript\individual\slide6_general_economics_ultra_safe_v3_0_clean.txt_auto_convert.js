const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0; // Higher start for dense content
    const MAX_CONTENT_Y = 5.2; // Allow content to go slightly lower to fit footer

    // Layout Calculations
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN);
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = CONTENT_WIDTH * 0.6 - 0.15; // 60% width with gutter
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.3;
    const RIGHT_COL_W = CONTENT_WIDTH * 0.4 - 0.15; // 40% width with gutter

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        titleText: '2D3748', // text-gray-800
        chartTitleText: '4B5563', // text-gray-600
        chartAxisText: '4B5563', // text-gray-600
        chartAxisLine: '9CA3AF', // border-gray-400
        chartAxisTitle: '374151', // text-gray-700
        rightColTitle: '0E7490', // text-cyan-700
        rightColTitleBorder: 'A5F3FC', // border-cyan-200
        rightColSubTitle: '155E75', // text-cyan-800
        rightColText: '4B5563', // text-gray-600
        policyBoxBg: 'CFFAFE', // bg-cyan-50
        policyBoxBorder: '06B6D4', // border-cyan-500
        policyBoxText: '374151', // text-gray-700
        footerText: '6B7280', // text-gray-500
        legendBorder: 'D1D5DB', // border-gray-300
        chartSolar: 'facc15',
        chartOnshore: '3b82f6',
        chartOffshore: '1e3a8a',
        chartCoal: '1f2937',
        chartGas: '9ca3af'
    };

    // Font Sizes (Professional & Consistent, translated from HTML)
    const FONT_SIZES = {
        mainTitle: 16, // text-4xl -> 16
        chartTitle: 11, // text-xl -> 11
        rightColTitle: 12, // text-2xl -> 12
        rightColSubTitle: 10, // 1.2em -> 10
        bodyText: 9, // 1.1em -> 9
        axisLabel: 8, // text-xs/sm -> 8
        axisTitle: 9,
        legend: 9, // text-sm -> 9
        footer: 8 // text-xs -> 8
    };

    // =======================================================================
    // 2. SLIDE BACKGROUND & HEADER (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    // Main Title
    slide.addText("The Evolving Economics of Renewables & Policy Influence", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: CONTENT_WIDTH,
        h: 0.6,
        fontSize: FONT_SIZES.mainTitle,
        color: COLORS.titleText,
        bold: true,
        valign: 'top'
    });

    // =======================================================================
    // 3. LEFT COLUMN: CHART (Robust Edge Case Handling)
    // =======================================================================

    // Chart Title
    slide.addText("Levelized Cost of Energy (LCOE) Trends (2010-2023)", {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        h: 0.3,
        fontSize: FONT_SIZES.chartTitle,
        color: COLORS.chartTitleText,
        bold: true,
        align: 'center'
    });

    // Chart Data (PROVEN SAFE FORMAT)
    const chartLabels = ["2010", "2013", "2016", "2019", "2023"];
    const chartData = [
        { name: "Solar PV", labels: chartLabels, values: [150, 110, 80, 60, 40] },
        { name: "Onshore Wind", labels: chartLabels, values: [80, 65, 50, 40, 35] },
        { name: "Offshore Wind", labels: chartLabels, values: [180, 140, 110, 90, 70] },
        { name: "Coal", labels: chartLabels, values: [100, 100, 100, 100, 100] },
        { name: "Natural Gas", labels: chartLabels, values: [60, 70, 80, 75, 75] }
    ];

    // Chart Options (PROVEN SAFE OPTIONS - NO CORRUPTION)
    const chartOptions = {
        x: LEFT_COL_X,
        y: CONTENT_START_Y + 0.4,
        w: LEFT_COL_W,
        h: 3.2,
        chartColors: [COLORS.chartSolar, COLORS.chartOnshore, COLORS.chartOffshore, COLORS.chartCoal, COLORS.chartGas],
        lineSize: 2,
        showLegend: false, // Custom legend will be built
        valAxisTitle: 'LCOE (USD/MWh)',
        valAxisTitleFontSize: FONT_SIZES.axisTitle,
        valAxisTitleColor: COLORS.chartAxisTitle,
        valAxisLabelFontSize: FONT_SIZES.axisLabel,
        valAxisMaxVal: 200,
        valAxisMinVal: 0,
        valAxisMajorUnit: 25,
        catAxisLabelFontSize: FONT_SIZES.axisLabel,
        catAxisLabelColor: COLORS.chartAxisText,
        valAxisLabelColor: COLORS.chartAxisText,
        // ❌ CRITICAL: NO lineDash, gridLine, plotArea, or other corruption-causing options
    };

    slide.addChart(pptx.ChartType.line, chartData, chartOptions);

    // =======================================================================
    // 4. RIGHT COLUMN: INSIGHTS & POLICY (Smart Sizing & Layout)
    // =======================================================================

    let currentY = CONTENT_START_Y;

    // Right Column Divider
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X - 0.15,
        y: CONTENT_START_Y,
        w: 0,
        h: 4.0,
        line: { color: 'E5E7EB', width: 2 }
    });

    // Right Column Title
    slide.addText("Cost & Policy Insights", {
        x: RIGHT_COL_X,
        y: currentY,
        w: RIGHT_COL_W,
        h: 0.4,
        fontSize: FONT_SIZES.rightColTitle,
        color: COLORS.rightColTitle,
        bold: true
    });
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X,
        y: currentY + 0.35,
        w: RIGHT_COL_W,
        h: 0,
        line: { color: COLORS.rightColTitleBorder, width: 2 }
    });
    currentY += 0.6;

    // Insight 1: Cost Reduction
    slide.addText("Dramatic Cost Reduction", {
        x: RIGHT_COL_X,
        y: currentY,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: FONT_SIZES.rightColSubTitle,
        color: COLORS.rightColSubTitle,
        bold: true
    });
    currentY += 0.3;
    slide.addText("Solar PV and wind technologies have seen a steep decline in costs, making them highly competitive against traditional fossil fuels, which have remained stable or fluctuated.", {
        x: RIGHT_COL_X,
        y: currentY,
        w: RIGHT_COL_W,
        h: 0.6,
        fontSize: FONT_SIZES.bodyText,
        color: COLORS.rightColText
    });
    currentY += 0.8;

    // Insight 2: Role of Policy
    slide.addText("The Role of Policy", {
        x: RIGHT_COL_X,
        y: currentY,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: FONT_SIZES.rightColSubTitle,
        color: COLORS.rightColSubTitle,
        bold: true
    });
    currentY += 0.3;

    // Policy Box
    const policyBoxH = 0.9;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: RIGHT_COL_X,
        y: currentY,
        w: RIGHT_COL_W,
        h: policyBoxH,
        fill: { color: COLORS.policyBoxBg },
        rectRadius: 0.1
    });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: RIGHT_COL_X,
        y: currentY,
        w: 0.05,
        h: policyBoxH,
        fill: { color: COLORS.policyBoxBorder }
    });
    slide.addText("Government incentives like feed-in tariffs and renewable portfolio standards have been crucial. They create stable markets, reduce investment risks, and are a primary driver for the observed cost reductions and accelerated deployment.", {
        x: RIGHT_COL_X + 0.15,
        y: currentY + 0.05,
        w: RIGHT_COL_W - 0.2,
        h: policyBoxH - 0.1,
        fontSize: FONT_SIZES.bodyText,
        color: COLORS.policyBoxText
    });
    currentY += policyBoxH + 0.3;

    // Custom Legend (Safer than built-in legend)
    const legendItems = [
        { text: 'Solar PV', color: COLORS.chartSolar, type: 'rect' },
        { text: 'Onshore Wind', color: COLORS.chartOnshore, type: 'rect' },
        { text: 'Offshore Wind', color: COLORS.chartOffshore, type: 'rect' },
        { text: 'Coal (Dashed)', color: COLORS.chartCoal, type: 'line' },
        { text: 'Natural Gas', color: COLORS.chartGas, type: 'rect' }
    ];

    legendItems.forEach(item => {
        if (item.type === 'rect') {
            slide.addShape(pptx.shapes.RECTANGLE, {
                x: RIGHT_COL_X,
                y: currentY,
                w: 0.2,
                h: 0.2,
                fill: { color: item.color },
                line: { color: COLORS.legendBorder, width: 1 }
            });
        } else { // Dashed line for Coal
            // Using two small rectangles to simulate a dash, as line dash causes corruption
            slide.addShape(pptx.shapes.RECTANGLE, { x: RIGHT_COL_X, y: currentY + 0.09, w: 0.08, h: 0.02, fill: { color: item.color } });
            slide.addShape(pptx.shapes.RECTANGLE, { x: RIGHT_COL_X + 0.12, y: currentY + 0.09, w: 0.08, h: 0.02,

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
