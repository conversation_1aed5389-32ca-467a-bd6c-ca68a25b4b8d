<!DOCTYPE html><html><head>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&amp;display=swap" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    body, html {
        margin: 0;
        padding: 0;
        font-family: 'Roboto', sans-serif;
        background-color: #f0f0f0;
    }
    .slide-container {
        width: 1280px;
        height: 720px;
        background-color: #ffffff;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
    }
    .slide-content {
        width: 100%;
        height: 100%;
        padding: 40px 60px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
    }
    .slide-title {
        font-size: 2.5em;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 20px;
    }
    .main-grid {
        display: grid;
        grid-template-columns: 5fr 4fr;
        grid-template-rows: auto 1fr;
        gap: 25px;
        width: 100%;
        height: 100%;
    }
    .chart-container {
        grid-column: 1 / 2;
        grid-row: 1 / 3;
        display: flex;
        flex-direction: column;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
    }
    .chart-title {
        font-size: 1.2em;
        font-weight: 500;
        color: #1a237e;
        margin-bottom: 15px;
        text-align: center;
    }
    .metrics-container {
        grid-column: 2 / 3;
        grid-row: 1 / 2;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    .metric-card {
        background-color: #e8f0fe;
        border-left: 4px solid #1a73e8;
        border-radius: 4px;
        padding: 15px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .metric-value {
        font-size: 1.8em;
        font-weight: 700;
        color: #1a237e;
    }
    .metric-label {
        font-size: 0.85em;
        color: #3c4043;
        line-height: 1.3;
    }
    .definitions-container {
        grid-column: 2 / 3;
        grid-row: 2 / 3;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        font-size: 0.75em;
        color: #3c4043;
        line-height: 1.5;
    }
    .definitions-container h3 {
        font-size: 1.1em;
        color: #1a237e;
        margin-top: 0;
        margin-bottom: 10px;
    }
    .definitions-container p {
        margin: 0 0 8px 0;
    }
    .definitions-container strong {
        font-weight: 500;
        color: #1a73e8;
    }
    .bottom-row {
        grid-column: 1 / 3;
        margin-top: 15px;
        padding: 15px;
        background-color: #e8f0fe;
        border-top: 3px solid #1a73e8;
        border-radius: 0 0 8px 8px;
    }
    .takeaway-title {
        font-size: 1.1em;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 8px;
    }
    .takeaway-text {
        font-size: 0.9em;
        color: #3c4043;
        line-height: 1.4;
    }
</style>
</head>
<body>

<div class="slide-container">
    <div class="slide-content">
        <h1 class="slide-title" style="margin-top: 10.8px; margin-bottom: 8px;">Cost &amp; ROI Analysis (5-Year Horizon)</h1>
        <div class="main-grid" style="font-size: 14.9131px;">
            <div class="chart-container" style="font-size: 14.764px; padding: 12px;">
                <h2 class="chart-title" style="margin-top: 8.936px; margin-bottom: 8px; font-size: 17.5394px;">Annual TCO: On-Prem vs. AWS (USD Millions)</h2>
                <canvas id="tcoChart" width="606" height="481" style="display: block; box-sizing: border-box; height: 481px; width: 606px; font-size: 14.6164px;"></canvas>
            </div>

            <div class="metrics-container" style="font-size: 14.764px;">
                <div class="metric-card" style="font-size: 14.6164px; padding: 8px;">
                    <div class="metric-value" style="font-size: 26.0462px;">150%</div>
                    <div class="metric-label" style="line-height: 16.6478px; font-size: 12.2997px;">5-Year ROI</div>
                </div>
                <div class="metric-card" style="font-size: 14.6164px; padding: 8px;">
                    <div class="metric-value" style="font-size: 26.0462px;">$4.0M</div>
                    <div class="metric-label" style="line-height: 16.6478px; font-size: 12.2997px;">Total Savings</div>
                </div>
                <div class="metric-card" style="font-size: 14.6164px; padding: 8px;">
                    <div class="metric-value" style="font-size: 26.0462px;">~38%</div>
                    <div class="metric-label" style="line-height: 16.6478px; font-size: 12.2997px;">Internal Rate of Return (IRR)</div>
                </div>
                <div class="metric-card" style="font-size: 14.6164px; padding: 8px;">
                    <div class="metric-value" style="font-size: 26.0462px;">12-18 mo</div>
                    <div class="metric-label" style="line-height: 16.6478px; font-size: 12.2997px;">Payback Period</div>
                </div>
            </div>

            <div class="definitions-container" style="line-height: 16.9491px; font-size: 11.073px; padding: 8px;">
                <h3 style="line-height: 16.8644px; margin-bottom: 8px; font-size: 12.0584px;">Metric Definitions</h3>
                <p style="line-height: 16.8644px; font-size: 10.9623px;"><strong style="line-height: 16.7801px; font-size: 10.8527px;">ROI:</strong> (Total Benefits - Investment) ÷ Investment</p>
                <p style="line-height: 16.8644px; font-size: 10.9623px;"><strong style="line-height: 16.7801px; font-size: 10.8527px;">TCO:</strong> Sum of all infrastructure, software, labor, and support costs over 5 years.</p>
                <p style="line-height: 16.8644px; font-size: 10.9623px;"><strong style="line-height: 16.7801px; font-size: 10.8527px;">Payback:</strong> Time to cumulative breakeven where savings equal investment.</p>
                <p style="line-height: 16.8644px; font-size: 10.9623px;"><strong style="line-height: 16.7801px; font-size: 10.8527px;">NPV (10%):</strong> Present value of net benefits, discounted at 10%. Result: <strong style="line-height: 16.7801px; font-size: 10.8527px;">+$2.3M</strong></p>
            </div>
            
            <div class="bottom-row" style="margin-top: 8px; font-size: 14.764px; padding: 8px;">
                <h3 class="takeaway-title" style="margin-top: 8.6px; font-size: 16.0779px;">Key Takeaway</h3>
                <p class="takeaway-text" style="line-height: 18.9831px; margin-top: 8.4px; margin-bottom: 8.4px; font-size: 13.1547px;">
                    The modernization investment pays for itself within Year 2, delivering a structurally lower run-rate, higher resilience, and capacity for future growth. Reductions in hardware, facilities, and ops labor drive savings, while cloud-native optimizations ensure long-term efficiency.
                </p>
            </div>
        </div>
    </div>
</div>

<script>
const ctx = document.getElementById('tcoChart').getContext('2d');
const tcoChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
        datasets: [
            // On-Prem Data
            {
                label: 'On-Prem: Hardware',
                data: [0.9, 0.9, 0.9, 0.9, 0.9],
                backgroundColor: '#757575',
                stack: 'On-Prem',
            },
            {
                label: 'On-Prem: Licensing',
                data: [0.6, 0.6, 0.6, 0.6, 0.6],
                backgroundColor: '#9E9E9E',
                stack: 'On-Prem',
            },
            {
                label: 'On-Prem: Facilities & Power',
                data: [0.3, 0.3, 0.3, 0.3, 0.3],
                backgroundColor: '#BDBDBD',
                stack: 'On-Prem',
            },
            {
                label: 'On-Prem: Labor/Ops',
                data: [1.0, 1.0, 1.0, 1.0, 1.0],
                backgroundColor: '#616161',
                stack: 'On-Prem',
            },
            {
                label: 'On-Prem: Support',
                data: [0.2, 0.2, 0.2, 0.2, 0.2],
                backgroundColor: '#E0E0E0',
                stack: 'On-Prem',
            },
            // AWS Data
            {
                label: 'AWS: Migration Amortization',
                data: [1.0, 0, 0, 0, 0],
                backgroundColor: '#d32f2f',
                stack: 'AWS',
            },
            {
                label: 'AWS: Compute & Storage',
                data: [1.1, 1.0, 1.0, 1.0, 1.0],
                backgroundColor: '#1976D2',
                stack: 'AWS',
            },
            {
                label: 'AWS: Managed Services',
                data: [0.5, 0.5, 0.4, 0.4, 0.4],
                backgroundColor: '#42A5F5',
                stack: 'AWS',
            },
            {
                label: 'AWS: Networking',
                data: [0.2, 0.2, 0.2, 0.2, 0.2],
                backgroundColor: '#90CAF9',
                stack: 'AWS',
            },
            {
                label: 'AWS: Support',
                data: [0.2, 0.2, 0.2, 0.2, 0.2],
                backgroundColor: '#BBDEFB',
                stack: 'AWS',
            },
            {
                label: 'AWS: Ops Labor',
                data: [0.2, 0.2, 0.1, 0.1, 0.1],
                backgroundColor: '#64B5F6',
                stack: 'AWS',
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: { display: false },
            legend: {
                display: false, // Hiding legend to save space; colors are distinct enough
            },
            tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                    footer: function(tooltipItems) {
                        let total = 0;
                        for (const item of tooltipItems) {
                            total += item.parsed.y;
                        }
                        return 'Total: $' + total.toFixed(1) + 'M';
                    }
                }
            },
        },
        scales: {
            x: {
                stacked: true,
                grid: { display: false },
                ticks: { font: { size: 12 } }
            },
            y: {
                stacked: true,
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Annual Cost (USD, $M)',
                    font: { size: 14, weight: '500' }
                },
                ticks: {
                    callback: function(value) {
                        return '$' + value + 'M';
                    }
                }
            }
        },
        animation: {
            duration: 0 // No animation
        }
    }
});
</script>


</body></html>