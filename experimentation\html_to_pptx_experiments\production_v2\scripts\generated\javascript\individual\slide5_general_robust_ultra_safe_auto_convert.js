const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 5.2; // Adjusted for footer
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN);

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        title: '003366',
        subtitle: '005A9E',
        text: '374151',
        textLight: '4B5563',
        cardBg: 'F3F4F6',
        cardBorder: 'D1D5DB',
        arrow: '90A4AE',
    };

    // Font Sizes (Professional & Consistent, translated from HTML)
    const FONT_SIZES = {
        mainTitle: 16, // text-4xl -> 16
        sectionTitle: 12, // text-2xl -> 12
        body: 10, // text-lg -> 10
        listItem: 9,
        diagramLabel: 8, // text-sm -> 8
        diagramTitle: 9, // text-md -> 9
    };

    // Layout Calculations
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 3.8; // 40% of content width
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.4;
    const RIGHT_COL_W = CONTENT_WIDTH - LEFT_COL_W - 0.4;

    // =======================================================================
    // 2. HELPER FUNCTIONS (Robust Edge Case Handling)
    // =======================================================================

    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            console.warn(`⚠️ Image failed, using fallback: ${imagePath}`);
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: COLORS.cardBg },
                line: { color: COLORS.cardBorder, width: 1 },
                rectRadius: 0.05
            });
            slide.addText(fallbackText, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fontSize: 8, color: COLORS.textLight,
                align: 'center', valign: 'middle'
            });
        }
    }

    function addArrow(slide, x, y, w) {
        slide.addShape(pptx.shapes.LINE, {
            x: x, y: y, w: w, h: 0,
            line: { color: COLORS.arrow, width: 2, arrowHead: true }
        });
    }

    // =======================================================================
    // 3. SLIDE BACKGROUND & HEADER (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    slide.addText('Robust and Scalable Technology Platform', {
        x: SAFE_MARGIN,
        y: 0.3,
        w: CONTENT_WIDTH,
        h: 0.5,
        fontSize: FONT_SIZES.mainTitle,
        color: COLORS.title,
        bold: true,
        valign: 'middle'
    });

    // =======================================================================
    // 4. LEFT COLUMN CONTENT (Key Components)
    // =======================================================================

    let currentY = CONTENT_START_Y;

    // Cloud Platform Section
    slide.addText('Cloud Platform: Amazon Web Services (AWS)', {
        x: LEFT_COL_X, y: currentY, w: LEFT_COL_W, h: 0.3,
        fontSize: FONT_SIZES.sectionTitle, color: COLORS.subtitle, bold: true
    });
    currentY += 0.35;

    slide.addText('Chosen for its comprehensive suite of services, scalability, and proven track record in handling large-scale data analytics and machine learning workloads.', {
        x: LEFT_COL_X, y: currentY, w: LEFT_COL_W, h: 0.7,
        fontSize: FONT_SIZES.body, color: COLORS.text
    });
    currentY += 0.8;

    // Key Components Section
    slide.addText('Key Components:', {
        x: LEFT_COL_X, y: currentY, w: LEFT_COL_W, h: 0.3,
        fontSize: FONT_SIZES.sectionTitle, color: COLORS.subtitle, bold: true
    });
    currentY += 0.35;

    const components = [
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws-s3.svg', label: 'Data Storage:', value: 'AWS S3' },
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws-ec2.svg', label: 'Compute:', value: 'AWS EC2' },
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Machine-Learning/AWS-Machine-Learning-SageMaker.svg', label: 'Machine Learning:', value: 'AWS SageMaker' },
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/tableau.svg', label: 'Data Visualization:', value: 'Tableau' },
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/copyleft-pirate.svg', label: 'Data Scraping:', value: 'Scrapy Cloud' },
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Machine-Learning/AWS-Machine-Learning-Comprehend.svg', label: 'NLP:', value: 'AWS Comprehend' },
    ];

    const itemSpacing = 0.35;
    components.forEach(item => {
        addImageWithFallback(slide, item.icon, { x: LEFT_COL_X, y: currentY, w: 0.25, h: 0.25 }, 'Icon');
        slide.addText([
            { text: `${item.label} `, options: { bold: true } },
            { text: item.value }
        ], {
            x: LEFT_COL_X + 0.3, y: currentY, w: LEFT_COL_W - 0.3, h: 0.25,
            fontSize: FONT_SIZES.listItem, color: COLORS.text, valign: 'middle'
        });
        currentY += itemSpacing;
    });

    // =======================================================================
    // 5. RIGHT COLUMN CONTENT (Architecture Diagram)
    // =======================================================================

    // Data Sources (Top Row)
    const sources = [
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/data-modelling.svg', label: 'Industry Reports' },
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/relational-databases.svg', label: 'Government Databases' },
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/discoverable-content.svg', label: 'News Feeds' },
        { icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/twitter.29b6fbd9.svg', label: 'Social Media' },
    ];
    const sourceBoxW = 1.2;
    const sourceSpacing = (RIGHT_COL_W - (sources.length * sourceBoxW)) / (sources.length - 1);
    let sourceX = RIGHT_COL_X;
    const sourceY = CONTENT_START_Y + 0.2;

    sources.forEach(source => {
        addImageWithFallback(slide, source.icon, { x: sourceX + 0.35, y: sourceY, w: 0.5, h: 0.5 }, 'Icon');
        slide.addText(source.label, {
            x: sourceX, y: sourceY + 0.55, w: sourceBoxW, h: 0.3,
            fontSize: FONT_SIZES.diagramLabel, color: COLORS.text, bold: true, align: 'center'
        });
        sourceX += sourceBoxW + sourceSpacing;
    });

    // Arrow to Data Lake
    addArrow(slide, RIGHT_COL_X + (RIGHT_COL_W / 2), sourceY + 1.0, 0);

    // Data Lake (Center)
    const dataLakeY = sourceY + 1.2;
    const dataLakeW = 2.0;
    const dataLakeH = 1.0;
    const dataLakeX = RIGHT_COL_X + (RIGHT_COL_W - dataLakeW) / 2;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: dataLakeX, y: dataLakeY, w: dataLakeW, h: dataLakeH,
        fill: { color: COLORS.cardBg }, line: { color: COLORS.cardBorder, width: 1 }, rectRadius: 0.1
    });
    addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws-s3.svg', { x: dataLakeX + 0.7, y: dataLakeY + 0.1, w: 0.6, h: 0.6 }, 'S3');
    slide.addText('AWS S3 (Data Lake)', {
        x: dataLakeX, y: dataLakeY + 0.7, w: dataLakeW, h: 0.2,
        fontSize: FONT_SIZES.diagramTitle, color: COLORS.text, bold: true, align: 'center'
    });

    // Processing Boxes (Bottom Row)
    const processY = dataLakeY + dataLakeH + 0.5;
    const processBoxW = 2.2;
    const processBoxH = 1.8;
    const processBoxSpacing = 0.6;
    const processBox1X = RIGHT_COL_X + (RIGHT_COL_W - (2 * processBoxW) - processBoxSpacing) / 2;
    const processBox2X = processBox1X + processBoxW + processBoxSpacing;

    // Box 1: EC2 / Comprehend
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: processBox1X, y: processY, w: processBoxW, h: processBoxH,
        fill: { color: COLORS.cardBg }, line: { color: COLORS.cardBorder, width: 1 }, rectRadius: 0.1
    });
    addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws-ec2.svg', { x: processBox1X + 0.85, y: processY + 0.1, w: 0.5, h: 0.5 }, 'EC2');
    slide.addText('AWS EC2', { x: processBox1X, y: processY + 0.6, w: processBoxW, h: 0.2, fontSize: FONT_SIZES.diagramTitle, color: COLORS.text, bold: true, align: 'center' });
    slide.addText('(Scrapy Cloud for Web Scraping)', { x: processBox1X, y: processY + 0.75, w: processBoxW, h: 0.2, fontSize: FONT_SIZES.diagramLabel, color: COLORS.textLight, align: 'center' });
    addArrow(slide, processBox1X + (processBoxW / 2), processY + 1.05, 0);
    addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Machine-Learning/AWS-Machine-Learning-Comprehend.svg', { x: processBox1X + 0.85, y: processY + 1.2, w: 0.5, h: 0.5 }, 'Comprehend');
    slide.addText('AWS Comprehend (NLP)', { x: processBox1X, y: processY + 1.5, w: processBoxW, h: 0.2, fontSize: FONT_SIZES.diagramLabel, color: COLORS.text, bold: true, align: 'center' });

    // Box 2: SageMaker / Tableau
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: processBox2X, y: processY, w: processBoxW, h: processBoxH,
        fill: { color: COLORS.cardBg }, line: { color: COLORS.cardBorder, width: 1 }, rectRadius: 0.1
    });
    addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Machine-Learning/AWS-Machine-Learning-SageMaker.svg', { x: processBox2X + 0.85, y: processY + 0.1, w: 0.5, h: 0.5 }, 'SageMaker');
    slide.addText('AWS SageMaker', { x: processBox2X, y: processY + 0.6, w: processBoxW, h: 0.2, fontSize: FONT_SIZES.diagramTitle, color: COLORS.text, bold: true, align: 'center' });
    slide.addText('(Machine Learning)', { x: processBox2X, y: processY + 0.75, w: processBoxW, h: 0.2, fontSize: FONT_SIZES.diagramLabel, color: COLORS.textLight, align: 'center' });
    addArrow(slide, processBox2X + (processBoxW / 2), processY + 1.05, 0);
    addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/tableau.svg', { x: processBox2X + 0.85, y: processY + 1.2, w: 0.5, h: 0.5 }, 'Tableau');
    slide.addText('Tableau (Interactive Dashboards)', { x: processBox2X, y: processY + 1.5, w: processBoxW, h: 0.2, fontSize: FONT_SIZES.diagramLabel, color: COLORS.text, bold: true, align: 'center' });

    // Connecting lines from Data Lake to Processing Boxes
    slide.addShape(pptx.shapes.LINE, { x: dataLakeX + (dataLakeW / 2), y: dataLakeY + dataLakeH, w: 0, h: 0.25, line: { color: COLORS.arrow, width: 2 } });
    slide.addShape(pptx.shapes.LINE, { x: processBox1X + (processBoxW / 2), y: dataLakeY + dataLakeH + 0.25, w: processBox2X - processBox1X, h: 0, line: { color: COLORS.arrow, width: 2 } });
    slide.addShape(pptx.shapes.LINE, { x: processBox1X + (processBoxW / 2), y: dataLakeY + dataLakeH + 0.25, w: 0, h: processY - (dataLakeY + dataLakeH + 0.25), line: { color: COLORS.arrow, width: 2 } });
    slide.addShape(pptx.shapes.LINE, { x: processBox2X + (processBoxW / 2), y: dataLakeY + dataLakeH + 0.25, w: 0, h: processY - (dataLakeY + dataLakeH + 0.25), line: { color: COLORS.arrow, width: 2 } });


    return pptx.writeFile({ fileName: 'generated/presentations/slide5_general_robust.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
