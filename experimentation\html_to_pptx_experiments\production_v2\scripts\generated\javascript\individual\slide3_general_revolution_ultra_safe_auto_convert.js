const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =================================================================================
    // 🛡️ ULTRA-SAFE CONFIGURATION & GENERIC CONSTANTS
    // =================================================================================
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4
    const MAX_CONTENT_Y = 5.0; // Slightly more space for footer

    // Two-column layout constants
    const LEFT_COL_X = 0.5;
    const RIGHT_COL_X = 5.3;
    const COL_W = 4.2;
    const COL_GAP = RIGHT_COL_X - (LEFT_COL_X + COL_W); // 0.6

    // Vertical positioning constants for this dense slide
    const HEADER_H = 0.8;
    const FOOTER_H = 0.5;
    const CONTENT_START_Y = HEADER_H + 0.2; // Start content right after header
    const CONTENT_H = MAX_CONTENT_Y - CONTENT_START_Y - FOOTER_H; // Available height for columns

    // =================================================================================
    // 🛠️ HELPER FUNCTIONS
    // =================================================================================

    /**
     * Adds an image with a robust fallback mechanism to prevent errors and show a placeholder.
     * @param {object} slide - The PptxGenJS slide object.
     * @param {string} imagePath - The URL or path of the image.
     * @param {object} options - {x, y, w, h} for positioning.
     * @param {string} fallbackText - Text to display if the image fails.
     */
    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'cover', w: options.w, h: options.h } // Use 'cover' to fill the area
            });
        } catch (error) {
            console.warn(`⚠️ Image failed to load, using fallback: ${imagePath}`);
            // Fallback: Add a placeholder shape and text
            slide.addShape(pptx.shapes.RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' },
                line: { color: 'D1D5DB', width: 1 }
            });
            slide.addText(fallbackText, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fontSize: 8, color: '6B7280', align: 'center', valign: 'middle'
            });
        }
    }

    /**
     * Translates Tailwind CSS font size classes to PowerPoint-appropriate font sizes.
     * @param {string} htmlClass - The string of HTML classes.
     * @returns {number} The appropriate font size for PowerPoint.
     */
    function translateHTMLFontSize(htmlClass) {
        if (htmlClass.includes('text-4xl')) return 16; // 36px -> 16pt
        if (htmlClass.includes('text-2xl')) return 12; // 24px -> 12pt
        if (htmlClass.includes('text-lg')) return 10;  // 18px -> 10pt
        return 9; // Default for other text
    }

    // =================================================================================
    // 🎨 SLIDE HEADER
    // =================================================================================
    // Header background (from border-b)
    slide.addShape(pptx.shapes.LINE, {
        x: 0, y: HEADER_H, w: SLIDE_WIDTH, h: 0,
        line: { color: 'E5E7EB', width: 1 }
    });

    // GCP Logo (from <img> tag)
    addImageWithFallback(slide, 'https://download.logo.wine/logo/Google_Cloud_Platform/Google_Cloud_Platform-Logo.wine.png', {
        x: LEFT_COL_X, y: 0.25, w: 1.5, h: 0.3
    }, 'GCP Logo');

    // Slide Title (from <h1> tag)
    slide.addText('The Evolving Business Landscape: Why Cloud is Essential', {
        x: LEFT_COL_X + 1.6, y: 0.2, w: SLIDE_WIDTH - (LEFT_COL_X + 1.7) - SAFE_MARGIN, h: 0.4,
        fontSize: translateHTMLFontSize('text-4xl'),
        color: '374151', // text-gray-700
        bold: true,
        valign: 'middle'
    });

    // =================================================================================
    // 🏛️ TWO-COLUMN LAYOUT
    // =================================================================================

    // --- LEFT COLUMN: The Challenge ---
    let currentYLeft = CONTENT_START_Y;

    // Background container (from <div class="bg-gray-50 ...">)
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: LEFT_COL_X, y: currentYLeft, w: COL_W, h: CONTENT_H,
        fill: { color: 'F9FAFB' }, // bg-gray-50
        line: { color: 'E5E7EB', width: 1 }, // border-gray-200
        rectRadius: 0.15 // rounded-lg
    });

    // Column Title (from <h2> tag)
    const leftTitleY = currentYLeft + 0.2;
    slide.addText('The Challenge: Traditional IT Constraints', {
        x: LEFT_COL_X + 0.2, y: leftTitleY, w: COL_W - 0.4, h: 0.3,
        fontSize: translateHTMLFontSize('text-2xl'),
        color: '1d3557',
        bold: true
    });
    // Title bottom border (from border-b-2)
    slide.addShape(pptx.shapes.LINE, {
        x: LEFT_COL_X + 0.2, y: leftTitleY + 0.3, w: COL_W - 0.4, h: 0,
        line: { color: 'e63946', width: 2 }
    });
    currentYLeft = leftTitleY + 0.4;

    // Image (from <img> tag)
    const leftImageH = 1.2;
    addImageWithFallback(slide, 'https://as1.ftcdn.net/v2/jpg/06/13/22/76/1000_F_613227665_HwmIuE3xGPlEDGUNwTCmrRPZ36WhaH1h.jpg', {
        x: LEFT_COL_X + 0.2, y: currentYLeft, w: COL_W - 0.4, h: leftImageH
    }, 'Tangled server wires');
    // Red overlay (from <div class="absolute ... bg-red-900 opacity-40">)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X + 0.2, y: currentYLeft, w: COL_W - 0.4, h: leftImageH,
        fill: { color: 'C53030', transparency: 60 } // Approximating bg-red-900 with opacity
    });
    currentYLeft += leftImageH + 0.15;

    // Descriptive Paragraph (from <p> tag)
    slide.addText('Your industry faces increasing pressure to overcome the limitations of on-premise infrastructure:', {
        x: LEFT_COL_X + 0.2, y: currentYLeft, w: COL_W - 0.4, h: 0.4,
        fontSize: translateHTMLFontSize('text-lg'),
        color: '4B5563' // text-gray-600
    });
    currentYLeft += 0.4;

    // Bullet Points (from <ul><li> tags) - ALL content preserved with tight spacing
    const problems = [
        { bold: 'High IT Costs:', text: ' Constant capital expenditure on hardware, maintenance, and real estate.' },
        { bold: 'Slow Agility:', text: ' Lengthy procurement and deployment cycles hinder speed to market.' },
        { bold: 'Complex Security:', text: ' Difficult to manage and scale security measures effectively.' },
        { bold: 'Rigid Scaling:', text: ' Inability to scale resources dynamically with demand.' },
        { bold: 'Legacy Systems:', text: ' Modernizing outdated applications is complex and resource-intensive.' }
    ];
    const bulletSpacing = 0.3;
    problems.forEach(item => {
        slide.addText('✘', { x: LEFT_COL_X + 0.2, y: currentYLeft, w: 0.2, h: 0.25, fontSize: 10, color: 'DB4437', bold: true, valign: 'top' });
        slide.addText([{ text: item.bold, options: { bold: true } }, { text: item.text }], {
            x: LEFT_COL_X + 0.45, y: currentYLeft, w: COL_W - 0.65, h: 0.3,
            fontSize: 9, color: '374151', valign: 'top'
        });
        currentYLeft += bulletSpacing;
    });

    // --- RIGHT COLUMN: The Solution ---
    let currentYRight = CONTENT_START_Y;

    // Background container (from <div class="bg-blue-50 ...">)
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: RIGHT_COL_X, y: currentYRight, w: COL_W, h: CONTENT_H,
        fill: { color: 'EFF6FF' }, // bg-blue-50
        line: { color: 'BFDBFE', width: 1 }, // border-blue-200
        rectRadius: 0.15 // rounded-lg
    });

    // Column Title (from <h2> tag)
    const rightTitleY = currentYRight + 0.2;
    slide.addText('The Solution: GCP-Powered Transformation', {
        x: RIGHT_COL_X + 0.2, y: rightTitleY, w: COL_W - 0.4, h: 0.3,
        fontSize: translateHTMLFontSize('text-2xl'),
        color: '1d3557',
        bold: true
    });
    // Title bottom border (from border-b-2)
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X + 0.2, y: rightTitleY + 0.3, w: COL_W - 0.4, h: 0,
        line: { color: '457b9d', width: 2 }
    });
    currentYRight = rightTitleY + 0.4;

    // Image (from <img> tag)
    const rightImageH = 1.2;
    addImageWithFallback(slide, 'https://static.vecteezy.com/system/resources/previews/047/408/392/non_2x/modern-cloud-computing-network-illustration-with-devices-and-connections-free-vector.jpg', {
        x: RIGHT_COL_X + 0.2, y: currentYRight, w: COL_W - 0.4, h: rightImageH
    }, 'Modern cloud network');
    // Blue overlay (from <div class="absolute ... bg-blue-900 opacity-20">)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: RIGHT_COL_X + 0.2, y: currentYRight, w: COL_W - 0.4, h: rightImageH,
        fill: { color: '1E3A8A', transparency: 80 } // Approximating bg-blue-900 with opacity
    });
    currentYRight += rightImageH + 0.15;

    // Descriptive Paragraph (from <p> tag)
    slide.addText('GCP provides a secure, scalable, and cost-effective foundation for your digital evolution:', {
        x: RIGHT_COL_X + 0.2, y: currentYRight, w: COL_W - 0.4, h: 0.4,
        fontSize: translateHTMLFontSize('text-lg'),
        color: '4B5563' // text-gray-600
    });
    currentYRight += 0.4;

    // Bullet Points (from <ul><li> tags) - ALL content preserved
    const solutions = [
        { bold: 'Cost-Effective Platform:', text: ' Shift from CapEx to OpEx with pay-as-you-go pricing.' },
        { bold: 'Rapid Innovation:', text: ' Leverage cutting-edge AI, ML, and data analytics services.' },
        { bold: 'Global Performance:', text: ' Utilize a world-class network of data centers for low latency.' },
        { bold: 'Elastic Scalability:', text: ' Automatically adjust resources to meet real-time user demand.' },
        { bold: 'Enterprise-Grade Security:', text: ' Benefit from Google\'s multi-layered, battle-tested security model.' }
    ];
    solutions.forEach(item => {
        slide.addText('✔', { x: RIGHT_COL_X + 0.2, y: currentYRight, w: 0.2, h: 0.25, fontSize: 10, color: '0F9D58', bold: true, valign: 'top' });
        slide.addText([{ text: item.bold, options: { bold: true } }, { text: item.text }], {
            x: RIGHT_COL_X + 0.45, y: currentYRight, w: COL_W - 0.65, h: 0.3,
            fontSize: 9, color: '374151', valign: 'top'
        });
        currentYRight += bulletSpacing;
    });

    // =================================================================================
    // 🦶 SLIDE FOOTER
    // =================================================================================
    // Footer background (from bg-gray-100)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: MAX_CONTENT_Y, w: SLIDE_WIDTH, h: FOOTER_H,
        fill: { color: 'F3F4F6' }
    });

    // Footer text (from <span> tags)
    slide.addText('Unlocking Innovation with Google Cloud', {
        x: LEFT_COL_X, y: MAX_CONTENT_Y, w: 4, h: FOOTER_H,
        fontSize: 8, color: '4B5563', bold: true, valign: 'middle'
    });
    slide.addText('Slide 3', {
        x: SLIDE_WIDTH - SAFE_MARGIN - 4, y: MAX_CONTENT_Y, w: 4, h: FOOTER_H,
        fontSize: 8, color: '4B5563', bold: true, valign: 'middle', align: 'right'
    });

    // =================================================================================
    // 💾 EXPORT PRESENTATION
    // =================================================================================
    return pptx.writeFile({ fileName: 'generated/presentations/slide3_general_revolution.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
