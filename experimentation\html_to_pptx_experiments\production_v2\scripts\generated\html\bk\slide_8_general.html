<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investment & ROI: A Zero Trust Approach</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f2f5;
            font-family: 'Roboto', sans-serif;
        }

        .slide-container {
            width: 1280px;
            height: 720px;
            background-color: #0a192f;
            background-image:
                linear-gradient(rgba(10, 25, 47, 0.85), rgba(10, 25, 47, 0.85)),
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a3a6e' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            color: #e6f1ff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 60px;
            box-sizing: border-box;
        }

        .slide-content {
            flex-grow: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            width: 100%;
            padding-top: 0;
        }

        .slide-content h1 {
            font-size: 2.8em;
            color: #64ffda;
            font-weight: 700;
            margin: 0 0 15px 0;
            line-height: 1.2;
        }
        
        .title-divider {
            width: 120px;
            height: 3px;
            background-color: #64ffda;
            margin-bottom: 40px;
        }

        .content-wrapper {
            display: flex;
            width: 100%;
            gap: 40px; /* Space between table and ROI list */
            align-items: flex-start;
        }

        .cost-table-container {
            flex: 3; /* Takes more space */
        }

        .cost-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 1.05em;
        }

        .cost-table th, .cost-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #1a3a6e;
            color: #ccd6f6;
        }

        .cost-table th {
            background-color: rgba(42, 75, 120, 0.3);
            font-weight: 700;
            color: #a8b2d1;
        }
        
        .cost-table td:nth-child(2) {
            color: #64ffda;
            font-weight: 700;
        }

        .cost-table tr:last-child td {
            border-bottom: none;
        }
        
        .cost-table tfoot td {
            font-weight: 700;
            font-size: 1.1em;
            color: #64ffda;
            padding-top: 15px;
        }

        .roi-container {
            flex: 2;
            padding-left: 20px;
            border-left: 2px solid #1a3a6e;
        }

        .roi-container h2 {
            font-size: 1.6em;
            color: #a8b2d1;
            margin-top: 0;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .roi-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .roi-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 18px;
            font-size: 1.1em;
            color: #ccd6f6;
            line-height: 1.5;
        }

        .roi-list .icon {
            width: 22px;
            height: 22px;
            margin-right: 15px;
            flex-shrink: 0;
            margin-top: 4px;
            filter: invert(84%) sepia(29%) saturate(399%) hue-rotate(99deg) brightness(104%) contrast(101%); /* #64ffda color */
        }

        .roi-list strong {
            font-weight: 700;
            color: #a8b2d1;
        }

    </style>
</head>
<body>

    <div class="slide-container">
        <div class="slide-content">
            <h1>Investing in a Secure Future</h1>
            <div class="title-divider"></div>
            <div class="content-wrapper">
                <div class="cost-table-container">
                    <table class="cost-table">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Estimated Cost</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Software & Cloud Services</td>
                                <td>$50,000/year</td>
                                <td>Azure AD, Intune, Cloudflare ZTNA, Microsoft Purview</td>
                            </tr>
                            <tr>
                                <td>Hardware</td>
                                <td>$5,000</td>
                                <td>Minimal, depends on existing infrastructure</td>
                            </tr>
                            <tr>
                                <td>Personnel Costs</td>
                                <td>$150,000/year</td>
                                <td>Security team, cloud experts, and training staff</td>
                            </tr>
                            <tr>
                                <td>Training Costs</td>
                                <td>$10,000</td>
                                <td>Employee and technical training programs</td>
                            </tr>
                            <tr>
                                <td>Consulting Fees</td>
                                <td>$20,000</td>
                                <td>If using external implementation consultants</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td>Total Estimated Cost</td>
                                <td>$235,000</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="roi-container">
                    <h2>Return on Investment (ROI)</h2>
                    <ul class="roi-list">
                        <li>
                            <img src="https://www.svgrepo.com/show/417127/investment.svg" alt="ROI Icon" class="icon">
                            <div><strong>Reduced Risk of Data Breaches:</strong> Quantify savings from preventing costly breaches.</div>
                        </li>
                        <li>
                            <img src="https://www.svgrepo.com/show/417127/investment.svg" alt="ROI Icon" class="icon">
                            <div><strong>Improved Compliance:</strong> Avoid fines and penalties from non-compliance.</div>
                        </li>
                        <li>
                            <img src="https://www.svgrepo.com/show/417127/investment.svg" alt="ROI Icon" class="icon">
                            <div><strong>Increased Productivity:</strong> Streamlined access and reduced security-related downtime.</div>
                        </li>
                        <li>
                            <img src="https://www.svgrepo.com/show/417127/investment.svg" alt="ROI Icon" class="icon">
                            <div><strong>Enhanced Reputation:</strong> Maintain customer trust and protect brand value.</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

</body>
</html>