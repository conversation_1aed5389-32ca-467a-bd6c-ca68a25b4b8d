<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Embracing the Future: Your Azure Journey Starts Now</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;700&display=swap');
    html, body { height: 100%; margin: 0; }
    body {
      background-color: #f0f2f5;
      font-family: 'Segoe UI','Roboto','Helvetica Neue',Arial,sans-serif;
    }
    .slide-content { /* main content area marker */ }
  </style>
</head>
<body class="flex items-center justify-center">
  <div class="w-[1280px] h-[720px] bg-white shadow-2xl overflow-hidden flex flex-col">
    <!-- Header (consistent with previous slides) -->
    <div class="flex items-center px-10 bg-[#0078D4] text-white" style="height:72px;">
      <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/azure-icon.svg" alt="Azure Icon" class="w-8 h-8 mr-4" />
      <div class="font-bold text-[1.2em]">Microsoft Azure</div>
    </div>

    <!-- Content -->
    <div class="slide-content flex-1 px-10 py-4 box-border overflow-hidden">
      <!-- Title aligned top-left -->
      <h1 class="text-[2.4em] leading-tight font-bold text-[#005A9E] mb-3">
        Embracing the Future: Your Azure Journey Starts Now
      </h1>

      <!-- Two-column layout: Left = Explanation; Right = Roadmap + Milestones -->
      <div class="grid grid-cols-2 gap-6">
        <!-- LEFT COLUMN: Details and Plan -->
        <div class="flex flex-col space-y-4">
          <!-- Proposed Solution -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="text-[#0078D4] font-bold text-[1.3em] mb-2">Proposed Solution</div>
            <ul class="list-disc pl-5 space-y-2 text-[1.2em] text-gray-700">
              <li>
                <span class="font-semibold">Proof of Concept (POC):</span>
                Target a low‑risk workload to validate performance, security, and costs.
              </li>
              <li>
                <span class="font-semibold">Migration Strategy:</span>
                Phased “assess → remediate → migrate → validate” waves to minimize disruption.
              </li>
              <li>
                <span class="font-semibold">New App Development:</span>
                Build cloud‑native services (App Service/AKS/Functions) for agility and scale.
              </li>
            </ul>
          </div>

          <!-- Infrastructure & Tech + Human Resources -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="text-[#0078D4] font-bold text-[1.3em] mb-2">Infrastructure & Tech Stack</div>
            <ul class="list-disc pl-5 space-y-1.5 text-[1.2em] text-gray-700 mb-3">
              <li>Compute: Azure VMs, Azure App Service, AKS (as needed)</li>
              <li>Storage & Data: Storage Accounts (Blob/Files), Azure SQL, Cosmos DB</li>
              <li>Networking: Virtual Network, Subnets, NSGs, Load Balancer, Private Endpoints</li>
              <li>Security & Ops: Key Vault, Microsoft Defender for Cloud, Azure Monitor/Log Analytics</li>
            </ul>
            <div class="text-[#0078D4] font-bold text-[1.2em] mb-1">Human Resources</div>
            <ul class="list-disc pl-5 space-y-1 text-[1.2em] text-gray-700">
              <li>Azure Architect, Cloud/Platform Engineer, Security/Identity, Data Engineer</li>
              <li>Upskill internal teams and/or engage certified partners for implementation</li>
            </ul>
          </div>

          <!-- Cost, Conclusion, CTA & Contact -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-[#0078D4] font-bold text-[1.3em] mb-2">Cost (Preliminary)</div>
                <ul class="list-disc pl-5 space-y-1 text-[1.2em] text-gray-700">
                  <li>POC cloud usage: ~$2K–$5K/month</li>
                  <li>Migration waves: ~$8K–$20K/month (workload‑dependent)</li>
                  <li>Implementation services: ~$50K–$150K (scope‑dependent)</li>
                </ul>
                <div class="text-xs text-gray-500 mt-2">Estimates vary by scale, regions, licensing, and optimization (RIs/AHUB).</div>
              </div>
              <div>
                <div class="text-[#0078D4] font-bold text-[1.3em] mb-2">Conclusion & Call to Action</div>
                <p class="text-[1.2em] text-gray-700 mb-2">
                  Azure accelerates innovation, reduces TCO, and scales securely across your business.
                </p>
                <p class="text-[1.2em] text-gray-700 mb-2">
                  Next step: Schedule a follow‑up to refine scope, confirm timeline, and finalize costs.
                </p>
                <div class="text-[1.2em] text-gray-800">
                  <div class="font-semibold">Contact</div>
                  <div>[Your Name], Principal Consultant</div>
                  <div><EMAIL> • +1 (555) 123‑4567</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- RIGHT COLUMN: Roadmap Timeline + Key Milestones -->
        <div class="flex flex-col space-y-4">
          <!-- Roadmap (Gantt-style) -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="text-[#005A9E] font-semibold text-[1.1em]">Azure Adoption Roadmap (24 weeks)</div>
              <div class="text-xs text-gray-500">High-level view</div>
            </div>

            <!-- Scale -->
            <div class="flex justify-between text-xs text-gray-600 mb-1 px-1">
              <span>W0</span><span>W6</span><span>W12</span><span>W18</span><span>W24</span>
            </div>

            <!-- Timeline Canvas -->
            <div class="relative w-full h-48 bg-white rounded border border-gray-100 overflow-hidden">
              <!-- Vertical ticks -->
              <span class="absolute left-0 top-0 h-full w-px bg-gray-200"></span>
              <span class="absolute left-1/4 top-0 h-full w-px bg-gray-200"></span>
              <span class="absolute left-1/2 top-0 h-full w-px bg-gray-200"></span>
              <span class="absolute left-3/4 top-0 h-full w-px bg-gray-200"></span>
              <span class="absolute right-0 top-0 h-full w-px bg-gray-200"></span>

              <!-- Bars (lanes) -->
              <!-- Assess & Plan -->
              <div class="absolute top-4 left-[0%] w-[15%] h-7 rounded bg-[#0078D4] flex items-center px-2 text-white text-sm font-semibold">Assess & Plan</div>
              <!-- POC -->
              <div class="absolute top-16 left-[10%] w-[25%] h-7 rounded bg-[#0EA5E9] flex items-center px-2 text-white text-sm font-semibold">POC</div>
              <!-- Landing Zone & Governance -->
              <div class="absolute top-28 left-[25%] w-[20%] h-7 rounded bg-[#0078D4] flex items-center px-2 text-white text-sm font-semibold">Landing Zone & Gov</div>
              <!-- Migration Waves -->
              <div class="absolute top-[140px] left-[45%] w-[40%] h-7 rounded bg-[#0EA5E9] flex items-center px-2 text-white text-sm font-semibold">Migration Waves</div>
              <!-- Optimize & Operate -->
              <div class="absolute top-[188px] left-[80%] w-[18%] h-7 rounded bg-[#0078D4] flex items-center px-2 text-white text-sm font-semibold">Optimize & Operate</div>
            </div>

            <!-- Legend -->
            <div class="flex items-center space-x-6 mt-3 text-sm text-gray-700">
              <div class="flex items-center">
                <span class="inline-block w-3 h-3 rounded mr-2" style="background-color:#0078D4;"></span>
                <span>Plan/Govern/Operate</span>
              </div>
              <div class="flex items-center">
                <span class="inline-block w-3 h-3 rounded mr-2" style="background-color:#0EA5E9;"></span>
                <span>POC/Migration</span>
              </div>
            </div>
          </div>

          <!-- Key Milestones -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="text-[#005A9E] font-semibold text-[1.1em] mb-2">Key Milestones & Deliverables</div>
            <ul class="space-y-2 text-[1.2em] text-gray-700">
              <li class="flex items-start">
                <span class="mt-2 mr-2 inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
                <div><span class="font-semibold">W2–W4:</span> Assessment report, migration backlog, and business case.</div>
              </li>
              <li class="flex items-start">
                <span class="mt-2 mr-2 inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0EA5E9;"></span>
                <div><span class="font-semibold">W6–W8:</span> POC validated (KPIs, security baseline, cost telemetry).</div>
              </li>
              <li class="flex items-start">
                <span class="mt-2 mr-2 inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
                <div><span class="font-semibold">W8–W10:</span> Landing Zone ready (networking, identity, policies, guardrails).</div>
              </li>
              <li class="flex items-start">
                <span class="mt-2 mr-2 inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0EA5E9;"></span>
                <div><span class="font-semibold">W12–W20:</span> Migration Waves cutovers with runbooks and rollback plans.</div>
              </li>
              <li class="flex items-start">
                <span class="mt-2 mr-2 inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
                <div><span class="font-semibold">W20–W24:</span> Optimization, right‑sizing, FinOps reporting, handover to ops.</div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>