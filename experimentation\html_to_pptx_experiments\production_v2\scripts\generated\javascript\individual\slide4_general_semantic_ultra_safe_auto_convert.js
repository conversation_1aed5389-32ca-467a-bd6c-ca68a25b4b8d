const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 0.9; // Higher start for dense content
    const MAX_CONTENT_Y = 5.2; // Allow slightly more vertical space

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        headline: '002147',
        sectionTitle: '002147',
        bodyText: '333333',
        subText: '475569',
        cardBorder: 'E5E7EB',
        cardBg: 'FBFBFD',
        diagramBg: 'F8FAFC',
        arrow: '94A3B8',
        checkMark: '16A34A',
        chipBg: 'EEF2FF',
        chipBorder: 'C7D2FE',
        chipText: '0F172A',
        docBorder: 'D1D5DB',
        docLine: 'E5E7EB',
    };

    // Font Sizes (Professional & Consistent)
    const FONT_SIZES = {
        headline: 16,
        sectionTitle: 12,
        listItem: 9,
        diagramTitle: 9,
        diagramSub: 8,
        chipLabel: 7,
    };

    // Layout Calculations
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN);
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = CONTENT_WIDTH * 0.44; // 4.136"
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.3; // 0.3" gap
    const RIGHT_COL_W = CONTENT_WIDTH - LEFT_COL_W - 0.3; // 5.264"

    // =======================================================================
    // 2. HELPER FUNCTIONS (Robust Edge Case Handling)
    // =======================================================================

    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            console.warn(`⚠️ Image failed, using fallback: ${imagePath}`);
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' }, line: { color: 'D1D5DB', width: 1 },
                rectRadius: 0.05
            });
            slide.addText(fallbackText, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fontSize: 8, color: '6B7280', align: 'center', valign: 'middle'
            });
        }
    }

    // =======================================================================
    // 3. SLIDE BACKGROUND & HEADER (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    // Header Logo
    addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/elasticsearch.svg', {
        x: SLIDE_WIDTH - SAFE_MARGIN - 1.2, y: 0.2, w: 1.2, h: 0.6
    }, 'Logo');

    // Main Headline
    slide.addText('Our Solution: Semantic Search Powered by AI — Intelligent Contract Discovery: Understanding Meaning, Delivering Results', {
        x: SAFE_MARGIN, y: 0.2, w: SLIDE_WIDTH - (2 * SAFE_MARGIN) - 1.5, h: 0.6,
        fontSize: FONT_SIZES.headline,
        color: COLORS.headline,
        bold: true,
        valign: 'top'
    });

    // =======================================================================
    // 4. LEFT COLUMN: KEY FEATURES & BENEFITS
    // =======================================================================

    let currentY = CONTENT_START_Y;

    // --- Key Features Card ---
    const featuresCardH = 2.3;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: LEFT_COL_X, y: currentY, w: LEFT_COL_W, h: featuresCardH,
        fill: { color: COLORS.cardBg },
        line: { color: COLORS.cardBorder, width: 1 },
        rectRadius: 0.15
    });

    let cardContentY = currentY + 0.2;
    slide.addText('Key Features', {
        x: LEFT_COL_X + 0.2, y: cardContentY, w: LEFT_COL_W - 0.4, h: 0.3,
        fontSize: FONT_SIZES.sectionTitle, color: COLORS.sectionTitle, bold: true
    });
    cardContentY += 0.3;

    const features = [
        "Semantic Search: Leverages transformer models (e.g., BERT, RoBERTa) to understand the meaning of queries and documents.",
        "Vector Database: Stores contracts as vector embeddings for fast and accurate similarity search.",
        "Cloud-Based Infrastructure: Scalable and reliable cloud infrastructure (AWS, Azure, or Google Cloud).",
        "User-Friendly Interface: Intuitive search interface for easy access to contract information.",
        "RAG Integration (Optional): Generates concise summaries or answers based on retrieved contract snippets."
    ];

    features.forEach((text, index) => {
        slide.addText('✓', { x: LEFT_COL_X + 0.2, y: cardContentY, w: 0.2, h: 0.2, color: COLORS.checkMark, bold: true, fontSize: 12 });
        const textH = text.length > 100 ? 0.4 : 0.25;
        slide.addText(text, {
            x: LEFT_COL_X + 0.4, y: cardContentY, w: LEFT_COL_W - 0.6, h: textH,
            fontSize: FONT_SIZES.listItem, color: COLORS.bodyText
        });
        cardContentY += textH + 0.05;

        if (index === 2) { // Cloud provider icons
            const iconY = cardContentY - 0.05;
            addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Business-Applications/AWS-Business-Applications-WorkDocs.svg', { x: LEFT_COL_X + 0.4, y: iconY, w: 0.3, h: 0.3 }, 'AWS');
            addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/ai%20%2B%20machine%20learning/Azure-ai%20%2B%20machine%20learning-00792-icon-service-Computer-Vision.svg', { x: LEFT_COL_X + 0.8, y: iconY, w: 0.3, h: 0.3 }, 'Azure');
            addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Google-Kubernetes-Engine.svg', { x: LEFT_COL_X + 1.2, y: iconY, w: 0.3, h: 0.3 }, 'GCP');
            cardContentY += 0.3;
        }
        cardContentY += 0.1;
    });

    currentY += featuresCardH + 0.2;

    // --- Benefits Card ---
    const benefitsCardH = 1.5;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: LEFT_COL_X, y: currentY, w: LEFT_COL_W, h: benefitsCardH,
        fill: { color: COLORS.cardBg },
        line: { color: COLORS.cardBorder, width: 1 },
        rectRadius: 0.15
    });

    cardContentY = currentY + 0.2;
    slide.addText('Benefits', {
        x: LEFT_COL_X + 0.2, y: cardContentY, w: LEFT_COL_W - 0.4, h: 0.3,
        fontSize: FONT_SIZES.sectionTitle, color: COLORS.sectionTitle, bold: true
    });
    cardContentY += 0.3;

    const benefits = [
        "Superior Accuracy: Find the right contracts, even with nuanced queries.",
        "Faster Results: Dramatically reduce search time.",
        "Scalability: Handles growing contract volumes with ease.",
        "Improved Compliance: Proactively identify and manage contract risks."
    ];

    benefits.forEach(text => {
        slide.addText('✓', { x: LEFT_COL_X + 0.2, y: cardContentY, w: 0.2, h: 0.2, color: COLORS.checkMark, bold: true, fontSize: 12 });
        slide.addText(text, {
            x: LEFT_COL_X + 0.4, y: cardContentY, w: LEFT_COL_W - 0.6, h: 0.2,
            fontSize: FONT_SIZES.listItem, color: COLORS.bodyText
        });
        cardContentY += 0.25;
    });

    // =======================================================================
    // 5. RIGHT COLUMN: DIAGRAM
    // =======================================================================

    const diagramH = MAX_CONTENT_Y - CONTENT_START_Y;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: RIGHT_COL_X, y: CONTENT_START_Y, w: RIGHT_COL_W, h: diagramH,
        fill: { color: COLORS.diagramBg },
        line: { color: COLORS.cardBorder, width: 1 },
        rectRadius: 0.15
    });

    const diagramOriginX = RIGHT_COL_X;
    const diagramOriginY = CONTENT_START_Y;
    const scaleX = RIGHT_COL_W / 580; // HTML width ~580px
    const scaleY = diagramH / 460; // HTML height 460px

    function addDiagramBox(title, sub, x, y, w, h, iconUrl = null, iconAlt = '') {
        const boxX = diagramOriginX + x * scaleX;
        const boxY = diagramOriginY + y * scaleY;
        const boxW = w * scaleX;
        const boxH = h * scaleY;

        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: boxX, y: boxY, w: boxW, h: boxH,
            fill: { color: 'FFFFFF' }, line: { color: COLORS.cardBorder, width: 1 }, rectRadius: 0.1
        });

        const titleH = 0.2;
        slide.addText(title, {
            x: boxX + 0.1, y: boxY + 0.05, w: boxW - (iconUrl ? 0.4 : 0.2), h: titleH,
            fontSize: FONT_SIZES.diagramTitle, color: COLORS.chipText, bold: true
        });

        if (sub) {
            slide.addText(sub, {
                x: boxX + 0.1, y: boxY + 0.05 + titleH, w: boxW - 0.2, h: boxH - titleH - 0.1,
                fontSize: FONT_SIZES.diagramSub, color: COLORS.subText
            });
        }

        if (iconUrl) {
            addImageWithFallback(slide, iconUrl, { x: boxX + boxW - 0.35, y: boxY + 0.05, w: 0.3, h: 0.3 }, iconAlt);
        }
    }

    function addArrow(x1, y1, x2, y2, type) {
        const startX = diagramOriginX + x1 * scaleX;
        const startY = diagramOriginY + y1 * scaleY;
        const endX = diagramOriginX + x2 * scaleX;
        const endY = diagramOriginY + y2 * scaleY;
        const lineOpts = { line: { color: COLORS.arrow, width: 1.5 } };
        if (type === 'line') {
            slide.addShape(pptx.shapes.LINE, { x: startX, y: startY, w: endX - startX, h: endY - startY, ...lineOpts });
        } else {
            const arrowOpts = { fill: { color: COLORS.arrow } };
            let shape, opts;
            if (type === 'right') {
                shape = pptx.shapes.RIGHT_TRIANGLE;
                opts = { x: startX, y: startY - 0.06, w: 0.1, h: 0.12, ...arrowOpts };
            } else if (type === 'left') {
                shape = pptx.shapes.RIGHT_TRIANGLE;
                opts = { x: startX, y: startY - 0.06, w: 0.1, h: 0.12, flipH: true, ...arrowOpts };
            } else if (type === 'down') {
                shape = pptx.shapes.RIGHT_TRIANGLE;
                opts = { x: startX - 0.06, y: startY, w: 0.12, h: 0.1, rotate: 90, ...arrowOpts };
            } else if (type === 'up') {
                shape = pptx.shapes.RIGHT_TRIANGLE;
                opts = { x: startX - 0.06, y: startY, w: 0.12, h: 0.1, rotate: 270, ...arrowOpts };
            }
            if (shape) slide.addShape(shape, opts);
        }
    }

    function addLabelChip(text, x, y) {
        const chipX = diagramOriginX + x * scaleX;
        const chipY = diagramOriginY + y * scaleY;
        const textW = text.length * 0.05;
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: chipX, y: chipY, w: textW, h: 0.18,
            fill: { color: COLORS.chipBg }, line: { color: COLORS.chipBorder, width: 0.5 }, rectRadius: 0.09
        });
        slide.addText(text, {
            x: chipX, y: chipY, w: textW, h: 0.18,
            fontSize: FONT_SIZES.chipLabel, color: COLORS.chipText, align: 'center', valign: 'middle'
        });
    }

    // Draw Diagram Elements
    // Boxes
    addDiagramBox('Contract Documents', null, 14, 18, 150, 84);
    // Custom doc stack shape
    for (let i = 0; i < 3; i++) {
        const docX = diagramOriginX + (30 + i * 34) * scaleX;
        const docY = diagramOriginY + 55 * scaleY;
        const docW = 28 * scaleX;
        const docH = 36 * scaleY;
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, { x: docX, y: docY, w: docW, h: docH, fill: { color: 'FFFFFF' }, line: { color: COLORS.docBorder, width: 1 }, rectRadius: 0.03 });
        slide.addShape(pptx.shapes.RIGHT_TRIANGLE, { x: docX + docW * 0.6, y: docY, w: docW * 0.4, h: docH * 0.4, fill: { color: COLORS.docLine } });
    }

    addDiagramBox('Data Ingestion & Preprocessing', 'OCR, text extraction, chunking, PII masking', 190, 14, 175, 92);
    addDiagramBox('Transformer Model', 'BERT / RoBERTa', 380, 14, 170, 92);
    addDiagramBox('Vector Database', 'e.g., Pinecone, Weaviate', 380, 130, 170, 88, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/af382f4619a6b8a20527af737456bb800d9ba722/svgs/servers-isometric-icons/cloud-server.svg', 'Vector DB');
    addDiagramBox('User Query', 'Natural language', 14, 230, 156, 62);
    addDiagramBox('Query Processing', 'Normalization, filters', 14, 306, 156, 66);
    addDiagramBox('Search & Retrieval', 'kNN, re-ranking, filters', 238, 274, 210, 78, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/af382f4619a6b8a20527af737456bb800d9ba722/svgs/servers-isometric-icons/spin.6ffd8bb2.svg', 'Search');
    addDiagramBox('Relevant Contracts', 'Results + optional RAG summary', 238, 366, 210, 64);

    // Arrows
    addArrow(166, 60, 190, 60, 'line'); addArrow(188, 60, 0, 0, 'right');
    addArrow(365, 60, 377, 60, 'line'); addArrow(374, 60, 0, 0, 'right');
    addArrow(464, 106, 464, 122, 'line'); addArrow(464, 120, 0, 0, 'down');
    addArrow(92, 292, 92, 304, 'line'); addArrow(92, 300, 0, 0, 'down');
    addArrow(92, 170, 92, 302, 'line'); addArrow(92, 170, 378, 170, 'line'); addArrow(92, 170, 0, 0, 'up'); addArrow(374, 170, 0, 0, 'right');
    addArrow(464, 218, 464, 274, 'line'); addArrow(300, 274, 464, 274, 'line'); addArrow(300, 274, 0, 0, 'left');
    addArrow(170, 336, 236, 336, 'line'); addArrow(234, 336, 0, 0, 'right');
    addArrow(342, 352, 342, 362, 'line'); addArrow(342, 358, 0, 0, 'down');

    // Labels
    addLabelChip('Vector Embeddings', 410, 108);
    addLabelChip('Query Embedding', 120, 152);

    return pptx.writeFile({ fileName: 'generated/presentations/slide4_general_semantic.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
