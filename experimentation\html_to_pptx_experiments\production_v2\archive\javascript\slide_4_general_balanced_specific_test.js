
const PptxGenJS = require('pptxgenjs');

// An expert-level PptxGenJS solution that adheres to the principles of balanced sizing, readable fonts, and overflow prevention.
// This code analyzes the content density and text length to dynamically adjust font sizes and spacing, ensuring a clean and professional presentation slide.

function createPresentation() {
    const pptx = new PptxGenJS();
    // Set slide dimensions to 16:9 aspect ratio (10" x 5.625")
    pptx.layout = 'LAYOUT_16x9';
    const slide = pptx.addSlide({ slideNumber: { show: false } });

    // --- Color & Style Definitions ---
    const BG_COLOR = '0A192F';
    const TITLE_COLOR = '64FFDA';
    const TEXT_COLOR_LIGHT = 'CCD6F6';
    const TEXT_COLOR_MEDIUM = 'A8B2D1';
    const BORDER_COLOR = '1A3A6E';
    const ACCENT_COLOR = '64FFDA';

    // --- Slide Background ---
    // The complex SVG background is not directly translatable. A solid color is used as a fallback.
    slide.background = { color: BG_COLOR };

    // --- Content Analysis & Dynamic Sizing ---
    // Total text-heavy elements: 1 title, 5 list items, 1 conclusion, 3 diagram cards = 10 elements
    const total_elements = 10;
    let base_title_size = 22;
    let base_content_size = 12;
    let base_body_size = 11;
    let line_spacing = 0.35;

    // Adjust based on content density (more than 8 elements)
    if (total_elements > 8) {
        base_title_size -= 2; // 20
        base_content_size -= 1; // 11
        base_body_size -= 1; // 10
        line_spacing = 0.32; // Slightly reduce spacing
    } else if (total_elements > 6) {
        base_title_size -= 1;
        base_content_size -= 1;
        base_body_size -= 1;
    }

    // Helper function for responsive font sizing based on text length
    function getOptimalFontSize(text, baseSize) {
        let size = baseSize;
        if (text.length > 120) size -= 3;
        else if (text.length > 80) size -= 2;
        return Math.max(size, 9); // Ensure font size is never smaller than 9pt
    }

    // --- Slide Content ---
    const SAFE_X = 0.4;
    const SAFE_W = 9.2;
    const COL_W = 4.5; // Width for each column in a two-column layout
    const COL_GAP = 0.2;

    // 1. Title
    const titleText = 'Zero Trust: Never Trust, Always Verify';
    slide.addText(titleText, {
        x: SAFE_X,
        y: 0.4,
        w: SAFE_W,
        h: 0.7,
        fontSize: getOptimalFontSize(titleText, base_title_size),
        color: TITLE_COLOR,
        bold: true,
        valign: 'top'
    });

    // 2. Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_X,
        y: 1.0,
        w: 1.2, // 120px equivalent
        h: 0,
        line: { color: ACCENT_COLOR, width: 2 }
    });

    let currentY = 1.4; // Start Y position for content below the title

    // --- Left Column ---
    const leftColX = SAFE_X;

    // 3. Principles List (Bullet Points)
    const principles = [
        { icon: 'https://www.svgrepo.com/show/506443/user-id.svg', boldText: 'Identity-Centric Security:', text: 'Verify every user and device before granting access.' },
        { icon: 'https://www.svgrepo.com/show/493736/microsegmentation.svg', boldText: 'Microsegmentation:', text: 'Isolate applications and data to limit the blast radius of a breach.' },
        { icon: 'https://www.svgrepo.com/show/489159/monitor.svg', boldText: 'Continuous Monitoring:', text: 'Constantly monitor user activity, device health, and network traffic for suspicious behavior.' },
        { icon: 'https://www.svgrepo.com/show/508938/data-protection.svg', boldText: 'Data-Centric Protection:', text: 'Protect sensitive data at rest and in transit with encryption and DLP.' },
        { icon: 'https://www.svgrepo.com/show/494752/automation-robot-1.svg', boldText: 'Automation & Orchestration:', text: 'Automate security tasks and workflows to improve efficiency and reduce response times.' }
    ];

    let leftY = currentY;
    const iconSize = 0.25;
    const textIndent = 0.35;

    principles.forEach(item => {
        const fullText = item.boldText + ' ' + item.text;
        const fontSize = getOptimalFontSize(fullText, base_body_size);
        
        slide.addImage({
            path: item.icon,
            x: leftColX,
            y: leftY + 0.05,
            w: iconSize,
            h: iconSize
        });

        slide.addText([
            { text: item.boldText + ' ', options: { color: TEXT_COLOR_MEDIUM, bold: true, fontSize: fontSize } },
            { text: item.text, options: { color: TEXT_COLOR_LIGHT, fontSize: fontSize } }
        ], {
            x: leftColX + textIndent,
            y: leftY,
            w: COL_W - textIndent,
            h: 0.5, // Allow space for wrapping
            lineSpacing: fontSize + 4 // Dynamic line spacing
        });
        
        // Estimate height of the text block and add spacing
        const textHeight = pptx.util.getSlidesForTable(fullText, { w: COL_W - textIndent, fontSize: fontSize }).length * (fontSize / 72 * 1.2);
        leftY += Math.max(textHeight, iconSize) + 0.15;
    });

    // 4. Conclusion Text
    const conclusionY = 4.2; // Positioned towards the bottom of the column
    slide.addShape(pptx.shapes.LINE, {
        x: leftColX,
        y: conclusionY,
        w: 0,
        h: 0.6,
        line: { color: ACCENT_COLOR, width: 2.5 }
    });

    const conclusionText = "Why it Works: Zero Trust eliminates implicit trust, making it significantly harder for attackers to gain access to sensitive data.";
    const conclusionFontSize = getOptimalFontSize(conclusionText, base_body_size);
    slide.addText([
        { text: 'Why it Works: ', options: { color: ACCENT_COLOR, bold: true, fontSize: conclusionFontSize } },
        { text: 'Zero Trust eliminates implicit trust, making it significantly harder for attackers to gain access to sensitive data.', options: { color: TEXT_COLOR_MEDIUM, fontSize: conclusionFontSize } }
    ], {
        x: leftColX + 0.2,
        y: conclusionY,
        w: COL_W - 0.2,
        h: 0.6,
        lineSpacing: conclusionFontSize + 4
    });

    // --- Right Column ---
    const rightColX = leftColX + COL_W + COL_GAP;
    let rightY = currentY + 0.2; // Align top of first card with top of list

    // 5. Diagram Cards
    const diagramCards = [
        { icon: 'https://www.svgrepo.com/show/460224/add-user-circle.svg', title: 'Verify Explicitly', text: 'Authenticate and authorize based on all available data points.' },
        { icon: 'https://www.svgrepo.com/show/417139/security-shield.svg', title: 'Least Privilege Access', text: 'Limit user access with just-in-time and just-enough-access (JIT/JEA).' },
        { icon: 'https://www.svgrepo.com/show/375386/cloud-security-scanner.svg', title: 'Assume Breach', text: 'Minimize blast radius and segment access. Verify all sessions are encrypted.' }
    ];

    const cardHeight = 1.0;
    const cardSpacing = 0.25;

    diagramCards.forEach(card => {
        // Card background
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: rightColX,
            y: rightY,
            w: COL_W,
            h: cardHeight,
            fill: { color: '2A446E', transparency: 70 },
            line: { color: BORDER_COLOR, width: 1 },
            rounding: { type: 'round', size: 0.1 }
        });

        // Card icon
        slide.addImage({
            path: card.icon,
            x: rightColX + 0.2,
            y: rightY + (cardHeight - 0.5) / 2, // Center vertically
            w: 0.5,
            h: 0.5
        });

        // Card text
        const cardTextX = rightColX + 0.9;
        const cardTextW = COL_W - 1.1;
        const titleFontSize = getOptimalFontSize(card.title, base_content_size);
        const textFontSize = getOptimalFontSize(card.text, base_body_size - 1);

        slide.addText(card.title, {
            x: cardTextX,
            y: rightY + 0.15,
            w: cardTextW,
            h: 0.3,
            color: TEXT_COLOR_LIGHT,
            fontSize: titleFontSize,
            bold: true
        });
        slide.addText(card.text, {
            x: cardTextX,
            y: rightY + 0.45,
            w: cardTextW,
            h: 0.4,
            color: TEXT_COLOR_MEDIUM,
            fontSize: textFontSize
        });

        rightY += cardHeight + cardSpacing;
    });

    return pptx.writeFile({ fileName: 'generated_presentations/slide_4_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
