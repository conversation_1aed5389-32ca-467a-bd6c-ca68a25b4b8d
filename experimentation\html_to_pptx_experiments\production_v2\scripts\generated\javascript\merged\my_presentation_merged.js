
// Consolidated Multi-Slide PowerPoint Generator
// Generated by JavaScript-based merging approach (production_v2)

const PptxGenJS = require('pptxgenjs');


function generateSlide2AgendaUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_2_agenda_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE DEFAULTS & CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const CONTENT_WIDTH = 10.0 - (2 * SAFE_MARGIN); // 9.4, but we'll use a safer 8.2
    const ICON_TEXT_MARGIN = 0.3;

    // COLOR PALETTE (from HTML analysis)
    const BG_COLOR = '0a192f';
    const ACCENT_COLOR = '64ffda';
    const TEXT_COLOR = 'ccd6f6';
    const TEXT_BOLD_COLOR = 'a8b2d1';

    // Set slide background color
    slide.background = { color: BG_COLOR };

    // TITLE
    slide.addText("Fortifying Our Defenses: A Zero Trust Approach", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16, // Ultra-safe max title size
        color: ACCENT_COLOR,
        bold: true,
        align: 'left',
    });

    // TITLE DIVIDER (using a safe RECTANGLE shape)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.2, // 120px is ~1.2 inches
        h: 0.03, // 3px is ~0.03 inches
        fill: { color: ACCENT_COLOR },
    });

    // AGENDA CONTENT
    const agendaItems = [
        { bold: "The Challenge:", text: "Current security relies on outdated \"trust but verify\" models, leaving us vulnerable to sophisticated attacks and insider threats." },
        { bold: "The Solution:", text: "Implement a comprehensive Zero Trust security framework, assuming no user or device is inherently trustworthy." },
        { bold: "Key Components:", text: "Strong Identity & Access Management, Device Security, Network Microsegmentation, and Data Protection." },
        { bold: "Cloud-First Strategy:", text: "Leverage cloud services for scalability, cost-effectiveness, and advanced security features." },
        { bold: "Benefits:", text: "Reduced risk of breaches, improved compliance, enhanced data protection, and increased operational efficiency." },
        { bold: "Call to Action:", text: "Invest in a phased Zero Trust implementation to secure our sensitive data and prevent cyberattacks." }
    ];

    // DYNAMIC VERTICAL POSITIONING
    let currentY = CONTENT_START_Y + 0.2; // Start slightly lower after divider
    const ITEM_HEIGHT = 0.6; // Allocate generous space for each item to prevent overlap

    // Function to add text safely and prevent overflow
    function addTextSafely(slide, text, options) {
        if ((options.y + options.h) > MAX_CONTENT_Y) {
            console.warn(`Skipping element to prevent vertical overflow: ${text.substring(0, 30)}...`);
            return false;
        }
        slide.addText(text, options);
        return true;
    }

    // Loop through agenda items and add them to the slide
    agendaItems.forEach(item => {
        const iconX = SAFE_MARGIN;
        const textX = iconX + ICON_TEXT_MARGIN;
        const textW = 8.2 - ICON_TEXT_MARGIN;

        // Add Icon (using ultra-safe text-based checkmark)
        addTextSafely(slide, "✓", {
            x: iconX,
            y: currentY,
            w: 0.2,
            h: 0.4,
            fontSize: 14,
            color: ACCENT_COLOR,
            bold: true,
            valign: 'top'
        });

        // Add Text (using rich text for bolding)
        addTextSafely(slide, [
            { text: item.bold + " ", options: { color: TEXT_BOLD_COLOR, bold: true, fontSize: 10 } },
            { text: item.text, options: { color: TEXT_COLOR, fontSize: 10 } }
        ], {
            x: textX,
            y: currentY,
            w: textW,
            h: ITEM_HEIGHT - 0.1, // Slightly less than item height for padding
            lineSpacing: 18, // ~1.5 line height for 10pt font
            valign: 'top'
        });

        // Increment Y position for the next item
        currentY += ITEM_HEIGHT;
    });

    return slide;
}

function generateSlide3GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_3_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS (GENERIC)
    const SAFE_MARGIN = 0.3;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.2; // Adjusted for title divider
    const MAX_CONTENT_Y = 4.8;
    const SAFE_W = 10.0 - (2 * SAFE_MARGIN); // 9.4

    // Two-column layout constants
    const LEFT_COL_X = 0.5;
    const LEFT_COL_W = 3.5;
    const RIGHT_COL_X = 4.5;
    const RIGHT_COL_W = 5.0;

    // Color Palette (from HTML analysis)
    const BG_COLOR = '0a192f';
    const ACCENT_COLOR = '64ffda';
    const TEXT_COLOR = 'ccd6f6';
    const TEXT_STRONG_COLOR = 'a8b2d1';

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Title
    slide.addText("The Weaknesses of Our Current Security Posture", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: SAFE_W,
        h: TITLE_H,
        fontSize: 16, // Ultra-safe max title size
        color: ACCENT_COLOR,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.9, // Below title
        w: 1.2, // 120px equivalent
        h: 0.03, // 3px equivalent
        fill: { color: ACCENT_COLOR }
    });

    // --- Left Column (Visuals) ---
    const VISUAL_CENTER_X = LEFT_COL_X + (LEFT_COL_W / 2);
    const VISUAL_CENTER_Y = CONTENT_START_Y + (MAX_CONTENT_Y - CONTENT_START_Y) / 2;

    // Visual Label: "Perimeter Security"
    slide.addText("Perimeter Security", {
        x: LEFT_COL_X,
        y: VISUAL_CENTER_Y + 1.5, // Positioned below the castle
        w: LEFT_COL_W,
        h: 0.4,
        align: 'center',
        fontSize: 11,
        bold: true,
        color: ACCENT_COLOR,
        border: { type: 'solid', pt: 2, color: ACCENT_COLOR },
        margin: 4 // Internal padding
    });

    // --- Right Column (Text Content) ---
    const weaknessData = [
        { strong: "Increased Sophistication of Cyberattacks:", text: "Attackers are bypassing traditional perimeter defenses." },
        { strong: "Insider Threats:", text: "Malicious or negligent employees can compromise sensitive data." },
        { strong: "Complex IT Environment:", text: "Cloud adoption, remote work, and BYOD create new attack vectors." },
        { strong: "Compliance Requirements:", text: "Regulations like GDPR and CCPA demand stronger data protection measures." },
        { strong: "Lack of Visibility:", text: "Difficult to track user activity and identify suspicious behavior across the network." },
        { strong: "The Cost of Inaction:", text: "Breaches result in significant financial, reputational, and legal liabilities." }
    ];

    // Function to get font size based on content density
    function getUltraSafeFontSize(elementCount, baseSize) {
        let size = baseSize;
        if (elementCount > 5) size = 9;
        else if (elementCount > 3) size = 10;
        return Math.max(size, 8); // Never below 8px
    }

    const FONT_SIZE = getUltraSafeFontSize(weaknessData.length, 10);
    const LINE_HEIGHT = 0.5; // Increased for readability
    let currentY = CONTENT_START_Y;

    weaknessData.forEach(item => {
        // Check if adding this element would cause overflow
        if ((currentY + LINE_HEIGHT) > MAX_CONTENT_Y) {
            console.warn(`Skipping element to prevent overflow: ${item.strong}`);
            return;
        }

        // Icon: Recreated as a safe, basic shape (triangle in a circle)
        slide.addShape(pptx.shapes.OVAL, {
            x: RIGHT_COL_X,
            y: currentY + 0.05,
            w: 0.25,
            h: 0.25,
            line: { color: ACCENT_COLOR, width: 1.5 }
        });
        slide.addText("!", {
            x: RIGHT_COL_X,
            y: currentY + 0.05,
            w: 0.25,
            h: 0.25,
            align: 'center',
            valign: 'middle',
            color: ACCENT_COLOR,
            fontSize: 12,
            bold: true
        });

        // Text content
        const textOptions = {
            x: RIGHT_COL_X + 0.4,
            y: currentY,
            w: RIGHT_COL_W - 0.4,
            h: LINE_HEIGHT,
            fontSize: FONT_SIZE,
            color: TEXT_COLOR,
            lineSpacing: FONT_SIZE + 4 // Generous line spacing
        };

        slide.addText([
            { text: item.strong + " ", options: { color: TEXT_STRONG_COLOR, bold: true } },
            { text: item.text, options: { color: TEXT_COLOR, bold: false } }
        ], textOptions);

        currentY += LINE_HEIGHT;
    });

    return slide;
}

function generateSlide4GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_4_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer column widths

    // Two-column layout constants
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.5; // 55% of available width, rounded for safety
    const RIGHT_COL_X = 5.2; // Left col (0.3 + 4.5) + 0.4 gap
    const RIGHT_COL_W = 3.3; // 45% of available width, rounded for safety

    // Color Palette (from CSS)
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_BORDER = '1a3a6e';
    const COLOR_CARD_FILL = '1a2c46'; // Approximated from rgba(42, 68, 110, 0.3) on dark bg

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Main Title
    slide.addText("Zero Trust: Never Trust, Always Verify", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: CONTENT_WIDTH,
        h: 0.4,
        fontSize: 16,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2,
        h: 0.03,
        fill: { color: COLOR_PRIMARY_ACCENT },
    });

    // --- Left Column Content ---
    let currentY = CONTENT_START_Y;
    const leftContent = [
        { strong: "Identity-Centric Security:", text: "Verify every user and device before granting access." },
        { strong: "Microsegmentation:", text: "Isolate applications and data to limit the blast radius of a breach." },
        { strong: "Continuous Monitoring:", text: "Constantly monitor user activity, device health, and network traffic for suspicious behavior." },
        { strong: "Data-Centric Protection:", text: "Protect sensitive data at rest and in transit with encryption and DLP." },
        { strong: "Automation & Orchestration:", text: "Automate security tasks and workflows to improve efficiency and reduce response times." },
    ];

    const ICON_SIZE = 0.22;
    const ICON_MARGIN_RIGHT = 0.15;
    const TEXT_START_X = LEFT_COL_X + ICON_SIZE + ICON_MARGIN_RIGHT;
    const TEXT_WIDTH = LEFT_COL_W - ICON_SIZE - ICON_MARGIN_RIGHT;
    const ITEM_SPACING = 0.5;

    leftContent.forEach(item => {
        if (currentY + ITEM_SPACING > MAX_CONTENT_Y) return; // Overflow check

        // Icon (using a safe, basic shape as a placeholder)
        slide.addShape(pptx.shapes.OVAL, {
            x: LEFT_COL_X,
            y: currentY + 0.04, // Align vertically
            w: ICON_SIZE,
            h: ICON_SIZE,
            fill: { color: COLOR_PRIMARY_ACCENT, transparency: 30 },
            line: { color: COLOR_PRIMARY_ACCENT, width: 1 }
        });

        // List Item Text
        slide.addText([
            { text: item.strong + " ", options: { color: COLOR_TEXT_SECONDARY, bold: true } },
            { text: item.text, options: { color: COLOR_TEXT_PRIMARY } }
        ], {
            x: TEXT_START_X,
            y: currentY,
            w: TEXT_WIDTH,
            h: ITEM_SPACING - 0.1, // Reserve small gap
            fontSize: 9,
            lineSpacing: 12
        });

        currentY += ITEM_SPACING;
    });

    // Conclusion Text (at the bottom of the left column)
    const CONCLUSION_H = 0.6;
    const CONCLUSION_Y = MAX_CONTENT_Y - CONCLUSION_H;
    // Border line for conclusion
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X,
        y: CONCLUSION_Y,
        w: 0.03,
        h: CONCLUSION_H,
        fill: { color: COLOR_PRIMARY_ACCENT }
    });
    slide.addText([
        { text: "Why it Works: ", options: { color: COLOR_PRIMARY_ACCENT, bold: true } },
        { text: "Zero Trust eliminates implicit trust, making it significantly harder for attackers to gain access to sensitive data.", options: { color: COLOR_TEXT_SECONDARY } }
    ], {
        x: LEFT_COL_X + 0.2,
        y: CONCLUSION_Y,
        w: LEFT_COL_W - 0.2,
        h: CONCLUSION_H,
        fontSize: 9,
        lineSpacing: 13
    });

    // --- Right Column Content ---
    currentY = CONTENT_START_Y;
    const rightContent = [
        { title: "Verify Explicitly", desc: "Authenticate and authorize based on all available data points." },
        { title: "Least Privilege Access", desc: "Limit user access with just-in-time and just-enough-access (JIT/JEA)." },
        { title: "Assume Breach", desc: "Minimize blast radius and segment access. Verify all sessions are encrypted." },
    ];

    const CARD_H = 1.1;
    const CARD_SPACING = 0.2;
    const CARD_ICON_SIZE = 0.5;
    const CARD_TEXT_X_OFFSET = CARD_ICON_SIZE + 0.2;

    rightContent.forEach(card => {
        if (currentY + CARD_H > MAX_CONTENT_Y) return; // Overflow check

        // Diagram Card (using a rounded rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: RIGHT_COL_X,
            y: currentY,
            w: RIGHT_COL_W,
            h: CARD_H,
            fill: { color: COLOR_CARD_FILL },
            line: { color: COLOR_BORDER, width: 1 },
            rectRadius: 0.08
        });

        // Diagram Icon (using a safe shape)
        slide.addShape(pptx.shapes.OVAL, {
            x: RIGHT_COL_X + 0.2,
            y: currentY + (CARD_H - CARD_ICON_SIZE) / 2, // Center vertically
            w: CARD_ICON_SIZE,
            h: CARD_ICON_SIZE,
            fill: { color: COLOR_PRIMARY_ACCENT, transparency: 50 }
        });

        // Diagram Text
        slide.addText(card.title, {
            x: RIGHT_COL_X + CARD_TEXT_X_OFFSET,
            y: currentY + 0.2,
            w: RIGHT_COL_W - CARD_TEXT_X_OFFSET - 0.2,
            h: 0.3,
            fontSize: 10,
            color: COLOR_TEXT_PRIMARY,
            bold: true
        });
        slide.addText(card.desc, {
            x: RIGHT_COL_X + CARD_TEXT_X_OFFSET,
            y: currentY + 0.5,
            w: RIGHT_COL_W - CARD_TEXT_X_OFFSET - 0.2,
            h: 0.4,
            fontSize: 9,
            color: COLOR_TEXT_SECONDARY
        });

        currentY += CARD_H + CARD_SPACING;
    });

    return slide;
}

function generateSlide5GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_5_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE PALETTE & STYLES
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_SHAPE_FILL = '2a4365'; // Derived from rgba(42, 67, 101, 0.5)
    const COLOR_SHAPE_BORDER = '1d3b66';

    // ULTRA-SAFE LAYOUT CONSTANTS (GENERIC, NO SLIDE-SPECIFIC NAMES)
    const SAFE_MARGIN = 0.3;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.4;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Two-column layout dimensions
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 3.8;
    const RIGHT_COL_X = 4.5;
    const RIGHT_COL_W = 4.2; // Ends at 8.7, adjusted to 4.0 to be safe
    const SAFE_RIGHT_COL_W = 4.0; // Ends at 8.5 (ULTRA-SAFE)

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Add Title
    slide.addText("Building a Secure Foundation with Cloud Technologies", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        valign: 'top'
    });

    // Add Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.75,
        w: 1.2, // 120px equivalent
        h: 0.03, // 3px equivalent
        fill: { color: COLOR_PRIMARY_ACCENT }
    });

    // --- LEFT COLUMN: Architecture Diagram ---
    const architectureLayers = [
        { text: "Identity:", detail: "Azure AD Conditional Access" },
        { text: "Application:", detail: "Web Apps / Microservices" },
        { text: "Data:", detail: "S3 Buckets / Azure Blob Storage" },
        { text: "Compute:", detail: "EC2 Instances / Azure VMs" },
        { text: "Network:", detail: "Azure Virtual Network / NSGs" },
        { text: "Infrastructure:", detail: "Cloud Provider" }
    ];

    let currentY = CONTENT_START_Y;
    const layerHeight = 0.55;
    const layerSpacing = 0.1;

    architectureLayers.forEach(layer => {
        if ((currentY + layerHeight) > MAX_CONTENT_Y) return; // Prevent vertical overflow

        // Layer Box (Rounded Rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: LEFT_COL_X,
            y: currentY,
            w: LEFT_COL_W,
            h: layerHeight,
            fill: { color: COLOR_SHAPE_FILL },
            line: { color: COLOR_SHAPE_BORDER, width: 1 },
            rectRadius: 0.1
        });

        // Layer Text
        slide.addText([
            { text: layer.text, options: { bold: true, color: COLOR_TEXT_SECONDARY, fontSize: 9 } },
            { text: ` ${layer.detail}`, options: { color: COLOR_TEXT_PRIMARY, fontSize: 9 } }
        ], {
            x: LEFT_COL_X + 0.15,
            y: currentY,
            w: LEFT_COL_W - 0.6, // Leave space for icon
            h: layerHeight,
            valign: 'middle'
        });

        // Lock Icon (recreated with basic shapes for safety)
        const iconX = LEFT_COL_X + LEFT_COL_W - 0.45;
        const iconY = currentY + (layerHeight / 2) - 0.125;
        slide.addShape(pptx.shapes.OVAL, {
            x: iconX,
            y: iconY,
            w: 0.25,
            h: 0.25,
            line: { color: COLOR_PRIMARY_ACCENT, width: 1.5 }
        });

        currentY += layerHeight + layerSpacing;
    });

    // --- RIGHT COLUMN: Technology Details ---
    const techItems = [
        { title: "Identity & Access Management (IAM)", details: "Azure AD Conditional Access + Passwordless Auth + Behavioral Biometrics" },
        { title: "Device Security & Endpoint Management", details: "Microsoft Endpoint Manager (Intune) + DPA + Remote Browser Isolation" },
        { title: "Network Security & Microsegmentation", details: "Azure VNets/NSGs + Cloudflare ZTNA + AI Anomaly Detection" },
        { title: "Data Security & Protection", details: "Microsoft Purview + Cloud-Native Key Management (KMS)" },
        { title: "Automation & Orchestration", details: "Azure Logic Apps + Ansible / Terraform (IaC)" }
    ];

    currentY = CONTENT_START_Y;
    const itemSpacing = 0.15;

    techItems.forEach(item => {
        const titleHeight = 0.25;
        const detailHeight = 0.4;
        const totalItemHeight = titleHeight + detailHeight + itemSpacing;

        if ((currentY + totalItemHeight) > MAX_CONTENT_Y) return; // Prevent vertical overflow

        // Tech Item Title
        slide.addText(item.title, {
            x: RIGHT_COL_X,
            y: currentY,
            w: SAFE_RIGHT_COL_W,
            h: titleHeight,
            fontSize: 10,
            color: COLOR_PRIMARY_ACCENT,
            bold: true
        });

        // Tech Item Details
        slide.addText(item.details, {
            x: RIGHT_COL_X,
            y: currentY + titleHeight,
            w: SAFE_RIGHT_COL_W,
            h: detailHeight,
            fontSize: 9,
            color: COLOR_TEXT_SECONDARY,
            valign: 'top'
        });

        currentY += totalItemHeight;
    });

    return slide;
}

function generateSlide6GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_6_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & COLOR CONSTANTS
    const SAFE_MARGIN = 0.3;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer values
    const MAX_CONTENT_Y = 4.8;

    // Colors from CSS analysis
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_TEXT_TERTIARY = '8892b0';

    // Layout constants for two columns
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.5;
    const RIGHT_COL_X = 5.2;
    const RIGHT_COL_W = 3.3; // Ends at 8.5 (SAFE)

    // Font size constants
    const FONT_SIZE_TITLE = 16;
    const FONT_SIZE_HEADING = 10;
    const FONT_SIZE_BODY = 9;
    const FONT_SIZE_SUB_BODY = 8;

    // Vertical positioning
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const DIVIDER_Y = TITLE_Y + TITLE_H;
    const CONTENT_START_Y = DIVIDER_Y + 0.4; // Start content below divider

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Add Title
    slide.addText("Assembling the Right Team for Success", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: CONTENT_WIDTH,
        h: TITLE_H,
        fontSize: FONT_SIZE_TITLE,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        align: 'left'
    });

    // Add Title Divider (using a shape)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: DIVIDER_Y,
        w: 1.2, // 120px equivalent
        h: 0.04, // 4px equivalent
        fill: { color: COLOR_PRIMARY_ACCENT }
    });

    // --- Left Column: Team List ---
    const teamListData = [
        {
            icon: 'shield',
            title: "Dedicated Security Team",
            text: "Requires experienced security engineers, architects, and analysts to lead the implementation."
        },
        {
            icon: 'cloud',
            title: "Cloud Expertise",
            text: "Personnel with deep knowledge of chosen cloud platforms (Azure, AWS, or Google Cloud)."
        },
        {
            icon: 'id',
            title: "IAM Specialists",
            text: "Experts in identity and access management solutions are crucial for the core of Zero Trust."
        },
        {
            icon: 'presentation',
            title: "Training & Awareness Programs",
            subItems: [
                "Educate all employees on Zero Trust principles and secure work practices.",
                "Provide in-depth technical training for IT staff on new technologies."
            ]
        },
        {
            icon: 'management',
            title: "Change Management",
            text: "A dedicated team to manage the cultural and operational transition, ensuring user adoption."
        }
    ];

    let currentY = CONTENT_START_Y;
    const ICON_SIZE = 0.25;
    const ICON_TEXT_GAP = 0.1;
    const ITEM_SPACING = 0.2; // Space between major list items

    // Function to add an icon shape safely
    function addIcon(iconType, x, y) {
        const iconOptions = {
            x: x,
            y: y + 0.05, // Align icon slightly lower
            w: ICON_SIZE,
            h: ICON_SIZE,
            fill: { color: COLOR_PRIMARY_ACCENT }
        };
        // Use valid PptxGenJS shapes to represent icons
        switch (iconType) {
            case 'shield':
                // Using a rounded rectangle as a stand-in for a shield
                slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, { ...iconOptions, rectRadius: 0.1 });
                break;
            case 'cloud':
                // Using an oval as a stand-in for a cloud
                slide.addShape(pptx.shapes.OVAL, iconOptions);
                break;
            case 'id':
            case 'presentation':
            case 'management':
            default:
                // Default to a simple rectangle for other icons
                slide.addShape(pptx.shapes.RECTANGLE, iconOptions);
                break;
        }
    }

    teamListData.forEach(item => {
        if (currentY > MAX_CONTENT_Y - 0.5) return; // Stop if approaching vertical limit

        const textX = LEFT_COL_X + ICON_SIZE + ICON_TEXT_GAP;
        const textW = LEFT_COL_W - ICON_SIZE - ICON_TEXT_GAP;

        // Add Icon
        addIcon(item.icon, LEFT_COL_X, currentY);

        // Add Title
        slide.addText(item.title, {
            x: textX,
            y: currentY,
            w: textW,
            h: 0.2,
            fontSize: FONT_SIZE_HEADING,
            color: COLOR_TEXT_SECONDARY,
            bold: true
        });
        currentY += 0.2;

        // Add Body Text or Sub-list
        if (item.text) {
            slide.addText(item.text, {
                x: textX,
                y: currentY,
                w: textW,
                h: 0.3,
                fontSize: FONT_SIZE_BODY,
                color: COLOR_TEXT_PRIMARY
            });
            currentY += 0.3 + ITEM_SPACING;
        } else if (item.subItems) {
            item.subItems.forEach(subItem => {
                if (currentY > MAX_CONTENT_Y - 0.3) return;
                // Add bullet point and text for sub-items
                slide.addText(`• ${subItem}`, {
                    x: textX + 0.2, // Indent sub-items
                    y: currentY,
                    w: textW - 0.2,
                    h: 0.3,
                    fontSize: FONT_SIZE_SUB_BODY,
                    color: COLOR_TEXT_TERTIARY
                });
                currentY += 0.2;
            });
            currentY += ITEM_SPACING;
        }
    });

    // --- Right Column: Main Icon ---
    // Recreate the "team" icon using basic, valid shapes for an abstract representation.
    const iconCenterX = RIGHT_COL_X + RIGHT_COL_W / 2;
    const iconCenterY = CONTENT_START_Y + (MAX_CONTENT_Y - CONTENT_START_Y) / 2;
    const iconFill = { color: COLOR_PRIMARY_ACCENT, transparency: 80 }; // opacity: 0.2

    // Central "body"
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - 0.5, y: iconCenterY - 0.2, w: 1.0, h: 1.0, fill: iconFill
    });
    // "Head"
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - 0.25, y: iconCenterY - 0.6, w: 0.5, h: 0.5, fill: iconFill
    });
    // Two side "members"
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - 1.2, y: iconCenterY, w: 0.8, h: 0.8, fill: iconFill
    });
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX + 0.4, y: iconCenterY, w: 0.8, h: 0.8, fill: iconFill
    });

    return slide;
}

function generateSlide7GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_7_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer values

    // Color Palette (from HTML analysis)
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_TEXT_ON_ACCENT = '0a192f';

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Main Title
    slide.addText("A Strategic Roadmap to Zero Trust", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 24, // Title can be larger, safely contained
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.5, // 120px is ~1.5 inches
        h: 0.04,
        fill: { color: COLOR_PRIMARY_ACCENT }
    });

    // --- Gantt Chart Layout ---
    const GANTT_START_Y = 1.4;
    const PHASE_COL_X = SAFE_MARGIN;
    const PHASE_COL_W = 2.5;
    const TIMELINE_COL_X = PHASE_COL_X + PHASE_COL_W + 0.1; // 2.9
    const TIMELINE_COL_W = SLIDE_WIDTH - TIMELINE_COL_X - SAFE_MARGIN; // 10 - 2.9 - 0.3 = 6.8
    const ROW_HEIGHT = 0.7;

    // Timeline Header Text
    const timelineLabels = ["3 Months", "6 Months", "9 Months", "12 Months"];
    const timelineLabelWidth = TIMELINE_COL_W / timelineLabels.length; // 6.8 / 4 = 1.7
    timelineLabels.forEach((label, index) => {
        slide.addText(label, {
            x: TIMELINE_COL_X + (index * timelineLabelWidth),
            y: GANTT_START_Y,
            w: timelineLabelWidth,
            h: 0.2,
            fontSize: 9,
            color: COLOR_TEXT_SECONDARY,
            align: 'center'
        });
    });

    // Timeline Header Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: TIMELINE_COL_X,
        y: GANTT_START_Y + 0.25,
        w: TIMELINE_COL_W,
        h: 0,
        line: { color: '4A5568', width: 1 } // Mapped from border-gray-700
    });

    // Gantt Chart Data
    const phases = [
        { title: "Phase 1: Assessment & Planning", desc: "Conduct security assessment, define policies, select technologies.", barWidth: 1/4, barOpacity: 'CC' },
        { title: "Phase 2: Identity & Access", desc: "Implement passwordless auth, conditional access, behavioral biometrics.", barWidth: 2/4, barOpacity: 'B3' },
        { title: "Phase 3: Device Security", desc: "Deploy endpoint management, enforce device posture, implement RBI.", barWidth: 3/4, barOpacity: '99' },
        { title: "Phase 4: Network Microsegmentation", desc: "Implement SDP, configure security groups, deploy AI anomaly detection.", barWidth: 4/4, barOpacity: '80' },
        { title: "Phase 5: Data Security & Monitoring", desc: "Implement DLP, encrypt data, and continuously monitor environment.", barWidth: 4/4, barOpacity: '66', text: "Ongoing" }
    ];

    let currentY = GANTT_START_Y + 0.4;

    phases.forEach(phase => {
        // This check guarantees no vertical overflow
        if ((currentY + ROW_HEIGHT) > MAX_CONTENT_Y) {
            console.warn(`Skipping phase "${phase.title}" to prevent vertical overflow.`);
            return;
        }

        // Left Column: Phase Title and Description
        slide.addText(phase.title, {
            x: PHASE_COL_X,
            y: currentY,
            w: PHASE_COL_W,
            h: 0.25,
            fontSize: 11,
            color: COLOR_TEXT_SECONDARY,
            bold: true
        });
        slide.addText(phase.desc, {
            x: PHASE_COL_X,
            y: currentY + 0.2,
            w: PHASE_COL_W,
            h: 0.4,
            fontSize: 8,
            color: COLOR_TEXT_PRIMARY
        });

        // Right Column: Timeline Bar
        const barW = TIMELINE_COL_W * phase.barWidth;
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: TIMELINE_COL_X,
            y: currentY + 0.1,
            w: barW,
            h: 0.4,
            fill: { color: COLOR_PRIMARY_ACCENT, transparency: parseInt(phase.barOpacity, 16) * 100 / 255 },
            rectRadius: 0.1
        });

        // Bar Text (e.g., "Ongoing")
        if (phase.text) {
            slide.addText(phase.text, {
                x: TIMELINE_COL_X + 0.1,
                y: currentY + 0.1,
                w: barW - 0.2,
                h: 0.4,
                fontSize: 9,
                color: COLOR_TEXT_ON_ACCENT,
                bold: true,
                align: 'left',
                valign: 'middle'
            });
        }

        currentY += ROW_HEIGHT;
    });

    return slide;
}

function generateSlide8GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_8_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & STYLE CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Colors from HTML analysis
    const BG_COLOR = '0a192f';
    const ACCENT_COLOR = '64ffda';
    const TEXT_COLOR_PRIMARY = 'ccd6f6';
    const TEXT_COLOR_SECONDARY = 'a8b2d1';
    const BORDER_COLOR = '1a3a6e';
    const TABLE_HEADER_FILL = '1a3a6e'; // Using a solid, darker version for contrast

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Add Title
    slide.addText("Investing in a Secure Future", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16,
        color: ACCENT_COLOR,
        bold: true,
        align: 'left'
    });

    // Add Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.5, // Safe width
        h: 0.04,
        fill: { color: ACCENT_COLOR }
    });

    // --- TWO-COLUMN LAYOUT ---

    // LEFT COLUMN: Cost Table
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.8;

    const tableRows = [
        // Header Row
        [
            { text: "Category", options: { bold: true, fontSize: 9, color: TEXT_COLOR_SECONDARY, fill: TABLE_HEADER_FILL } },
            { text: "Estimated Cost", options: { bold: true, fontSize: 9, color: TEXT_COLOR_SECONDARY, fill: TABLE_HEADER_FILL } },
            { text: "Notes", options: { bold: true, fontSize: 9, color: TEXT_COLOR_SECONDARY, fill: TABLE_HEADER_FILL } }
        ],
        // Data Rows
        ["Software & Cloud Services", "$50,000/year", "Azure AD, Intune, Cloudflare ZTNA..."],
        ["Hardware", "$5,000", "Minimal, depends on existing infra."],
        ["Personnel Costs", "$150,000/year", "Security team, cloud experts, training"],
        ["Training Costs", "$10,000", "Employee and technical training"],
        ["Consulting Fees", "$20,000", "If using external consultants"],
        // Footer Row (styled differently)
        [
            { text: "Total Estimated Cost", options: { bold: true, fontSize: 9, color: ACCENT_COLOR } },
            { text: "$235,000", options: { bold: true, fontSize: 9, color: ACCENT_COLOR } },
            { text: "", options: {} }
        ]
    ];

    slide.addTable(tableRows, {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        colW: [1.8, 1.2, 1.8], // Total width = 4.8
        rowH: 0.3, // Ultra-safe row height
        fontSize: 8,
        color: TEXT_COLOR_PRIMARY,
        border: { type: 'solid', pt: 1, color: BORDER_COLOR },
        valign: 'middle'
    });

    // RIGHT COLUMN: ROI List
    const RIGHT_COL_X = 5.4; // LEFT_COL_X + LEFT_COL_W + 0.3 gutter
    const RIGHT_COL_W = 3.4;

    // Vertical Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: 5.25, // Centered in the gutter
        y: CONTENT_START_Y,
        w: 0,
        h: 3.8,
        line: { color: BORDER_COLOR, width: 2 }
    });

    // ROI Title
    slide.addText("Return on Investment (ROI)", {
        x: RIGHT_COL_X,
        y: CONTENT_START_Y,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: 12,
        color: TEXT_COLOR_SECONDARY,
        bold: true
    });

    // ROI Content
    const roiItems = [
        { strong: "Reduced Risk of Data Breaches:", text: "Quantify savings from preventing costly breaches." },
        { strong: "Improved Compliance:", text: "Avoid fines and penalties from non-compliance." },
        { strong: "Increased Productivity:", text: "Streamlined access and reduced security-related downtime." },
        { strong: "Enhanced Reputation:", text: "Maintain customer trust and protect brand value." }
    ];

    let currentY = CONTENT_START_Y + 0.4; // Start below ROI title
    const ICON_SIZE = 0.2;
    const ICON_TEXT_GAP = 0.1;
    const ITEM_SPACING = 0.7; // Generous spacing for readability

    roiItems.forEach(item => {
        // Check for vertical overflow before adding new item
        if (currentY + ITEM_SPACING > MAX_CONTENT_Y) return;

        // Add text-based icon for maximum safety and compatibility
        slide.addText("✓", {
            x: RIGHT_COL_X,
            y: currentY,
            w: ICON_SIZE,
            h: ICON_SIZE,
            fontSize: 14,
            color: ACCENT_COLOR,
            bold: true,
            valign: 'top'
        });

        // Add the text content next to the icon
        slide.addText([
            { text: item.strong, options: { fontSize: 9, color: TEXT_COLOR_SECONDARY, bold: true, breakLine: true } },
            { text: item.text, options: { fontSize: 8, color: TEXT_COLOR_PRIMARY } }
        ], {
            x: RIGHT_COL_X + ICON_SIZE + ICON_TEXT_GAP,
            y: currentY,
            w: RIGHT_COL_W - (ICON_SIZE + ICON_TEXT_GAP),
            h: ITEM_SPACING - 0.1, // Height for the text block
            lineSpacing: 12 // Ultra-safe line spacing in points
        });

        currentY += ITEM_SPACING;
    });

    return slide;
}

// Main presentation creation function
function createPresentation() {
    const pptx = new PptxGenJS();

    console.log('Creating multi-slide presentation with 7 slides...');

    generateSlide2AgendaUltraSafeAutoConvert(pptx);
    generateSlide3GeneralUltraSafeAutoConvert(pptx);
    generateSlide4GeneralUltraSafeAutoConvert(pptx);
    generateSlide5GeneralUltraSafeAutoConvert(pptx);
    generateSlide6GeneralUltraSafeAutoConvert(pptx);
    generateSlide7GeneralUltraSafeAutoConvert(pptx);
    generateSlide8GeneralUltraSafeAutoConvert(pptx);

    console.log(`✅ Created presentation with ${pptx.slides.length} slides`);

    return pptx.writeFile({ fileName: 'my_presentation.pptx' });
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('✅ Multi-slide PowerPoint generated successfully!');
        console.log('📄 File saved as: my_presentation.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('❌ Error generating presentation:', error);
        console.error(error.stack);
        process.exit(1);
    });
