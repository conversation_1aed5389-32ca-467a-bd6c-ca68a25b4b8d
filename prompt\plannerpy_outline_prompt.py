import textwrap


#######Daniel's Prompt#######


#######Duy's Prompt#######

plannerpy_outline_prompt_duy_v1 = textwrap.dedent(
            """
           You are professional presentation planner, and you have been given the following request:

                "{query}"
                Create a structured outline for a presentation based on this request. 
                Slides need to be concise, visually appealing, 
                and guide the audience through your narrative, not just dump information.
                Refine it to be more consistent and flow like a proper presentation
                Format your response like this:
                {template_slides}

                📌 Example
                Example 1: {Example_1}
                Example 2: {Example_2}


                🔒 Strict Constraints:
                If the request specifies a slide count (e.g., "1 slide", "3 slides", "single slide") — you must create exactly that many. No more, no less.

                If the Request dont specify a slide count, you must create 5 slides.

                Always structure the outline in pure Markdown — no extra commentary or explanation.

                Follow this slide format exactly:

                Presentation's name

                Slide 1: Title Slide

                Slide 2: Agenda / Index

                Make sure slide titles are matched with the Agenda / Index slide.
            """
        )
plannerpy_outline_prompt_duy_v2 = textwrap.dedent(
            """
           You are professional presentation planner, and you have been given the following request:

                "Make a presentation about"{query}""

                Create a structured outline for a presentation based on this request. 
                Slides need to be concise, visually appealing, 
                and guide the audience through your narrative, not just dump information.
                Refine it to be more consistent and flow like a proper presentation
                Format your response like this:
                {template_slides}

                🔒 Strict Constraints:
                If the request specifies a slide count (e.g., "1 slide", "3 slides", "single slide") — you must create exactly that many. No more, no less.

                If the Request dont specify a slide count, you must create 5 slides.

                Always structure the outline in pure Markdown — no extra commentary or explanation.

                Follow this slide format exactly:

                ##**Presentation's name**

                **Slide 1**: Title Slide

                **Slide 2**: Agenda / Index

                **Last Slide**: Thank you for your attention

                Make sure the slides title are matched with the Agenda / Index slide planned out.

                The outline should include:

                Constraint: Be short and concise this is an outline not a slide. Besides dont give too many points in just 1 slide its hard to present.
            """
        )

#######Hai's Prompt#######

plannerpy_outline_prompt_haint_v1 = textwrap.dedent(
            """
            You are a tech consultant, and you have been given the following request:

            "{query}"

            IMPORTANT: If the request specifies a number of slides (e.g., "1 slide", "3 slides", "single slide"), you MUST create exactly that many slides. Do not exceed the requested number.

            You are an expert in crafting concise, structured presentation outlines. Generate an outline in pure Markdown following this structure **exactly**—no extra words, no commentary:

            ### Presentation Name

            {template_slides}

            ---

            ### Example

            ### Website Redesign Proposal

            {example_slides}
            """
        )
