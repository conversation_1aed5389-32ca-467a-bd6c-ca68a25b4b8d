const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. ULTRA-SAFE CONSTANTS & CONFIGURATION
    // =======================================================================

    // Slide Dimensions & Safe Area
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Layout Coordinates (2x2 Grid)
    const COL_GAP = 0.4;
    const ROW_GAP = 0.4;
    const CARD_W = (SLIDE_WIDTH - (2 * SAFE_MARGIN) - COL_GAP) / 2; // (10 - 0.6 - 0.4) / 2 = 4.5
    const CARD_H = (MAX_CONTENT_Y - CONTENT_START_Y - ROW_GAP) / 2; // (4.8 - 1.0 - 0.4) / 2 = 1.7

    const CARD_POSITIONS = [
        { x: SAFE_MARGIN, y: CONTENT_START_Y }, // Top-Left
        { x: SAFE_MARGIN + CARD_W + COL_GAP, y: CONTENT_START_Y }, // Top-Right
        { x: SAFE_MARGIN, y: CONTENT_START_Y + CARD_H + ROW_GAP }, // Bottom-Left
        { x: SAFE_MARGIN + CARD_W + COL_GAP, y: CONTENT_START_Y + CARD_H + ROW_GAP } // Bottom-Right
    ];

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        headerText: '4A5568', // gray-700
        cardBackground: 'F8F9FA',
        cardBorder: 'DEE2E6',
        cardBodyText: '495057',
        cardServiceText: '6C757D',
        benefitBoxBg: 'E9F5FF',
        benefitBoxText: '005A9E',
        footerBg: 'F1F5F9', // gray-100
        footerText: '4B5563', // gray-600
        accent: {
            compute: '4285F4',
            data: 'DB4437',
            ai: 'F4B400',
            network: '0F9D58'
        }
    };

    // Font Sizes (Ultra-Safe)
    const FONT_SIZES = {
        mainTitle: 16,
        cardTitle: 11,
        cardBody: 9,
        cardService: 8,
        benefit: 8,
        footer: 8
    };

    // Image URLs (extracted from HTML)
    const IMAGE_URLS = {
        gcpLogo: 'https://download.logo.wine/logo/Google_Cloud_Platform/Google_Cloud_Platform-Logo.wine.png',
        computeIcon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Compute-Engine.svg',
        databaseIcon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Cloud-SQL.svg',
        aiIcon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Vertex-AI.svg',
        networkIcon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Virtual-Private-Cloud.svg'
    };

    // =======================================================================
    // 2. SLIDE BACKGROUND & HEADER
    // =======================================================================

    slide.background = { color: COLORS.background };

    // Header Background Shape (simulating the border-b)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: 0, w: SLIDE_WIDTH, h: 0.9,
        fill: { color: COLORS.background },
        line: { color: COLORS.cardBorder, width: 1 }
    });

    // GCP Logo Image
    slide.addImage({
        path: IMAGE_URLS.gcpLogo,
        x: SAFE_MARGIN, y: SAFE_MARGIN, w: 1.5, h: 0.4,
        sizing: { type: 'contain', w: 1.5, h: 0.4 }
    });

    // Main Title Text
    slide.addText('GCP: A Comprehensive Suite of Services', {
        x: SAFE_MARGIN + 1.5 + 0.2, y: SAFE_MARGIN, w: SLIDE_WIDTH - (SAFE_MARGIN * 2) - 1.7, h: 0.4,
        fontSize: FONT_SIZES.mainTitle,
        color: COLORS.headerText,
        bold: true,
        valign: 'middle'
    });

    // =======================================================================
    // 3. SERVICE CARD DATA & RENDERING FUNCTION
    // =======================================================================

    const serviceCardsData = [
        {
            icon: IMAGE_URLS.computeIcon,
            title: 'Compute',
            accentColor: COLORS.accent.compute,
            body: 'Flexible and scalable compute options for running virtual machines, containers, and serverless applications.',
            services: 'Services: Compute Engine, Cloud Run, Kubernetes Engine (GKE)',
            benefit: 'Benefit: Cost-effective, high-performance, and easy to manage.'
        },
        {
            icon: IMAGE_URLS.databaseIcon,
            title: 'Data Storage & Databases',
            accentColor: COLORS.accent.data,
            body: 'Secure and reliable storage solutions for all types of data, from structured databases to unstructured objects.',
            services: 'Services: Cloud Storage, Cloud SQL, Cloud Spanner, BigQuery',
            benefit: 'Benefit: Highly durable, scalable, and optimized for analytics.'
        },
        {
            icon: IMAGE_URLS.aiIcon,
            title: 'AI & Machine Learning',
            accentColor: COLORS.accent.ai,
            body: 'Powerful AI and ML tools for building intelligent applications and gaining insights from data.',
            services: 'Services: Vertex AI, Vision API, Natural Language API',
            benefit: 'Benefit: Accelerate innovation, automate tasks, and improve decision-making.'
        },
        {
            icon: IMAGE_URLS.networkIcon,
            title: 'Networking',
            accentColor: COLORS.accent.network,
            body: 'Secure and reliable networking infrastructure for connecting your applications and users globally.',
            services: 'Services: Virtual Private Cloud (VPC), Cloud Load Balancing, Cloud CDN',
            benefit: 'Benefit: High performance, low latency, and enhanced security.'
        }
    ];

    // Reusable function to create a service card, ensuring consistency and safety
    function createServiceCard(slide, cardData, position) {
        const { icon, title, accentColor, body, services, benefit } = cardData;
        const { x, y } = position;

        // Card container shape
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: x, y: y, w: CARD_W, h: CARD_H,
            fill: { color: COLORS.cardBackground },
            line: { color: COLORS.cardBorder, width: 1 }
        });

        // Accent line on the left
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: x, y: y, w: 0.05, h: CARD_H,
            fill: { color: accentColor }
        });

        // --- Card Header ---
        const headerH = 0.4;
        const iconSize = 0.3;
        const headerPadding = 0.15;

        slide.addImage({
            path: icon,
            x: x + headerPadding, y: y + (headerH - iconSize) / 2, w: iconSize, h: iconSize,
            sizing: { type: 'contain', w: iconSize, h: iconSize }
        });

        slide.addText(title, {
            x: x + headerPadding + iconSize + 0.1, y: y, w: CARD_W - (headerPadding * 2) - iconSize - 0.1, h: headerH,
            fontSize: FONT_SIZES.cardTitle,
            color: '14213D',
            bold: true,
            valign: 'middle'
        });

        // --- Card Body ---
        const bodyStartY = y + headerH + 0.1;
        const bodyPadding = 0.15;
        const bodyW = CARD_W - (bodyPadding * 2);
        let currentY = bodyStartY;

        // Main description
        slide.addText(body, {
            x: x + bodyPadding, y: currentY, w: bodyW, h: 0.5,
            fontSize: FONT_SIZES.cardBody,
            color: COLORS.cardBodyText
        });
        currentY += 0.5;

        // Services list
        slide.addText(services, {
            x: x + bodyPadding, y: currentY, w: bodyW, h: 0.3,
            fontSize: FONT_SIZES.cardService,
            color: COLORS.cardServiceText,
            bold: true // Simulates <strong>
        });
        currentY += 0.3;

        // --- Benefit Box (at the bottom of the card) ---
        const benefitBoxH = 0.35;
        const benefitBoxY = y + CARD_H - benefitBoxH - 0.05; // Positioned at the bottom

        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: x + bodyPadding, y: benefitBoxY, w: bodyW, h: benefitBoxH,
            fill: { color: COLORS.benefitBoxBg },
            rectRadius: 0.05
        });

        slide.addText(benefit, {
            x: x + bodyPadding + 0.1, y: benefitBoxY, w: bodyW - 0.2, h: benefitBoxH,
            fontSize: FONT_SIZES.benefit,
            color: COLORS.benefitBoxText,
            bold: true, // Simulates <strong>
            valign: 'middle'
        });
    }

    // Loop through data and positions to create the 4 cards
    serviceCardsData.forEach((data, index) => {
        createServiceCard(slide, data, CARD_POSITIONS[index]);
    });

    // =======================================================================
    // 4. FOOTER
    // =======================================================================

    const footerY = SLIDE_HEIGHT - 0.5;
    const footerH = 0.5;

    // Footer background shape
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: footerY, w: SLIDE_WIDTH, h: footerH,
        fill: { color: COLORS.footerBg }
    });

    // Footer text (left)
    slide.addText('Unlocking Innovation with Google Cloud', {
        x: SAFE_MARGIN, y: footerY, w: 4.0, h: footerH,
        fontSize: FONT_SIZES.footer,
        color: COLORS.footerText,
        bold: true,
        valign: 'middle'
    });

    // Footer text (right)
    slide.addText('Slide 4', {
        x: SLIDE_WIDTH - SAFE_MARGIN - 4.0, y: footerY, w: 4.0, h: footerH,
        fontSize: FONT_SIZES.footer,
        color: COLORS.footerText,
        bold: true,
        valign: 'middle',
        align: 'right'
    });

    // =======================================================================
    // 5. WRITE FILE
    // =======================================================================

    return pptx.writeFile({ fileName: 'generated/presentations/slide4_general_gcp.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
