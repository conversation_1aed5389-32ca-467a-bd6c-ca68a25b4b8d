<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Required Human Resources</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Roboto', sans-serif;
            background-color: #111827; /* A darker gray for contrast */
        }
        .slide-container {
            width: 1280px;
            height: 720px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
        }
        .slide-content {
            width: 100%;
            height: 100%;
            padding: 40px 60px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            background-color: #0d1117; /* GitHub Dark-like background */
        }
        .slide-title {
            font-size: 2.5em; /* Slightly smaller to accommodate more content */
            font-weight: 700;
            color: #c9d1d9; /* Light gray text */
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .logo {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.75rem; /* Compact font size */
            line-height: 1.2;
            color: #c9d1d9;
        }
        caption {
            font-size: 0.8rem;
            color: #8b949e;
            margin-bottom: 8px;
            text-align: left;
            caption-side: bottom;
        }
        thead th {
            position: sticky;
            top: 0;
            background-color: #161b22;
            color: #8b949e;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-weight: 500;
            padding: 10px 12px;
            text-align: left;
            border-bottom: 2px solid #30363d;
        }
        tbody td {
            padding: 8px 12px;
            border-bottom: 1px solid #21262d;
            vertical-align: top;
        }
        tbody tr:nth-child(even) {
            background-color: #161b22;
        }
        tbody tr:hover {
            background-color: #1f242c;
        }
        .text-right-align {
            text-align: right;
        }
        .insights-container {
            width: 100%;
            margin-top: 15px;
            padding: 15px;
            background-color: #161b22;
            border-radius: 6px;
            border: 1px solid #30363d;
        }
        .insights-title {
            font-size: 1.1em;
            font-weight: 500;
            color: #8b949e;
            margin-bottom: 10px;
        }
        .insights-list {
            list-style-position: inside;
            padding-left: 5px;
            color: #c9d1d9;
            font-size: 0.85em;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px 20px;
        }
    </style>
</head>
<body>

<div class="slide-container">
    <div class="slide-content">
        <h1 class="slide-title">
            <img src="https://storage.slide-gen.com/logo/aws.svg" class="logo" alt="AWS Logo"/>
            Required Human Resources
        </h1>
        
        <div class="table-wrapper" style="height: 520px; overflow-y: auto; width: 100%;">
            <table>
                <colgroup>
                    <col style="width: 16%;">
                    <col style="width: 38%;">
                    <col style="width: 9%;">
                    <col style="width: 12%;">
                    <col style="width: 12%;">
                    <col style="width: 13%;">
                </colgroup>
                <thead>
                    <tr>
                        <th>Role</th>
                        <th>Key Responsibilities</th>
                        <th>Headcount</th>
                        <th>Seniority Mix</th>
                        <th>Phase Focus</th>
                        <th>Key Skills/Certs</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>Project Manager</td><td>Program governance, RAID management, timeline & budget control, cross-team orchestration</td><td class="text-right-align">1</td><td>1 Senior</td><td>All phases</td><td>PMP/Agile</td></tr>
                    <tr><td>Cloud Architect</td><td>Landing zone, reference architecture, networking, security, resiliency/DR patterns</td><td class="text-right-align">1–2</td><td>1 Principal</td><td>Discovery</td><td>AWS SA Pro</td></tr>
                    <tr><td>DevOps Engineer</td><td>CI/CD pipelines, IaC, environment automation, container build/deploy, observability</td><td class="text-right-align">2–3</td><td>1 Sr + 1–2 Mid</td><td>Build</td><td>Terraform, EKS</td></tr>
                    <tr><td>Java/Python Developer</td><td>Refactor/rewrite services, API design, integration adapters, performance tuning</td><td class="text-right-align">4–6</td><td>2 Principal</td><td>Refactor/Build</td><td>Spring Boot, REST</td></tr>
                    <tr><td>COBOL Specialist</td><td>Reverse-engineer logic, map interfaces & batch jobs, data layout translation</td><td class="text-right-align">2–3</td><td>1 Lead + 1-2 Sr</td><td>Discovery</td><td>COBOL, JCL, CICS</td></tr>
                    <tr><td>QA Engineer</td><td>Test strategy, functional & regression automation, performance testing, data validation</td><td class="text-right-align">2–4</td><td>1 Lead + 1–3 Eng</td><td>Test, UAT</td><td>Selenium, JMeter</td></tr>
                    <tr><td>Database Administrator</td><td>Schema modernization, performance tuning, migration, backup/restore, HA/DR</td><td class="text-right-align">1–2</td><td>1 Lead</td><td>Migration</td><td>RDS, DMS</td></tr>
                    <tr><td>Security Engineer</td><td>Threat modeling, IAM/KMS design, secrets management, control mapping (SOX/PCI)</td><td class="text-right-align">1–2</td><td>1 Senior</td><td>Design</td><td>IAM, KMS, GuardDuty</td></tr>
                    <tr><td>Data Migration Specialist</td><td>ETL/CDC design, data quality & reconciliation, migration rehearsal, cutover planning</td><td class="text-right-align">1–2</td><td>1 Lead</td><td>Migration</td><td>AWS DMS, Glue</td></tr>
                    <tr><td>Business Analyst / SME</td><td>Process mapping, detailed requirements, backlog grooming, change impact assessment</td><td class="text-right-align">1–2</td><td>1 Senior</td><td>Discovery</td><td>User Stories</td></tr>
                    <tr><td>SRE / Operations Lead</td><td>Define SLOs/SLIs, runbooks, monitoring/alerting, incident response, game days</td><td class="text-right-align">1–2</td><td>1 Lead</td><td>Stabilization</td><td>CloudWatch, Grafana</td></tr>
                    <tr><td>Change Mgmt Lead</td><td>Communication plan, training & enablement, support model transition, rollout readiness</td><td class="text-right-align">1</td><td>1 Lead</td><td>Adoption</td><td>PROSCI/ADKAR</td></tr>
                    <tr><td>FinOps Analyst</td><td>Cost modeling, tagging policy, budgets/guardrails, optimization recommendations</td><td class="text-right-align">1</td><td>1 Analyst</td><td>Planning, Run</td><td>Cost Explorer</td></tr>
                </tbody>
                <caption>Team composition for COBOL to AWS migration. Headcount represents full-time equivalents (FTE).</caption>
            </table>
        </div>

        <div class="insights-container">
            <h3 class="insights-title">Key Resourcing Insights</h3>
            <ul class="insights-list">
                <li><strong>Specialized Pairing:</strong> Success hinges on pairing COBOL specialists with modern developers to ensure accurate business logic translation.</li>
                <li><strong>Front-loaded Expertise:</strong> Architectural and discovery roles are critical upfront, shifting towards development and QA during the build phases.</li>
                <li><strong>Holistic Team Structure:</strong> The plan extends beyond pure tech roles, incorporating critical governance, change management, and FinOps functions.</li>
                <li><strong>Lean, Senior-Led Teams:</strong> The model favors smaller teams led by senior or principal talent to maximize velocity and ensure quality.</li>
            </ul>
        </div>
    </div>
</div>

</body>
</html>