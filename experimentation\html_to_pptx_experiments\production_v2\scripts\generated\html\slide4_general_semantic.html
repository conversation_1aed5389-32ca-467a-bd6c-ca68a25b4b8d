<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Presentation</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<script src="https://cdn.tailwindcss.com"></script>
<style>
    body, html {
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f0f2f5;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
    }
    .slide-container {
        width: 1280px;
        height: 720px;
        background-color: #ffffff;
        display: flex;
        flex-direction: column;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border: 1px solid #ddd;
    }
    .header {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 20px 40px;
        height: 80px;
        box-sizing: border-box;
    }
    .logo {
        max-height: 60px;
        max-width: 120px;
    }
    .slide-content {
        flex-grow: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
        width: 100%;
        padding: 24px 80px 40px 80px;
        box-sizing: border-box;
    }
    .headline {
        font-size: 2.7em;
        color: #002147;
        margin: 0 0 14px 0;
        font-weight: 600;
        line-height: 1.2;
        width: 100%;
    }
    .cols {
        display: grid;
        grid-template-columns: 44% 56%;
        gap: 36px;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
    }
    .card {
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 16px 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        background: linear-gradient(180deg, #ffffff 0%, #fbfbfd 100%);
    }
    .section-title {
        font-size: 1.35em;
        color: #002147;
        font-weight: 600;
        margin-bottom: 8px;
    }
    .list {
        list-style: none;
        padding-left: 0;
        margin: 0;
        font-size: 1.2em;
        color: #333;
        line-height: 1.5;
    }
    .list li {
        position: relative;
        padding-left: 26px;
        margin-bottom: 8px;
    }
    .list li::before {
        content: '✓';
        position: absolute;
        left: 0;
        top: 0;
        color: #16a34a;
        font-weight: 700;
    }

    /* Diagram */
    .diagram {
        position: relative;
        width: 100%;
        height: 460px;
        border: 1px solid #e5e7eb;
        border-radius: 14px;
        background: #f8fafc;
        box-shadow: inset 0 1px 0 rgba(255,255,255,0.9);
        overflow: hidden;
    }
    .d-box {
        position: absolute;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 10px;
        padding: 10px 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        min-width: 120px;
    }
    .d-title {
        font-size: 0.95em;
        font-weight: 700;
        color: #0f172a;
        margin-bottom: 6px;
    }
    .d-sub {
        font-size: 0.85em;
        color: #475569;
    }
    .doc-stack {
        display: flex; gap: 6px; align-items: center;
    }
    .doc {
        width: 28px; height: 36px; background: #ffffff; border: 1px solid #d1d5db; border-radius: 3px; position: relative; box-shadow: 0 1px 2px rgba(0,0,0,0.06);
    }
    .doc:after {
        content: '';
        position: absolute;
        right: 0; top: 0;
        width: 12px; height: 12px;
        background: linear-gradient(135deg, #e5e7eb 50%, transparent 50%);
        border-top-right-radius: 3px;
    }
    .doc .line { height: 4px; background: #e5e7eb; margin: 6px 5px; border-radius: 4px; }

    .tiny-icon {
        width: 22px; height: 22px; object-fit: contain;
    }

    /* Arrows */
    .arrow-h { position: absolute; height: 2px; background: #94a3b8; }
    .arrow-v { position: absolute; width: 2px; background: #94a3b8; }
    .arrowhead-right { position: absolute; width: 0; height: 0; border-top: 6px solid transparent; border-bottom: 6px solid transparent; border-left: 8px solid #94a3b8; }
    .arrowhead-left { position: absolute; width: 0; height: 0; border-top: 6px solid transparent; border-bottom: 6px solid transparent; border-right: 8px solid #94a3b8; }
    .arrowhead-down { position: absolute; width: 0; height: 0; border-left: 6px solid transparent; border-right: 6px solid transparent; border-top: 8px solid #94a3b8; }
    .arrowhead-up { position: absolute; width: 0; height: 0; border-left: 6px solid transparent; border-right: 6px solid transparent; border-bottom: 8px solid #94a3b8; }

    .label-chip {
        position: absolute;
        font-size: 0.72em;
        color: #0f172a;
        background: #eef2ff;
        border: 1px solid #c7d2fe;
        padding: 2px 6px;
        border-radius: 999px;
        white-space: nowrap;
    }
</style>
</head>
<body>
<div class="slide-container">
    <div class="header">
        <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/elasticsearch.svg" alt="Company Logo" class="logo">
    </div>

    <div class="slide-content">
        <h1 class="headline">Our Solution: Semantic Search Powered by AI — Intelligent Contract Discovery: Understanding Meaning, Delivering Results</h1>

        <div class="cols">
            <!-- Left column: Key Features + Benefits -->
            <div class="flex flex-col gap-3">
                <div class="card">
                    <div class="section-title">Key Features</div>
                    <ul class="list">
                        <li>Semantic Search: Leverages transformer models (e.g., BERT, RoBERTa) to understand the meaning of queries and documents.</li>
                        <li>Vector Database: Stores contracts as vector embeddings for fast and accurate similarity search.</li>
                        <li class="flex flex-col">
                            <span>Cloud-Based Infrastructure: Scalable and reliable cloud infrastructure (AWS, Azure, or Google Cloud).</span>
                            <span class="mt-2 flex items-center gap-3">
                                <img class="h-5 w-auto" alt="AWS" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Business-Applications/AWS-Business-Applications-WorkDocs.svg">
                                <img class="h-5 w-auto" alt="Azure" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/ai%20%2B%20machine%20learning/Azure-ai%20%2B%20machine%20learning-00792-icon-service-Computer-Vision.svg">
                                <img class="h-5 w-auto" alt="GCP" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Google-Kubernetes-Engine.svg">
                            </span>
                        </li>
                        <li>User-Friendly Interface: Intuitive search interface for easy access to contract information.</li>
                        <li>RAG Integration (Optional): Generates concise summaries or answers based on retrieved contract snippets.</li>
                    </ul>
                </div>
                <div class="card">
                    <div class="section-title">Benefits</div>
                    <ul class="list">
                        <li>Superior Accuracy: Find the right contracts, even with nuanced queries.</li>
                        <li>Faster Results: Dramatically reduce search time.</li>
                        <li>Scalability: Handles growing contract volumes with ease.</li>
                        <li>Improved Compliance: Proactively identify and manage contract risks.</li>
                    </ul>
                </div>
            </div>

            <!-- Right column: Diagram -->
            <div class="diagram">
                <!-- Boxes -->
                <div class="d-box" style="left:14px; top:18px; width:150px; height:84px;">
                    <div class="d-title">Contract Documents</div>
                    <div class="doc-stack">
                        <div class="doc"><div class="line" style="width:18px"></div><div class="line" style="width:20px"></div></div>
                        <div class="doc"><div class="line" style="width:21px"></div><div class="line" style="width:16px"></div></div>
                        <div class="doc"><div class="line" style="width:22px"></div><div class="line" style="width:14px"></div></div>
                    </div>
                </div>

                <div class="d-box" style="left:190px; top:14px; width:175px; height:92px;">
                    <div class="d-title">Data Ingestion &amp; Preprocessing</div>
                    <div class="d-sub">OCR, text extraction, chunking, PII masking</div>
                </div>

                <div class="d-box" style="left:380px; top:14px; width:170px; height:92px;">
                    <div class="d-title">Transformer Model</div>
                    <div class="d-sub">BERT / RoBERTa</div>
                </div>

                <div class="d-box" style="left:380px; top:130px; width:170px; height:88px;">
                    <div class="flex items-center justify-between">
                        <div class="d-title">Vector Database</div>
                        <img class="tiny-icon" alt="Vector DB Icon" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/af382f4619a6b8a20527af737456bb800d9ba722/svgs/servers-isometric-icons/cloud-server.svg">
                    </div>
                    <div class="d-sub">e.g., Pinecone, Weaviate</div>
                </div>

                <div class="d-box" style="left:14px; top:230px; width:156px; height:62px;">
                    <div class="d-title">User Query</div>
                    <div class="d-sub">Natural language</div>
                </div>

                <div class="d-box" style="left:14px; top:306px; width:156px; height:66px;">
                    <div class="d-title">Query Processing</div>
                    <div class="d-sub">Normalization, filters</div>
                </div>

                <div class="d-box" style="left:238px; top:274px; width:210px; height:78px;">
                    <div class="flex items-center justify-between">
                        <div class="d-title">Search &amp; Retrieval</div>
                        <img class="tiny-icon" alt="Search Icon" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/af382f4619a6b8a20527af737456bb800d9ba722/svgs/servers-isometric-icons/spin.6ffd8bb2.svg">
                    </div>
                    <div class="d-sub">kNN, re-ranking, filters</div>
                </div>

                <div class="d-box" style="left:238px; top:366px; width:210px; height:64px;">
                    <div class="d-title">Relevant Contracts</div>
                    <div class="d-sub">Results + optional RAG summary</div>
                </div>

                <!-- Arrows and labels -->
                <!-- Documents -> Ingestion -->
                <div class="arrow-h" style="left:166px; top:60px; width:24px;"></div>
                <div class="arrow-h" style="left:190px; top:60px; width:0;"></div>
                <div class="arrowhead-right" style="left:188px; top:54px;"></div>

                <!-- Ingestion -> Transformer -->
                <div class="arrow-h" style="left:365px; top:60px; width:12px;"></div>
                <div class="arrowhead-right" style="left:374px; top:54px;"></div>

                <!-- Transformer -> Vector DB (down) -->
                <div class="arrow-v" style="left:464px; top:106px; height:16px;"></div>
                <div class="arrowhead-down" style="left:458px; top:120px;"></div>
                <div class="label-chip" style="left:410px; top:108px;">Vector Embeddings</div>

                <!-- User Query -> Query Processing -->
                <div class="arrow-v" style="left:92px; top:292px; height:12px;"></div>
                <div class="arrowhead-down" style="left:86px; top:300px;"></div>

                <!-- Query Processing -> Transformer (elbow: up then right) -->
                <div class="arrow-v" style="left:92px; top:170px; height:132px;"></div>
                <div class="arrowhead-up" style="left:86px; top:166px;"></div>
                <div class="arrow-h" style="left:92px; top:170px; width:286px;"></div>
                <div class="arrowhead-right" style="left:374px; top:164px;"></div>
                <div class="label-chip" style="left:120px; top:152px;">Query Embedding</div>

                <!-- Vector DB -> Search & Retrieval (elbow: down then left) -->
                <div class="arrow-v" style="left:464px; top:218px; height:56px;"></div>
                <div class="arrow-h" style="left:300px; top:274px; width:164px;"></div>
                <div class="arrowhead-left" style="left:296px; top:268px;"></div>

                <!-- Query Processing -> Search & Retrieval -->
                <div class="arrow-h" style="left:170px; top:336px; width:66px;"></div>
                <div class="arrowhead-right" style="left:234px; top:330px;"></div>

                <!-- Search & Retrieval -> Relevant Contracts (down) -->
                <div class="arrow-v" style="left:342px; top:352px; height:10px;"></div>
                <div class="arrowhead-down" style="left:336px; top:358px;"></div>
            </div>
        </div>
    </div>
</div>
</body>
</html>