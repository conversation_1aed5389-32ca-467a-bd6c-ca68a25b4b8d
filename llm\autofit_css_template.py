# Auto-fit CSS Template for HTML Slides
# This ensures all content fits within 1280x720px dimensions

AUTOFIT_CSS_TEMPLATE = """
/* AUTO-FIT CSS TEMPLATE - Ensures content fits within 720px height */
body {
    width: 1280px;
    height: 720px;
    margin: 0;
    padding: 0;
    position: relative;
    background-color: #FFFFFF;
    font-family: '<PERSON>o', sans-serif;
    overflow: hidden;
    color: #272774;
    display: flex;
    flex-direction: column;
}

/* Main content container with auto-fit */
.slide-content {
    position: absolute;
    top: 60px;
    left: 60px;
    right: 60px;
    bottom: 80px; /* Leave space for footer */
    max-width: 1160px;
    max-height: 580px; /* 720 - 60 (top) - 80 (bottom) */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1;
}

/* Auto-scaling title */
.slide-title {
    font-size: clamp(1.8em, 4vw, 2.3em); /* Responsive title size */
    font-weight: 700;
    margin-bottom: clamp(15px, 3vh, 30px);
    color: #272774;
    flex-shrink: 0; /* Don't shrink title */
    line-height: 1.2;
}

/* Content area that auto-fits remaining space */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0; /* Allow flex shrinking */
}

/* Split content layout */
.split-content {
    display: flex;
    gap: clamp(20px, 3vw, 40px);
    flex: 1;
    min-height: 0;
}

/* Sections that auto-scale */
.visual-section, .bullet-points-section {
    flex: 1;
    background: #F5E2DA;
    border-radius: 8px;
    padding: clamp(10px, 2vh, 20px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

/* Auto-scaling text elements - Improved readability */
.content-text {
    font-size: clamp(1.0em, 1.5vw, 1.2em);  /* Increased min from 0.8em to 1.0em */
    line-height: 1.4;
    margin-bottom: clamp(8px, 1.5vh, 15px);
}

/* Bullet points with auto-scaling - Better readability */
ul {
    margin: 0;
    padding-left: 20px;
    flex: 1;
    overflow: hidden;
}

li {
    font-size: clamp(0.9em, 1.4vw, 1.1em);  /* Increased min from 0.75em to 0.9em */
    line-height: 1.5;
    margin-bottom: clamp(5px, 1vh, 10px);
    display: flex;
    align-items: flex-start;
}

/* Icon sizing */
.icon {
    margin-right: 10px;
    flex-shrink: 0;
    font-size: clamp(0.8em, 1.2vw, 1em);
    padding-top: 2px;
}

/* Image placeholders that scale */
.image-placeholder {
    position: relative;
    width: 100%;
    height: clamp(120px, 25vh, 200px);
    background: #FFFFFF;
    border: 2px solid #272774;
    border-radius: 8px;
    margin-bottom: clamp(10px, 2vh, 20px);
    flex-shrink: 0;
}

/* Progress bars and other elements */
.progress-bar-container {
    margin-top: auto;
    padding-top: clamp(5px, 1vh, 10px);
}

/* Fixed elements (logo, page number) */
.page-number {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: #EBC4B4;
    color: #272774;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    font-size: clamp(12px, 1.2vw, 14px);
    z-index: 10;
}

.logo {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 10;
}

.logo img {
    height: clamp(40px, 6vh, 60px);
}

/* Responsive adjustments for content-heavy slides */
@media (max-height: 720px) {
    .slide-title {
        font-size: 1.8em;
        margin-bottom: 15px;
    }
    
    .content-text, li {
        font-size: 0.9em;  /* Increased from 0.8em */
        line-height: 1.3;
        margin-bottom: 8px;
    }
    
    .visual-section, .bullet-points-section {
        padding: 15px;
    }
    
    .image-placeholder {
        height: 120px;
        margin-bottom: 10px;
    }
}

/* Utility classes for different content densities - Improved readability */
.content-light .slide-title { font-size: 2.5em; }
.content-light .content-text { font-size: 1.1em; }
.content-light li { font-size: 1em; }

.content-medium .slide-title { font-size: 2.2em; }
.content-medium .content-text { font-size: 1.0em; }  /* Increased from 0.95em */
.content-medium li { font-size: 0.9em; }  /* Increased from 0.85em */

.content-heavy .slide-title { font-size: 2.1em; margin-bottom: 15px; }  /* Increased from 1.9em */
.content-heavy .content-text { font-size: 0.9em; line-height: 1.3; }  /* Increased from 0.8em */
.content-heavy li { font-size: 0.85em; line-height: 1.4; margin-bottom: 6px; }  /* Increased from 0.75em */
.content-heavy .visual-section, .content-heavy .bullet-points-section { padding: 12px; }
.content-heavy .image-placeholder { height: 100px; margin-bottom: 8px; }
"""

def get_autofit_css_for_content_density(content_length: int) -> str:
    """
    Returns appropriate CSS class based on content density

    Args:
        content_length: Approximate character count of slide content

    Returns:
        CSS class name for content density
    """
    # More realistic thresholds based on actual HTML content size
    # A typical slide with 1,395 output tokens ≈ 7,000-10,000 HTML characters
    if content_length < 8000:        # Increased from 300 - Light content
        density = "content-light"
    elif content_length < 15000:     # Increased from 600 - Medium content
        density = "content-medium"
    else:                            # 15,000+ characters - Truly heavy content
        density = "content-heavy"

    # Debug logging to help understand classification
    print(f"📊 Content Analysis: {content_length} chars → {density}")
    return density

def inject_autofit_css(html_content: str, content_density: str = "content-medium") -> str:
    """
    Inject auto-fit CSS into existing HTML content
    
    Args:
        html_content: Original HTML content
        content_density: CSS class for content density (content-light, content-medium, content-heavy)
        
    Returns:
        Modified HTML with auto-fit CSS injected
    """
    import re
    
    # Add content density class to body
    html_content = re.sub(
        r'<body([^>]*)>',
        f'<body\\1 class="{content_density}">',
        html_content
    )
    
    # Inject auto-fit CSS into the style section
    style_pattern = r'(<style[^>]*>)(.*?)(</style>)'
    
    def replace_styles(match):
        start_tag = match.group(1)
        existing_styles = match.group(2)
        end_tag = match.group(3)
        
        # Combine existing styles with auto-fit template
        combined_styles = existing_styles + "\n\n" + AUTOFIT_CSS_TEMPLATE
        
        return start_tag + combined_styles + end_tag
    
    modified_html = re.sub(style_pattern, replace_styles, html_content, flags=re.DOTALL)
    
    return modified_html
