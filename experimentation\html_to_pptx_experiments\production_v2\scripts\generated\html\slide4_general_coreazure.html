<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Core Azure Services: Powering Your Business</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/gojs/release/go.js"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;700&display=swap');
    html, body { height: 100%; margin: 0; }
    body {
      background-color: #f0f2f5;
      font-family: 'Segoe UI','Roboto','Helvetica Neue',Arial,sans-serif;
    }
    .slide-content { /* main content area marker for validator */ }
    /* Fixed canvas sizes to ensure everything fits within 1280x720 */
    .diagram-card { height: 220px; }
    .diagram-canvas { width: 100%; height: 170px; }
  </style>
</head>
<body class="flex items-center justify-center">
  <div class="w-[1280px] h-[720px] bg-white shadow-2xl overflow-hidden flex flex-col">
    <!-- Header (consistent with previous slides) -->
    <div class="flex items-center px-10 bg-[#0078D4] text-white" style="height:72px;">
      <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/azure-icon.svg" alt="Azure Icon" class="w-8 h-8 mr-4" />
      <div class="font-bold text-[1.2em]">Microsoft Azure</div>
    </div>

    <!-- Content -->
    <div class="slide-content flex-1 px-10 py-4 box-border overflow-hidden">
      <!-- Title aligned top-left -->
      <h1 class="text-[2.4em] leading-tight font-bold text-[#005A9E] mb-3">Core Azure Services: Powering Your Business</h1>

      <!-- Four-category overview -->
      <div class="grid grid-cols-2 gap-6 mb-3">
        <!-- Compute -->
        <div class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-center mb-2">
            <span class="text-[#0078D4] font-bold text-[1.3em] mr-2">Compute</span>
            <div class="flex items-center space-x-2">
              <img class="w-5 h-5" alt="VM" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/other/Azure-other-01846-icon-service-AVS-VM.svg" />
              <img class="w-5 h-5" alt="AKS" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/containers/Azure-containers-10023-icon-service-Kubernetes-Services.svg" />
              <img class="w-5 h-5" alt="Functions" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/compute/Azure-compute-10029-icon-service-Function-Apps.svg" />
            </div>
          </div>
          <ul class="list-disc pl-5 space-y-1 text-[1.2em] text-gray-700">
            <li><span class="font-semibold">Azure Virtual Machines:</span> IaaS with broad VM families (General purpose, Compute-optimized, Memory-optimized, GPU) and Windows/Linux images.</li>
            <li><span class="font-semibold">Azure Kubernetes Service (AKS):</span> Managed K8s for microservices; portable containers, autoscaling, integrated CI/CD.</li>
            <li><span class="font-semibold">Azure Functions:</span> Serverless, event-driven apps for data processing, integrations, and lightweight APIs.</li>
          </ul>
        </div>

        <!-- Storage -->
        <div class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-center mb-2">
            <span class="text-[#0078D4] font-bold text-[1.3em] mr-2">Storage</span>
            <div class="flex items-center space-x-2">
              <img class="w-5 h-5" alt="Storage Accounts" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/storage/Azure-storage-10086-icon-service-Storage-Accounts.svg" />
              <img class="w-5 h-5" alt="Azure Files" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/storage/Azure-storage-10400-icon-service-Azure-Fileshares.svg" />
            </div>
          </div>
          <ul class="list-disc pl-5 space-y-1 text-[1.2em] text-gray-700">
            <li><span class="font-semibold">Azure Blob Storage:</span> Object storage for images, videos, docs with Hot, Cool, Archive tiers based on access frequency and cost.</li>
            <li><span class="font-semibold">Azure Files:</span> SMB/NFS file shares for Windows, Linux, macOS—simple lift-and-shift of file servers.</li>
          </ul>
        </div>

        <!-- Database -->
        <div class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-center mb-2">
            <span class="text-[#0078D4] font-bold text-[1.3em] mr-2">Database</span>
            <div class="flex items-center space-x-2">
              <img class="w-5 h-5" alt="SQL Database" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/databases/Azure-databases-10130-icon-service-SQL-Database.svg" />
              <img class="w-5 h-5" alt="Cosmos DB" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/databases/Azure-databases-10121-icon-service-Azure-Cosmos-DB.svg" />
            </div>
          </div>
          <ul class="list-disc pl-5 space-y-1 text-[1.2em] text-gray-700">
            <li><span class="font-semibold">Azure SQL Database:</span> Single DB, elastic pool, or managed instance for performance, scale, and built‑in HA.</li>
            <li><span class="font-semibold">Azure Cosmos DB:</span> Globally distributed, multi‑model with single‑digit ms latency and 99.999% availability.</li>
          </ul>
        </div>

        <!-- Networking -->
        <div class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-center mb-2">
            <span class="text-[#0078D4] font-bold text-[1.3em] mr-2">Networking</span>
            <div class="flex items-center space-x-2">
              <img class="w-5 h-5" alt="VNet" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/networking/Azure-networking-10061-icon-service-Virtual-Networks.svg" />
              <img class="w-5 h-5" alt="Load Balancer" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/networking/Azure-networking-10062-icon-service-Load-Balancers.svg" />
            </div>
          </div>
          <ul class="list-disc pl-5 space-y-1 text-[1.2em] text-gray-700">
            <li><span class="font-semibold">Azure Virtual Network:</span> Private address space, subnets, peering; control with NSGs and routing.</li>
            <li><span class="font-semibold">Azure Load Balancer:</span> Internal/external L4 distribution for HA and scale.</li>
          </ul>
        </div>
      </div>

      <!-- Visuals: Three GoJS diagrams -->
      <div class="grid grid-cols-3 gap-4">
        <!-- Azure VM Architecture -->
        <div class="diagram-card bg-gray-50 border border-gray-200 rounded-lg p-3">
          <div class="flex items-center justify-between mb-2">
            <div class="font-semibold text-[#005A9E] text-[1.1em]">Azure VM: VNet + NSG + Storage</div>
            <img class="w-5 h-5" alt="VM icon" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/other/Azure-other-01846-icon-service-AVS-VM.svg" />
          </div>
          <div id="vmDiagram" class="diagram-canvas border border-gray-100 rounded"></div>
        </div>

        <!-- AKS Architecture -->
        <div class="diagram-card bg-gray-50 border border-gray-200 rounded-lg p-3">
          <div class="flex items-center justify-between mb-2">
            <div class="font-semibold text-[#005A9E] text-[1.1em]">Azure Kubernetes Service (AKS)</div>
            <img class="w-5 h-5" alt="AKS icon" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/containers/Azure-containers-10023-icon-service-Kubernetes-Services.svg" />
          </div>
          <div id="aksDiagram" class="diagram-canvas border border-gray-100 rounded"></div>
        </div>

        <!-- Blob Storage Tiers -->
        <div class="diagram-card bg-gray-50 border border-gray-200 rounded-lg p-3">
          <div class="flex items-center justify-between mb-2">
            <div class="font-semibold text-[#005A9E] text-[1.1em]">Azure Blob Storage: Tiers & Lifecycle</div>
            <img class="w-5 h-5" alt="Storage icon" src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/storage/Azure-storage-10086-icon-service-Storage-Accounts.svg" />
          </div>
          <div id="blobDiagram" class="diagram-canvas border border-gray-100 rounded"></div>
        </div>
      </div>
    </div>
  </div>

  <script>
    function $(id) { return document.getElementById(id); }

    function makeDiagram(div, nodeDefaults, linkDefaults, layoutOptions) {
      const $go = go.GraphObject.make;
      const diagram = $go(go.Diagram, div, {
        initialContentAlignment: go.Spot.Center,
        isReadOnly: true,
        "undoManager.isEnabled": false,
        "animationManager.isEnabled": false,
        padding: 4,
        layout: $go(go.LayeredDigraphLayout, Object.assign({
          direction: 0, // LeftToRight
          layerSpacing: 30,
          columnSpacing: 20,
          setsPortSpots: false
        }, layoutOptions || {}))
      });

      diagram.nodeTemplate =
        $go(go.Node, "Auto",
          Object.assign({}, nodeDefaults || {}),
          $go(go.Shape, "RoundedRectangle",
            { fill: "white", stroke: "#CBD5E1", strokeWidth: 1, parameter1: 8 }),
          $go(go.Panel, "Horizontal", { margin: 6, defaultAlignment: go.Spot.Center },
            $go(go.Picture, { width: 18, height: 18, margin: new go.Margin(0,6,0,0) },
              new go.Binding("source", "icon")),
            $go(go.Panel, "Table",
              $go(go.TextBlock,
                { row: 0, column: 0, font: "bold 12px Segoe UI, sans-serif", stroke: "#1f2937" },
                new go.Binding("text", "title")),
              $go(go.TextBlock,
                { row: 1, column: 0, margin: new go.Margin(2,0,0,0), font: "11px Segoe UI, sans-serif", stroke: "#6b7280", wrap: go.TextBlock.WrapFit, width: 150 },
                new go.Binding("text", "desc"))
            )
          )
        );

      diagram.linkTemplate =
        $go(go.Link,
          {
            routing: go.Link.AvoidsNodes,
            curve: go.Link.JumpGap,
            corner: 6,
            toShortLength: 3,
            selectable: false
          },
          Object.assign({},
            linkDefaults || {}),
          $go(go.Shape, { stroke: "#64748B", strokeWidth: 1.6 }),
          $go(go.Shape, { toArrow: "Standard", stroke: null, fill: "#64748B", scale: 1 })
        );

      return diagram;
    }

    function initVM() {
      const vnetIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/networking/Azure-networking-10061-icon-service-Virtual-Networks.svg";
      const vmIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/other/Azure-other-01846-icon-service-AVS-VM.svg";
      const storageIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/storage/Azure-storage-10086-icon-service-Storage-Accounts.svg";

      const d = makeDiagram("vmDiagram");
      d.model = new go.GraphLinksModel(
        [
          { key: "vnet", title: "Virtual Network", desc: "Private address space, subnets", icon: vnetIcon },
          { key: "nsg", title: "Network Security Group", desc: "Allow/Deny inbound/outbound", icon: "" },
          { key: "vm", title: "Virtual Machine", desc: "Windows/Linux, size families", icon: vmIcon },
          { key: "sa", title: "Storage Account (Blob)", desc: "OS/Data disks, snapshots", icon: storageIcon }
        ],
        [
          { from: "vnet", to: "nsg" },
          { from: "nsg", to: "vm" },
          { from: "vm", to: "sa" }
        ]
      );
    }

    function initAKS() {
      const aksIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/containers/Azure-containers-10023-icon-service-Kubernetes-Services.svg";

      const d = makeDiagram("aksDiagram", null, null, { layerSpacing: 28, columnSpacing: 18 });

      d.model = new go.GraphLinksModel(
        [
          { key: "cp", title: "Control Plane", desc: "API server, Scheduler, Ctlr Manager", icon: aksIcon },
          { key: "np1", title: "Node Pool: Node 1", desc: "Kubelet, container runtime", icon: aksIcon },
          { key: "np2", title: "Node Pool: Node 2", desc: "Kubelet, container runtime", icon: aksIcon },
          { key: "podA", title: "Pod A", desc: "Service A - 2 containers", icon: "" },
          { key: "podB", title: "Pod B", desc: "Service B - 1+ containers", icon: "" },
          { key: "svc", title: "Service (ClusterIP/LoadBalancer)", desc: "Stable access to Pods", icon: "" }
        ],
        [
          { from: "cp", to: "np1" },
          { from: "cp", to: "np2" },
          { from: "np1", to: "podA" },
          { from: "np2", to: "podB" },
          { from: "svc", to: "podA" },
          { from: "svc", to: "podB" }
        ]
      );
    }

    function initBlob() {
      const storageIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/storage/Azure-storage-10086-icon-service-Storage-Accounts.svg";

      const d = makeDiagram("blobDiagram", null, { stroke: "#0ea5e9", fill: "#0ea5e9" }, { layerSpacing: 26, columnSpacing: 18 });

      d.model = new go.GraphLinksModel(
        [
          { key: "acct", title: "Storage Account", desc: "Lifecycle policies", icon: storageIcon },
          { key: "hot", title: "Hot Tier", desc: "Frequent access, higher cost/GB", icon: storageIcon },
          { key: "cool", title: "Cool Tier", desc: "Infrequent access, lower cost/GB", icon: storageIcon },
          { key: "archive", title: "Archive Tier", desc: "Rare access, lowest cost/GB", icon: storageIcon }
        ],
        [
          { from: "acct", to: "hot" },
          { from: "hot", to: "cool" },
          { from: "cool", to: "archive" }
        ]
      );
    }

    function initDiagrams() {
      initVM();
      initAKS();
      initBlob();
    }

    window.addEventListener('load', initDiagrams);
  </script>
</body>
</html>