const PptxGenJS = require('pptxgenjs');

// Inline corruption prevention utility (hybrid approach)
function preventChartCorruption(chartOptions, verbose = true) {
    if (verbose) {
        console.log('🛡️ Applying chart corruption prevention...');
    }

    const safeOptions = { ...chartOptions };
    const removedOptions = [];
    const warnings = [];

    // FORBIDDEN OPTIONS that cause silent corruption
    const forbiddenOptions = [
        'showValAxisGridLines', 'valGridLine', 'catGridLine',
        'plotArea', 'chartArea', 'chartColorsOpacity',
        'catAxisLabelColor', 'valAxisLabelColor', 'dataLabelBorder'
    ];

    forbiddenOptions.forEach(option => {
        if (safeOptions.hasOwnProperty(option)) {
            delete safeOptions[option];
            removedOptions.push(option);
        }
    });

    // Handle lineDash arrays with null/undefined values
    if (safeOptions.lineDash) {
        const hasNullOrUndefined = safeOptions.lineDash.some(val =>
            val === null || val === undefined
        );

        if (hasNullOrUndefined) {
            delete safeOptions.lineDash;
            removedOptions.push('lineDash (contained null/undefined values)');
            warnings.push('lineDash with null/undefined values causes silent PowerPoint corruption');
        }
    }

    // Handle color options with # prefix
    if (safeOptions.chartColors) {
        const hasHashPrefix = safeOptions.chartColors.some(color =>
            typeof color === 'string' && color.startsWith('#')
        );

        if (hasHashPrefix) {
            safeOptions.chartColors = safeOptions.chartColors.map(color =>
                typeof color === 'string' && color.startsWith('#') ? color.substring(1) : color
            );
            warnings.push('Removed # prefix from chartColors for better compatibility');
        }
    }

    // Log results
    if (verbose) {
        if (removedOptions.length > 0) {
            console.warn('🚨 Removed corruption-causing options:', removedOptions);
        }

        if (warnings.length > 0) {
            console.warn('⚠️ Warnings:', warnings);
        }

        if (removedOptions.length === 0 && warnings.length === 0) {
            console.log('✅ Chart options are already corruption-free');
        } else {
            console.log('✅ Chart options are now corruption-free');
        }
    }

    return safeOptions;
}

function createSafeChart(slide, pptx, chartType, chartData, chartOptions, verbose = true) {
    try {
        // Apply corruption prevention
        const safeOptions = preventChartCorruption(chartOptions, verbose);

        // Create chart with safe options
        slide.addChart(pptx.ChartType[chartType], chartData, safeOptions);

        if (verbose) {
            console.log(`✅ ${chartType} chart created successfully`);
        }

        return true;

    } catch (error) {
        console.error('❌ Chart creation failed:', error.message);
        return false;
    }
}

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();
    
    // Ultra-safe positioning constants
    const SLIDE_W = 10;
    const SLIDE_H = 5.625;
    const MARGIN = 0.3;
    const SAFE_W = SLIDE_W - (2 * MARGIN);
    const SAFE_H = SLIDE_H - (2 * MARGIN);
    
    // Layout constants
    const LEFT_COL_X = 0.5;
    const LEFT_COL_W = 5.5;
    const RIGHT_COL_X = 6.2;
    const RIGHT_COL_W = 3.5;
    const CONTENT_START_Y = 1.0;
    
    // Background
    slide.background = { color: 'FDF5E6' };
    
    // Helper function for safe image addition
    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' }, line: { color: 'D1D5DB', width: 1 }
            });
            slide.addText(fallbackText, {
                x: options.x + 0.1, y: options.y + (options.h/2) - 0.1,
                w: options.w - 0.2, h: 0.2, fontSize: 8, color: '6B7280', align: 'center'
            });
        }
    }
    
    // 1. Header Section
    addImageWithFallback(slide, 'https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Google_Cloud_Logo.max-2800x2800.jpg', {
        x: 0.3, y: 0.3, w: 1.0, h: 0.4
    }, 'GCP Logo');
    
    slide.addText('The Evolving Economics of Renewables & Policy Influence', {
        x: 1.5, y: 0.3, w: 7.8, h: 0.5,
        fontSize: 16, color: '1d3557', bold: true, align: 'left'
    });
    
    // 2. Chart Section - USING HYBRID APPROACH
    
    // 2.1. Chart Data (PROVEN WORKING FORMAT)
    const chartData = [
        {
            name: "Solar PV",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [150, 110, 80, 60, 40]
        },
        {
            name: "Onshore Wind",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [80, 65, 50, 40, 35]
        },
        {
            name: "Offshore Wind",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [180, 140, 110, 90, 70]
        },
        {
            name: "Coal",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [100, 100, 100, 100, 100]
        },
        {
            name: "Natural Gas",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [60, 70, 80, 75, 75]
        }
    ];
    
    // 2.2. Chart Options (BEFORE VALIDATION - may contain problematic options)
    const rawChartOptions = {
        x: LEFT_COL_X,
        y: CONTENT_START_Y + 0.4,
        w: LEFT_COL_W,
        h: 3.2,
        chartColors: ['#facc15', '#3b82f6', '#1e3a8a', '#1f2937', '#9ca3af'], // Has # prefix
        lineSize: 2,
        lineDash: [null, null, null, 'dash', null], // PROBLEMATIC: mixed types
        showLegend: false,
        valAxisTitle: 'LCOE (USD/MWh)',
        valAxisTitleFontSize: 9,
        valAxisTitleColor: '374151',
        valAxisLabelFontSize: 8,
        valAxisMaxVal: 200,
        valAxisMinVal: 0,
        valAxisMajorUnit: 25,
        catAxisLabelFontSize: 9,
        showValAxisGridLines: true, // PROBLEMATIC: invalid option
        valGridLine: { color: 'E5E7EB', size: 1, style: 'solid' }, // PROBLEMATIC: complex object
        catAxisLabelColor: '4B5563' // PROBLEMATIC: can cause issues
    };
    
    // 2.3. HYBRID APPROACH: Use code-side validation + safe chart creation
    console.log('🔧 HYBRID APPROACH: Applying code-side corruption prevention...');
    
    // Method 1: Manual validation (demonstrates the process)
    console.log('📋 Method 1: Manual validation');
    const manualSafeOptions = preventChartCorruption(rawChartOptions, true);
    
    // Method 2: Safe chart creation wrapper (recommended)
    console.log('📋 Method 2: Safe chart creation wrapper');
    const chartCreated = createSafeChart(slide, pptx, 'line', chartData, rawChartOptions, true);
    
    if (!chartCreated) {
        console.error('❌ Chart creation failed, adding fallback content');
        slide.addText('Chart could not be generated safely', {
            x: LEFT_COL_X, y: CONTENT_START_Y + 0.4, w: LEFT_COL_W, h: 0.5,
            fontSize: 12, color: 'FF0000', align: 'center'
        });
    }
    
    // 2.4. Chart Title
    slide.addText('Levelized Cost of Energy (LCOE) Trends (2010-2023)', {
        x: LEFT_COL_X, y: CONTENT_START_Y, w: LEFT_COL_W, h: 0.3,
        fontSize: 12, color: '1d3557', bold: true, align: 'center'
    });
    
    // 3. Right Column Content
    let rightY = CONTENT_START_Y;
    
    // 3.1. Insights Section
    slide.addText('Key Insights', {
        x: RIGHT_COL_X, y: rightY, w: RIGHT_COL_W, h: 0.3,
        fontSize: 12, color: '1d3557', bold: true
    });
    rightY += 0.4;
    
    const insights = [
        'Solar PV costs dropped 73% since 2010',
        'Wind technologies show consistent improvement',
        'Renewables now cost-competitive with fossils',
        'Policy support accelerated cost reductions'
    ];
    
    insights.forEach(insight => {
        if (rightY < 4.5) {
            slide.addText(`• ${insight}`, {
                x: RIGHT_COL_X, y: rightY, w: RIGHT_COL_W, h: 0.25,
                fontSize: 9, color: '4B5563'
            });
            rightY += 0.3;
        }
    });
    
    rightY += 0.2;
    
    // 3.2. Policy Impact Section
    slide.addText('Policy Impact', {
        x: RIGHT_COL_X, y: rightY, w: RIGHT_COL_W, h: 0.3,
        fontSize: 12, color: '1d3557', bold: true
    });
    rightY += 0.4;
    
    slide.addText('Government incentives, R&D funding, and renewable energy targets have been crucial drivers of cost reductions and technology improvements.', {
        x: RIGHT_COL_X, y: rightY, w: RIGHT_COL_W, h: 0.8,
        fontSize: 9, color: '4B5563', align: 'left'
    });
    
    // 4. Footer
    slide.addText('Unlocking Innovation with Google Cloud', {
        x: 0.3, y: 4.8, w: 6.0, h: 0.3,
        fontSize: 10, color: '6B7280', italic: true
    });
    
    slide.addText('Slide 6', {
        x: 8.5, y: 4.8, w: 1.0, h: 0.3,
        fontSize: 10, color: '6B7280', align: 'right'
    });
    
    return pptx.writeFile({ fileName: 'generated/presentations/slide6_general_economics_hybrid_safe.pptx' });
}

// Execute the function
createPresentation()
    .then(() => {
        console.log('✅ PowerPoint generated with hybrid corruption prevention!');
    })
    .catch(error => {
        console.error('❌ Error:', error);
    });
