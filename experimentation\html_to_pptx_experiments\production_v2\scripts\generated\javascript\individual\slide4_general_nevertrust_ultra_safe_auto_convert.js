const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        leftText: '33475B',
        leftTitle: '0A2351',
        leftCheckmark: '0078D4',
        rightBg: '0A2351',
        diagramSegment1: '1E88E5',
        diagramSegment2: '00BCD4',
        diagramSegment3: '43A047',
        diagramSegment4: '7E57C2',
        diagramSegment5: 'EF6C00',
        diagramStroke: 'FFFFFF',
        diagramCenterBadge: '0A2351',
        diagramCenterBorder: 'E5E7EB',
        diagramCenterText: 'FFFFFF',
        diagramCenterSubtext: 'CFE8FF',
        diagramIconBg: 'FFFFFF',
        diagramCaptionBg: 'FFFFFF', // Opacity will be handled by `transparency`
        diagramCaptionTitle: 'FFFFFF',
        diagramCaptionText: 'E6F2FF',
    };

    // Font Sizes (Professional & Consistent)
    const FONT_SIZES = {
        mainTitle: 18, // Slightly larger for emphasis on this layout
        listItem: 10,
        listItemBold: 10,
        checkmark: 14,
        diagramCenterTitle: 11,
        diagramCenterSubtext: 6,
        diagramCaptionTitle: 6.5,
        diagramCaptionText: 6,
    };

    // Layout Calculations (Clean & Efficient)
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 5.4; // 54% of the slide width
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W;
    const RIGHT_COL_W = SLIDE_WIDTH - RIGHT_COL_X;

    // =======================================================================
    // 2. HELPER FUNCTIONS (Robust Edge Case Handling)
    // =======================================================================

    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            console.warn(`⚠️ Image failed, using fallback: ${imagePath}`);
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' }, line: { color: 'D1D5DB', width: 1 },
                rectRadius: 0.05
            });
            slide.addText(fallbackText, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fontSize: 8, color: '6B7280', align: 'center', valign: 'middle'
            });
        }
    }

    // =======================================================================
    // 3. SLIDE BACKGROUND & LAYOUT (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    // Right-side dark background container
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: RIGHT_COL_X, y: 0, w: RIGHT_COL_W, h: SLIDE_HEIGHT,
        fill: { color: COLORS.rightBg }
    });

    // =======================================================================
    // 4. LEFT COLUMN CONTENT (Text Content)
    // =======================================================================

    let currentY = 0.8; // Start Y position for the left column content

    // Main Title
    slide.addText('"Never Trust, Always Verify": Our Zero Trust Approach', {
        x: LEFT_COL_X + 0.2, y: currentY, w: LEFT_COL_W - 0.4, h: 0.8,
        fontSize: FONT_SIZES.mainTitle,
        color: COLORS.leftTitle,
        bold: true,
        valign: 'top'
    });
    currentY += 1.0;

    // List Items
    const listItems = [
        { bold: "BeyondCorp Enterprise (Google Inspired):", text: " Every user, device, and application is authenticated and authorized before accessing any resource." },
        { bold: "Identity-Centric Security:", text: " Strong authentication (MFA, passwordless), continuous authorization, and identity governance." },
        { bold: "Microsegmentation:", text: " Granular network segmentation to limit the impact of breaches." },
        { bold: "AI-Powered Threat Detection:", text: " Real-time anomaly detection using User and Entity Behavior Analytics (UEBA)." },
        { bold: "DevSecOps Integration:", text: " Embedding security into the software development lifecycle." },
        { bold: "Data Loss Prevention (DLP) Everywhere:", text: " Protecting sensitive data across all endpoints, applications, and cloud services." },
    ];

    listItems.forEach(item => {
        // Checkmark icon
        slide.addText("✓", {
            x: LEFT_COL_X + 0.2, y: currentY, w: 0.3, h: 0.3,
            fontFace: 'Segoe UI Symbol',
            fontSize: FONT_SIZES.checkmark,
            color: COLORS.leftCheckmark,
            bold: true,
            valign: 'top'
        });

        // List item text (using rich text for bolding)
        slide.addText([
            { text: item.bold, options: { fontSize: FONT_SIZES.listItemBold, color: COLORS.leftText, bold: true } },
            { text: item.text, options: { fontSize: FONT_SIZES.listItem, color: COLORS.leftText } }
        ], {
            x: LEFT_COL_X + 0.5, y: currentY, w: LEFT_COL_W - 0.7, h: 0.6,
            valign: 'top',
            lineSpacing: 14 // Equivalent to 1.5 line-height for 10pt font
        });

        currentY += 0.7; // Increment Y for the next item
    });

    // =======================================================================
    // 5. RIGHT COLUMN CONTENT (SVG Diagram Recreation)
    // =======================================================================

    const diagramW = RIGHT_COL_W - 0.6;
    const diagramH = diagramW;
    const diagramX = RIGHT_COL_X + (RIGHT_COL_W - diagramW) / 2;
    const diagramY = (SLIDE_HEIGHT - diagramH) / 2;
    const centerX = diagramX + diagramW / 2;
    const centerY = diagramY + diagramH / 2;
    const outerR = diagramW * 0.366; // 220/600
    const innerR = diagramW * 0.266; // 160/600

    // Diagram Segments (using ARC shapes)
    slide.addShape(pptx.shapes.ARC, { x: centerX - outerR, y: centerY - outerR, w: outerR * 2, h: outerR * 2, angleRange: [-90, 72], fill: { color: COLORS.diagramSegment1 } });
    slide.addShape(pptx.shapes.ARC, { x: centerX - outerR, y: centerY - outerR, w: outerR * 2, h: outerR * 2, angleRange: [-18, 72], fill: { color: COLORS.diagramSegment2 } });
    slide.addShape(pptx.shapes.ARC, { x: centerX - outerR, y: centerY - outerR, w: outerR * 2, h: outerR * 2, angleRange: [54, 72], fill: { color: COLORS.diagramSegment3 } });
    slide.addShape(pptx.shapes.ARC, { x: centerX - outerR, y: centerY - outerR, w: outerR * 2, h: outerR * 2, angleRange: [126, 72], fill: { color: COLORS.diagramSegment4 } });
    slide.addShape(pptx.shapes.ARC, { x: centerX - outerR, y: centerY - outerR, w: outerR * 2, h: outerR * 2, angleRange: [198, 72], fill: { color: COLORS.diagramSegment5 } });

    // White donut hole to create the ring effect
    slide.addShape(pptx.shapes.OVAL, { x: centerX - innerR, y: centerY - innerR, w: innerR * 2, h: innerR * 2, fill: { color: COLORS.rightBg } });

    // Center Badge
    const badgeR = diagramW * 0.196; // 118/600
    slide.addShape(pptx.shapes.OVAL, { x: centerX - badgeR, y: centerY - badgeR, w: badgeR * 2, h: badgeR * 2, fill: { color: COLORS.diagramCenterBadge }, line: { color: COLORS.diagramCenterBorder, width: 1 } });
    slide.addText('Zero Trust', { x: centerX - badgeR, y: centerY - 0.2, w: badgeR * 2, h: 0.3, align: 'center', fontSize: FONT_SIZES.diagramCenterTitle, color: COLORS.diagramCenterText, bold: true });
    slide.addText('Never Trust, Always Verify', { x: centerX - badgeR, y: centerY + 0.05, w: badgeR * 2, h: 0.2, align: 'center', fontSize: FONT_SIZES.diagramCenterSubtext, color: COLORS.diagramCenterSubtext });

    // Icons and Captions
    const diagramElements = [
        { angle: -54, icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/web-authentication-api.svg', title: 'Identity Verification', text: 'Verify users and devices', captionPos: { x: diagramX + 2.1, y: diagramY + 0.35, w: 1.0, h: 0.35 } },
        { angle: 18, icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/mobile/Azure-mobile-03335-icon-service-Power-Platform.svg', title: 'Device Security', text: 'Compliant, healthy devices', captionPos: { x: diagramX + 2.5, y: diagramY + 2.6, w: 1.0, h: 0.35 } },
        { angle: 90, icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/keys.svg', title: 'Least Privilege Access', text: 'Minimum necessary rights', captionPos: { x: diagramX + 1.3, y: diagramY + 3.4, w: 1.2, h: 0.35 } },
        { angle: 162, icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/af382f4619a6b8a20527af737456bb800d9ba722/svgs/servers-isometric-icons/cloud-server.svg', title: 'Microsegmentation', text: 'Isolate critical assets', captionPos: { x: diagramX + 0.3, y: diagramY + 2.6, w: 1.1, h: 0.35 } },
        { angle: 234, icon: 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/observers.svg', title: 'Continuous Monitoring', text: 'Detect anomalies in real time', captionPos: { x: diagramX + 0.3, y: diagramY + 0.35, w: 1.3, h: 0.35 } },
    ];

    const iconR = diagramW * 0.31; // Approx radius for icons
    const iconSize = diagramW * 0.073; // 44/600
    const imageSize = iconSize * 0.68; // 30/44

    diagramElements.forEach(el => {
        const rad = (el.angle * Math.PI) / 180;
        const iconX = centerX + iconR * Math.cos(rad) - iconSize / 2;
        const iconY = centerY + iconR * Math.sin(rad) - iconSize / 2;

        // Icon background circle
        slide.addShape(pptx.shapes.OVAL, { x: iconX, y: iconY, w: iconSize, h: iconSize, fill: { color: COLORS.diagramIconBg } });
        // Icon image
        addImageWithFallback(slide, el.icon, { x: iconX + (iconSize - imageSize) / 2, y: iconY + (iconSize - imageSize) / 2, w: imageSize, h: imageSize }, 'Icon');

        // Caption background
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: el.captionPos.x, y: el.captionPos.y, w: el.captionPos.w, h: el.captionPos.h,
            fill: { color: COLORS.diagramCaptionBg, transparency: 88 }, // rgba(255,255,255,0.12)
            rectRadius: 0.05
        });
        // Caption text
        slide.addText(el.title, { x: el.captionPos.x + 0.05, y: el.captionPos.y + 0.02, w: el.captionPos.w - 0.1, h: 0.15, fontSize: FONT_SIZES.diagramCaptionTitle, color: COLORS.diagramCaptionTitle, bold: true });
        slide.addText(el.text, { x: el.captionPos.x + 0.05, y: el.captionPos.y + 0.17, w: el.captionPos.w - 0.1, h: 0.15, fontSize: FONT_SIZES.diagramCaptionText, color: COLORS.diagramCaptionText });
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide4_general_nevertrust.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
