import logging
from PIL import Image
import asyncio # Import asyncio for running sync functions in a thread pool
from llm.llmwrapper import LLM
from htmlrender.renderer import HTMLRenderer
from utils.utils import find_text_in_between_tags
from loguru import logger
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    AsyncRetrying  # Import AsyncRetrying for async operations
)
from prompt.generationpy_agenda_slide_prompt import generationpy_agenda_slide_prompt_duy_v2
from prompt.generationpy_slide_prompt import generationpy_slide_prompt_duy_v2
from prompt.generationpy_title_slide_prompt import generationpy_title_slide_prompt_duy_v2
from prompt.generationpy_slide_review_prompt import generationpy_slide_review_prompt_daniel_v1
from prompt.official_prompts import generationpy_official_agenda_slide_prompt, generationpy_official_general_slide_prompt, generationpy_official_slide_review_prompt, generationpy_official_title_slide_prompt
from llm.few_shot_template import Example_1_inforgraphic, Example_2_inforgraphic, Example_1_agenda, Example_2_agenda, Example_1_title, Example_2_title
from llm.autofit_css_template import inject_autofit_css, get_autofit_css_for_content_density  # Commented out - autofit disabled

logger = logging.getLogger(__name__) # Use a proper logger

# --- Custom Exceptions for Retry Logic ---
class NoHtmlContentError(Exception):
    """Custom exception for when no HTML content is found in LLM response."""
    pass

class LLMInvalidResponseError(Exception):
    """Custom exception for when LLM returns an invalid structure or None text."""
    pass
# --- End Custom Exceptions ---

class Generator:
    def __init__(self, render_resize_ratio=0.4):
        self.rndr = HTMLRenderer()
        self.render_resize_ratio = render_resize_ratio

    def apply_autofit_css(self, html_content: str) -> str:
         """
         Apply auto-fit CSS to ensure content fits within 720px height
         DISABLED: autofit overflow:hidden was counterproductive for cut-off issues
         """
         try:
             # Estimate content density based on HTML length
             content_length = len(html_content)
             content_density = get_autofit_css_for_content_density(content_length)
    
             # Apply auto-fit CSS
             autofit_html = inject_autofit_css(html_content, content_density)
    
             logger.info(f"✅ Applied auto-fit CSS with {content_density} density")
             return autofit_html
    
         except Exception as e:
             logger.warning(f"⚠️ Failed to apply auto-fit CSS: {e}")
             return html_content  # Return original if auto-fit fails

    # Make reviewer method async because it calls async LLM methods and sync HTMLRenderer
    async def reviewer(self, html_code: str, html_image: Image.Image, llm: LLM) -> dict:
        slide_review_prompt = generationpy_official_slide_review_prompt

        # Sanitizing input html
        html_code = find_text_in_between_tags(html_code, start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)

        review_prompt = slide_review_prompt.format(code=html_code)
        
        # Await the async LLM call
        response = await llm.call_with_images(query=review_prompt, images=[html_image])

        # Extract and log token counts for reviewer
        reviewer_input_token_count = response['input_token_count']
        reviewer_output_token_count = response.get('output_token_count', 0)
        logger.info(f"Reviewer Input token count: {reviewer_input_token_count}")
        logger.info(f"Reviewer Output token count: {reviewer_output_token_count}")

        # Prepare the return dictionary including token counts
        return_data = {
            "status": "unchanged",
            "html_code": html_code,
            "input_token_count": reviewer_input_token_count,
            "output_token_count": reviewer_output_token_count
        }

        if '<OK>' not in response['text']:
            modified_html = find_text_in_between_tags(response['text'], start_tag="<!DOCTYPE html>", end_tag="</html>", inclusive=True)
            return_data["status"] = "modified"
            return_data["html_code"] = modified_html
        
        return return_data
    
    # Make generate_title_slide async
    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times (1 initial + 2 retries)
        wait=wait_exponential(multiplier=1, min=2, max=10), # Wait 1s, then 2s, etc., up to 10s
        retry=(
            retry_if_exception_type(NoHtmlContentError) | # Retry if HTML content is missing
            retry_if_exception_type(LLMInvalidResponseError) # Retry if LLM response is invalid
        ),
        reraise=True, # Re-raise the last exception if all retries fail
        before_sleep=lambda retry_state: logger.warning(
            f"Retrying generate_title_slide: Attempt {retry_state.attempt_number}, "
            f"waiting {retry_state.next_action.sleep:.1f} seconds due to {retry_state.outcome.exception()}..."
        )
    )

    # Make generate_title_slide async
    async def generate_title_slide(self, query: str, slide_content: str, brainstorm_output: str, generator_llm: LLM, reviewer_llm: LLM, review: bool = True) -> dict:
        # Added brainstorm_output parameter
        title_slide_prompt = generationpy_official_title_slide_prompt.format(
            query=query, slide_content=slide_content, 
            #Example_1=Example_1_title, Example_2=Example_2_title, 
            brain_storm=brainstorm_output # Added brain_storm
        )
        
        logger.info("Generating title slide...")
        logger.info(f"🔧 DEBUG: Title slide using LLM provider: {generator_llm.provider}")
        logger.info(f"🔧 DEBUG: Title slide LLM instance type: {type(generator_llm.llm)}")

        try: # Added this try block
            # Await the async LLM call
            logger.info("🔄 DEBUG: About to call generator_llm.call() for title slide")
            llm_response = await generator_llm.call(query=title_slide_prompt)
            logger.info("✅ DEBUG: generator_llm.call() completed successfully for title slide")

            # Extract and log token counts for title slide generation
            gen_input_token_count = llm_response['input_token_count']
            gen_output_token_count = llm_response.get('output_token_count', 0)
            logger.info(f"Title Slide Generation Input token count: {gen_input_token_count}")
            logger.info(f"Title Slide Generation Output token count: {gen_output_token_count}")

            # Check for invalid LLM response structure or None text
            if llm_response is None or 'text' not in llm_response or llm_response['text'] is None:
                logger.error("LLM returned invalid response structure or None text for title slide.")
                raise LLMInvalidResponseError("LLM returned invalid response for title slide")

            llm_text = llm_response['text'] # New line: Store text in variable to use consistently

            html_code = find_text_in_between_tags(
                llm_text, # Changed to use llm_text variable
                start_tag="<!DOCTYPE html>",
                end_tag="</html>",
                inclusive=True
            )
            if not html_code:
                logger.error("No HTML content found in LLM response for title slide after parsing.")
                raise NoHtmlContentError("No HTML content found in LLM response for title slide")

        except Exception as e: # New catch-all for unexpected errors
            # Catch any unexpected errors during the LLM call or initial parsing
            logger.error(f"An unexpected error occurred during title slide LLM call or parsing: {e}")
            raise # Re-raise for tenacity to handle or propagate

        # Apply auto-fit CSS to ensure content fits within 720px height
        html_code = self.apply_autofit_css(html_code)

        if review:
            logger.info("Reviewing generated HTML...")
            # Run synchronous renderHTML in a thread pool to avoid blocking the event loop
            html_img = await asyncio.to_thread(
                self.rndr.renderHTML, html_str=html_code, resize=True, resize_ratio=self.render_resize_ratio
            )
            # Await the async reviewer call
            review_response = await self.reviewer(html_code, html_img, reviewer_llm)
            html_code = review_response['html_code']

            # Accumulate token counts from the reviewer step
            gen_input_token_count += review_response['input_token_count']
            gen_output_token_count += review_response['output_token_count']

            # Re-apply auto-fit CSS after review (in case reviewer modified the HTML)
            # html_code = self.apply_autofit_css(html_code)  # DISABLED: autofit caused overflow:hidden issues
            html_code = self.apply_autofit_css(html_code)
        return {
            "html_code": html_code,
            "input_token_count": gen_input_token_count,
            "output_token_count": gen_output_token_count
        }
    
    # Make generate_agenda_slide async
    async def generate_agenda_slide(self, query: str, slide_content: str, 
                                    #title_slide_html: str, 
                                    brainstorm_output: str, generator_llm: LLM, reviewer_llm: LLM, review: bool = True) -> dict:
        # Added brainstorm_output parameter
        agenda_slide_prompt = generationpy_official_agenda_slide_prompt.format(
            query=query, 
            #title_slide_html=title_slide_html, 
            slide_content=slide_content, 
            #Example_1=Example_1_agenda, Example_2=Example_2_agenda, 
            brain_storm=brainstorm_output # Added brain_storm
        )

        logger.info("Generating Agenda slide...")

        # Await the async LLM call
        llm_response = await generator_llm.call(query=agenda_slide_prompt)

        # Extract and log token counts for agenda slide generation
        gen_input_token_count = llm_response['input_token_count']
        gen_output_token_count = llm_response.get('output_token_count', 0)
        logger.info(f"Agenda Slide Generation Input token count: {gen_input_token_count}")
        logger.info(f"Agenda Slide Generation Output token count: {gen_output_token_count}")


        html_code = find_text_in_between_tags(
            llm_response['text'],
            start_tag="<!DOCTYPE html>",
            end_tag="</html>",
            inclusive=True
        )

        # Apply auto-fit CSS to ensure content fits within 720px height
        html_code = self.apply_autofit_css(html_code)

        if review:
            logger.info("Reviewing generated HTML...")
            # Run synchronous renderHTML in a thread pool
            html_img = await asyncio.to_thread(
                self.rndr.renderHTML, html_str=html_code, resize=True, resize_ratio=self.render_resize_ratio
            )
            # Await the async reviewer call
            review_response = await self.reviewer(html_code, html_img, reviewer_llm)
            html_code = review_response['html_code']

            # Accumulate token counts from the reviewer step
            gen_input_token_count += review_response['input_token_count']
            gen_output_token_count += review_response['output_token_count']

            # Re-apply auto-fit CSS after review
            html_code = self.apply_autofit_css(html_code)

        return {
            "html_code": html_code,
            "input_token_count": gen_input_token_count,
            "output_token_count": gen_output_token_count
        }
    
    #General slide retry logic
    @retry(
        stop=stop_after_attempt(3), # Retry up to 3 times
        wait=wait_exponential(multiplier=1, min=2, max=10), # Exponential backoff: 1s, 2s, 4s, capped at 10s
        retry=(
            retry_if_exception_type(NoHtmlContentError) | # Retry if HTML content is missing
            retry_if_exception_type(LLMInvalidResponseError) # Retry if LLM response is invalid
        ),
        reraise=True, # Re-raise the last exception if all retries fail
        before_sleep=lambda retry_state: logger.warning(
            f"Retrying generate_general_slide: Attempt {retry_state.attempt_number}, "
            f"waiting {retry_state.next_action.sleep:.1f} seconds due to {retry_state.outcome.exception()}..."
        )
    )

    # Make generate_general_slide async
    async def generate_general_slide(self, query: str, slide_content: str, brainstorm_output: str, generator_llm: LLM, reviewer_llm: LLM, review: bool = True) -> dict: #existing_slide_content: dict
        # Added brainstorm_output parameter
        # existing_slides = "\n".join([f"{slide['name']} html code:\n```html\n{slide['html']}\n```\n" for slide in existing_slide_content])
        slide_prompt = generationpy_official_general_slide_prompt.format(
            query=query, slide_content=slide_content, 
            #Example_1=Example_1_inforgraphic, Example_2=Example_2_inforgraphic, 
            brain_storm=brainstorm_output # Added brain_storm
        ) #existing_slides=existing_slides

        logger.info("Generating slide...")

        gen_input_token_count = 0
        gen_output_token_count = 0

        try:
            # Await the async LLM call
            llm_response = await generator_llm.call(query=slide_prompt)
            
            # Extract and log token counts for general slide generation
            gen_input_token_count = llm_response['input_token_count']
            gen_output_token_count = llm_response.get('output_token_count', 0)
            logger.info(f"General Slide Input token count: {gen_input_token_count}")
            logger.info(f"General Slide token count: {gen_output_token_count}")

            if llm_response is None or 'text' not in llm_response:
                logger.error("LLM returned invalid response")
                raise Exception("LLM returned invalid response")

            llm_text = llm_response['text']
            if llm_text is None:
                logger.error("LLM returned None text")
                raise Exception("LLM returned None text")

            html_code = find_text_in_between_tags(llm_text,
                                                   start_tag="<!DOCTYPE html>",
                                                   end_tag="</html>",
                                                   inclusive=True)

            if not html_code:
                logger.error("No HTML content found in LLM response after parsing.") # Logs the error
                # Raise the custom exception to trigger retry
                raise NoHtmlContentError("No HTML content found in LLM response")
        except LLMInvalidResponseError as e:
            logger.error(f"LLM invalid response error: {e}")
            # Re-raise the LLMInvalidResponseError to trigger retry
            raise
        except NoHtmlContentError as e:
            logger.error(f"No HTML content error: {e}")
            # Re-raise the NoHtmlContentError to trigger retry
            raise
        except Exception as e:
            # This general exception catch is for unexpected errors during the LLM call or parsing
            logger.error(f"An unexpected error occurred during slide generation: {e}")
            # If it's a completely unexpected error
            raise # Important: Re-raise the exception for tenacity to catch it.

        # Apply auto-fit CSS to ensure content fits within 720px height
        html_code = self.apply_autofit_css(html_code)

        if review:
            logger.info("Reviewing generated HTML...")
            # Run synchronous renderHTML in a thread pool
            html_img = await asyncio.to_thread(
                self.rndr.renderHTML, html_str=html_code, resize=True, resize_ratio=self.render_resize_ratio
            )
            # Await the async reviewer call
            review_response = await self.reviewer(html_code, html_img, reviewer_llm)
            html_code = review_response['html_code']

            # Accumulate token counts from the reviewer step
            gen_input_token_count += review_response['input_token_count']
            gen_output_token_count += review_response['output_token_count']

            # Re-apply auto-fit CSS after review
            html_code = self.apply_autofit_css(html_code)

        return {
            "html_code": html_code,
            "input_token_count": gen_input_token_count,
            "output_token_count": gen_output_token_count
        }