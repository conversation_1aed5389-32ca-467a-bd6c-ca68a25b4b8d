<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Weaknesses of Our Current Security Posture</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f2f5;
            font-family: 'Roboto', sans-serif;
        }

        .slide-container {
            width: 1280px;
            height: 720px;
            background-color: #0a192f;
            background-image:
                linear-gradient(rgba(10, 25, 47, 0.85), rgba(10, 25, 47, 0.85)),
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a3a6e' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            color: #e6f1ff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 60px;
            box-sizing: border-box;
        }

        .slide-content {
            flex-grow: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            width: 100%;
            padding-top: 0;
        }

        .slide-content h1 {
            font-size: 2.8em;
            color: #64ffda;
            font-weight: 700;
            margin: 0 0 15px 0;
            line-height: 1.2;
        }
        
        .title-divider {
            width: 120px;
            height: 3px;
            background-color: #64ffda;
            margin-bottom: 40px;
        }

        .main-layout {
            display: flex;
            width: 100%;
            height: 100%;
            align-items: center;
            gap: 60px;
        }

        .visual-column {
            flex: 0 0 40%; /* Assign 40% of the width to the visual column */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .castle-container {
            position: relative;
            width: 350px; /* Container for the castle */
            height: 350px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .castle-icon {
            width: 100%;
            height: 100%;
            filter: invert(84%) sepia(29%) saturate(399%) hue-rotate(99deg) brightness(104%) contrast(101%); /* #64ffda color */
        }
        
        .inner-weakness-icon {
            position: absolute;
            width: 80px; /* Size of the lock icon */
            height: 80px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -60%); /* Adjust position to center inside the castle */
            filter: invert(78%) sepia(11%) saturate(1147%) hue-rotate(173deg) brightness(92%) contrast(88%); /* #a8b2d1 color */
        }

        .visual-label {
            margin-top: 20px;
            font-size: 1.3em;
            font-weight: 700;
            color: #64ffda;
            text-align: center;
            border: 2px solid #64ffda;
            padding: 8px 16px;
            border-radius: 5px;
        }

        .text-column {
            flex: 1; /* The text column takes the remaining space */
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .weakness-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .weakness-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            font-size: 1.1em;
            color: #ccd6f6;
            line-height: 1.6;
        }

        .weakness-list .icon {
            width: 24px;
            height: 24px;
            margin-right: 15px;
            flex-shrink: 0;
            margin-top: 5px;
            filter: invert(84%) sepia(29%) saturate(399%) hue-rotate(99deg) brightness(104%) contrast(101%); /* #64ffda color */
        }
        
        .weakness-list strong {
            font-weight: 700;
            color: #a8b2d1;
        }

    </style>
</head>
<body>

    <div class="slide-container">
        <div class="slide-content">
            <h1>The Weaknesses of Our Current Security Posture</h1>
            <div class="title-divider"></div>
            <div class="main-layout">
                <div class="visual-column">
                    <div class="castle-container">
                        <img src="https://www.svgrepo.com/show/514338/castle.svg" alt="Castle" class="castle-icon">
                        <img src="https://www.svgrepo.com/show/315900/unlocked-padlock.svg" alt="Unlocked" class="inner-weakness-icon">
                    </div>
                    <p class="visual-label">Perimeter Security</p>
                </div>
                <div class="text-column">
                    <ul class="weakness-list">
                        <li>
                            <img src="https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg" alt="Warning Icon" class="icon">
                            <div><strong>Increased Sophistication of Cyberattacks:</strong> Attackers are bypassing traditional perimeter defenses.</div>
                        </li>
                        <li>
                            <img src="https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg" alt="Warning Icon" class="icon">
                            <div><strong>Insider Threats:</strong> Malicious or negligent employees can compromise sensitive data.</div>
                        </li>
                        <li>
                            <img src="https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg" alt="Warning Icon" class="icon">
                            <div><strong>Complex IT Environment:</strong> Cloud adoption, remote work, and BYOD create new attack vectors.</div>
                        </li>
                        <li>
                            <img src="https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg" alt="Warning Icon" class="icon">
                            <div><strong>Compliance Requirements:</strong> Regulations like GDPR and CCPA demand stronger data protection measures.</div>
                        </li>
                        <li>
                            <img src="https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg" alt="Warning Icon" class="icon">
                            <div><strong>Lack of Visibility:</strong> Difficult to track user activity and identify suspicious behavior across the network.</div>
                        </li>
                         <li>
                            <img src="https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg" alt="Warning Icon" class="icon">
                            <div><strong>The Cost of Inaction:</strong> Breaches result in significant financial, reputational, and legal liabilities.</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

</body>
</html>