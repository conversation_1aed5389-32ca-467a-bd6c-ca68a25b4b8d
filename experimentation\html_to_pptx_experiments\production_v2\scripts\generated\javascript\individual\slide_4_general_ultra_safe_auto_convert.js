const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE POSITIONING & SIZING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer column widths

    // Two-column layout constants
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.5; // 55% of available width, rounded for safety
    const RIGHT_COL_X = 5.2; // Left col (0.3 + 4.5) + 0.4 gap
    const RIGHT_COL_W = 3.3; // 45% of available width, rounded for safety

    // Color Palette (from CSS)
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_BORDER = '1a3a6e';
    const COLOR_CARD_FILL = '1a2c46'; // Approximated from rgba(42, 68, 110, 0.3) on dark bg

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Main Title
    slide.addText("Zero Trust: Never Trust, Always Verify", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: CONTENT_WIDTH,
        h: 0.4,
        fontSize: 16,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2,
        h: 0.03,
        fill: { color: COLOR_PRIMARY_ACCENT },
    });

    // --- Left Column Content ---
    let currentY = CONTENT_START_Y;
    const leftContent = [
        { strong: "Identity-Centric Security:", text: "Verify every user and device before granting access." },
        { strong: "Microsegmentation:", text: "Isolate applications and data to limit the blast radius of a breach." },
        { strong: "Continuous Monitoring:", text: "Constantly monitor user activity, device health, and network traffic for suspicious behavior." },
        { strong: "Data-Centric Protection:", text: "Protect sensitive data at rest and in transit with encryption and DLP." },
        { strong: "Automation & Orchestration:", text: "Automate security tasks and workflows to improve efficiency and reduce response times." },
    ];

    const ICON_SIZE = 0.22;
    const ICON_MARGIN_RIGHT = 0.15;
    const TEXT_START_X = LEFT_COL_X + ICON_SIZE + ICON_MARGIN_RIGHT;
    const TEXT_WIDTH = LEFT_COL_W - ICON_SIZE - ICON_MARGIN_RIGHT;
    const ITEM_SPACING = 0.5;

    leftContent.forEach(item => {
        if (currentY + ITEM_SPACING > MAX_CONTENT_Y) return; // Overflow check

        // Icon (using a safe, basic shape as a placeholder)
        slide.addShape(pptx.shapes.OVAL, {
            x: LEFT_COL_X,
            y: currentY + 0.04, // Align vertically
            w: ICON_SIZE,
            h: ICON_SIZE,
            fill: { color: COLOR_PRIMARY_ACCENT, transparency: 30 },
            line: { color: COLOR_PRIMARY_ACCENT, width: 1 }
        });

        // List Item Text
        slide.addText([
            { text: item.strong + " ", options: { color: COLOR_TEXT_SECONDARY, bold: true } },
            { text: item.text, options: { color: COLOR_TEXT_PRIMARY } }
        ], {
            x: TEXT_START_X,
            y: currentY,
            w: TEXT_WIDTH,
            h: ITEM_SPACING - 0.1, // Reserve small gap
            fontSize: 9,
            lineSpacing: 12
        });

        currentY += ITEM_SPACING;
    });

    // Conclusion Text (at the bottom of the left column)
    const CONCLUSION_H = 0.6;
    const CONCLUSION_Y = MAX_CONTENT_Y - CONCLUSION_H;
    // Border line for conclusion
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: LEFT_COL_X,
        y: CONCLUSION_Y,
        w: 0.03,
        h: CONCLUSION_H,
        fill: { color: COLOR_PRIMARY_ACCENT }
    });
    slide.addText([
        { text: "Why it Works: ", options: { color: COLOR_PRIMARY_ACCENT, bold: true } },
        { text: "Zero Trust eliminates implicit trust, making it significantly harder for attackers to gain access to sensitive data.", options: { color: COLOR_TEXT_SECONDARY } }
    ], {
        x: LEFT_COL_X + 0.2,
        y: CONCLUSION_Y,
        w: LEFT_COL_W - 0.2,
        h: CONCLUSION_H,
        fontSize: 9,
        lineSpacing: 13
    });


    // --- Right Column Content ---
    currentY = CONTENT_START_Y;
    const rightContent = [
        { title: "Verify Explicitly", desc: "Authenticate and authorize based on all available data points." },
        { title: "Least Privilege Access", desc: "Limit user access with just-in-time and just-enough-access (JIT/JEA)." },
        { title: "Assume Breach", desc: "Minimize blast radius and segment access. Verify all sessions are encrypted." },
    ];

    const CARD_H = 1.1;
    const CARD_SPACING = 0.2;
    const CARD_ICON_SIZE = 0.5;
    const CARD_TEXT_X_OFFSET = CARD_ICON_SIZE + 0.2;

    rightContent.forEach(card => {
        if (currentY + CARD_H > MAX_CONTENT_Y) return; // Overflow check

        // Diagram Card (using a rounded rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: RIGHT_COL_X,
            y: currentY,
            w: RIGHT_COL_W,
            h: CARD_H,
            fill: { color: COLOR_CARD_FILL },
            line: { color: COLOR_BORDER, width: 1 },
            rectRadius: 0.08
        });

        // Diagram Icon (using a safe shape)
        slide.addShape(pptx.shapes.OVAL, {
            x: RIGHT_COL_X + 0.2,
            y: currentY + (CARD_H - CARD_ICON_SIZE) / 2, // Center vertically
            w: CARD_ICON_SIZE,
            h: CARD_ICON_SIZE,
            fill: { color: COLOR_PRIMARY_ACCENT, transparency: 50 }
        });

        // Diagram Text
        slide.addText(card.title, {
            x: RIGHT_COL_X + CARD_TEXT_X_OFFSET,
            y: currentY + 0.2,
            w: RIGHT_COL_W - CARD_TEXT_X_OFFSET - 0.2,
            h: 0.3,
            fontSize: 10,
            color: COLOR_TEXT_PRIMARY,
            bold: true
        });
        slide.addText(card.desc, {
            x: RIGHT_COL_X + CARD_TEXT_X_OFFSET,
            y: currentY + 0.5,
            w: RIGHT_COL_W - CARD_TEXT_X_OFFSET - 0.2,
            h: 0.4,
            fontSize: 9,
            color: COLOR_TEXT_SECONDARY
        });

        currentY += CARD_H + CARD_SPACING;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_4_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
