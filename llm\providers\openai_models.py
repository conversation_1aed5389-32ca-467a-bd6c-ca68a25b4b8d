import io
import base64
from typing import List, Union
from openai import OpenAI
from PIL import Image
import asyncio 

class OpenAI_LLM:
    def __init__(self, api_key: str, model: str = 'gpt-3.5-turbo', temperature: float = 0):
        self.client = OpenAI(api_key=api_key)
        self.model = model
        self.temperature = temperature

    # Make this method async
    async def call(self, query: str):
        # OpenAI client calls are often synchronous. Use asyncio.to_thread.
        response = await asyncio.to_thread(
            self.client.chat.completions.create,
            model=self.model,
            messages=[{"role": "user", "content": query}],
            temperature=self.temperature
        )
        # Extract and return the text. Adjust if your response object is different.
        return {'text': response.choices[0].message.content}

    # Make this method async
    async def call_with_images(self, query: str, images: Union[List[Image.Image], Image.Image]):
        # This part depends heavily on which OpenAI model you're using (e.g., GPT-4o, GPT-4-Vision)
        # and how you're converting PIL Image to a format OpenAI expects (e.g., base64).
        # This is a placeholder and would need the actual base64 conversion logic.

        if not isinstance(images, list):
            images = [images]

        # Example for vision models (requires base64 encoding of images)
        # You would need a function like `pil_image_to_base64(image)`
        # from .utils import pil_image_to_base64 # Assuming you have this helper

        content_parts = [{"type": "text", "text": query}]
        for img in images:
            # You'll need to convert PIL Image to base64
            # Example (you need to implement pil_image_to_base64):
            # base64_image = pil_image_to_base64(img)
            # content_parts.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}})
            pass # Placeholder - **YOU NEED TO IMPLEMENT IMAGE TO BASE64 CONVERSION HERE**

        # If you are using a model that handles images directly (e.g., Anthropic's image_message)
        # or if you have a different setup, adjust accordingly.
        # For OpenAI GPT-4 Vision, you send base64 encoded images.

        # Example using a dummy placeholder for image content for now:
        # For a complete example with base64, see OpenAI's vision API documentation.
        messages_content = [{"type": "text", "text": query}]
        # Add image URLs/base64 content here if using vision models.
        # For this example, let's just assume we're sending text only if images are not supported
        # or if you handle image uploads separately.
        # FOR A VISION MODEL, THIS PART NEEDS TO BE COMPLETED WITH BASE64 IMAGE DATA.

        # Ensure you handle base64 encoding of images if using models like GPT-4o or GPT-4-Vision
        # For demonstration, I'll keep it simple, but this is critical for vision models.
        
        # If your OpenAI model does NOT support images directly, you might need to
        # use an external service or a different LLM for multi-modal tasks.

        response = await asyncio.to_thread(
            self.client.chat.completions.create,
            model=self.model,
            messages=[{"role": "user", "content": content_parts}], # content_parts for vision
            temperature=self.temperature
        )
        return {'text': response.choices[0].message.content}