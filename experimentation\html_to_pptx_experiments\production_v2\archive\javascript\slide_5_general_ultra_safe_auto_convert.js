const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE DEFAULTS & CONSTANTS
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const TITLE_DIVIDER_Y = 0.8;

    // Colors from HTML
    const BG_COLOR = '0a192f';
    const PRIMARY_COLOR = '64ffda';
    const TEXT_COLOR_LIGHT = 'ccd6f6';
    const TEXT_COLOR_MEDIUM = 'a8b2d1';
    const SHAPE_FILL_COLOR = '2a4365'; // rgba(42, 67, 101, 0.5) -> solid
    const SHAPE_BORDER_COLOR = '1d3b66';

    // Two-column layout constants
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.2;
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.4; // 4.9
    const RIGHT_COL_W = SLIDE_WIDTH - RIGHT_COL_X - SAFE_MARGIN - 0.3; // 4.5

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Slide Title
    slide.addText("Building a Secure Foundation with Cloud Technologies", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: SLIDE_WIDTH - (2 * SAFE_MARGIN),
        h: TITLE_H,
        fontSize: 16,
        color: PRIMARY_COLOR,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: TITLE_DIVIDER_Y,
        w: 1.2,
        h: 0.03,
        fill: { color: PRIMARY_COLOR }
    });

    // --- LEFT COLUMN: Architecture Diagram ---
    const architectureLayers = [
        { text: "Identity: Azure AD Conditional Access" },
        { text: "Application: Web Apps / Microservices" },
        { text: "Data: S3 Buckets / Azure Blob Storage" },
        { text: "Compute: EC2 Instances / Azure VMs" },
        { text: "Network: Azure Virtual Network / NSGs" },
        { text: "Infrastructure: Cloud Provider" }
    ];

    let currentY = CONTENT_START_Y + 0.5; // Start lower for visual centering
    const layerHeight = 0.45;
    const layerSpacing = 0.1;
    const iconSize = 0.2;

    architectureLayers.forEach(layer => {
        // Check for vertical overflow before adding a layer
        if ((currentY + layerHeight) > MAX_CONTENT_Y) return;

        // Layer Box (Rounded Rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: LEFT_COL_X,
            y: currentY,
            w: LEFT_COL_W,
            h: layerHeight,
            fill: { color: SHAPE_FILL_COLOR },
            line: { color: SHAPE_BORDER_COLOR, width: 1 },
            rectRadius: 0.08
        });

        // Layer Text
        slide.addText(layer.text, {
            x: LEFT_COL_X + 0.15,
            y: currentY,
            w: LEFT_COL_W - 0.6, // Leave space for icon
            h: layerHeight,
            fontSize: 9,
            color: TEXT_COLOR_LIGHT,
            valign: 'middle'
        });

        // Lock Icon (using a safe custom shape)
        // A simple "lock" shape: a square base with an arc on top.
        const iconX = LEFT_COL_X + LEFT_COL_W - 0.4;
        const iconY = currentY + (layerHeight - iconSize) / 2;
        // Arc for the lock shackle
        slide.addShape(pptx.shapes.ARC, {
            x: iconX,
            y: iconY,
            w: iconSize,
            h: iconSize,
            line: { color: PRIMARY_COLOR, width: 1.5 },
            angleRange: [180, 360]
        });
        // Rectangle for the lock body
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: iconX,
            y: iconY + (iconSize / 2),
            w: iconSize,
            h: iconSize / 2,
            fill: { color: PRIMARY_COLOR }
        });

        currentY += layerHeight + layerSpacing;
    });

    // --- RIGHT COLUMN: Technology Details ---
    const techItems = [
        { title: "Identity & Access Management (IAM)", details: "Azure AD Conditional Access + Passwordless Auth + Behavioral Biometrics" },
        { title: "Device Security & Endpoint Management", details: "Microsoft Endpoint Manager (Intune) + DPA + Remote Browser Isolation" },
        { title: "Network Security & Microsegmentation", details: "Azure VNets/NSGs + Cloudflare ZTNA + AI Anomaly Detection" },
        { title: "Data Security & Protection", details: "Microsoft Purview + Cloud-Native Key Management (KMS)" },
        { title: "Automation & Orchestration", details: "Azure Logic Apps + Ansible / Terraform (IaC)" }
    ];

    currentY = CONTENT_START_Y + 0.2; // Reset Y for the right column
    const itemHeight = 0.6;
    const itemSpacing = 0.15;

    techItems.forEach(item => {
        // Check for vertical overflow before adding an item
        if ((currentY + itemHeight) > MAX_CONTENT_Y) return;

        // Tech Item Title
        slide.addText(item.title, {
            x: RIGHT_COL_X,
            y: currentY,
            w: RIGHT_COL_W,
            h: 0.2,
            fontSize: 10,
            color: PRIMARY_COLOR,
            bold: true
        });

        // Tech Item Details
        slide.addText(item.details, {
            x: RIGHT_COL_X,
            y: currentY + 0.2,
            w: RIGHT_COL_W,
            h: 0.4,
            fontSize: 9,
            color: TEXT_COLOR_MEDIUM,
            lineSpacing: 12 // Ultra-safe line spacing in points
        });

        currentY += itemHeight + itemSpacing;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_5_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
