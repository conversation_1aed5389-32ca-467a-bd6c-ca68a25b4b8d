const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0; // Start content higher for this layout
    const MAX_CONTENT_Y = 5.2; // Allow content to go slightly lower to fit footer

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        titleText: '1F2937', // text-gray-800
        chartAxisText: '4B5563', // text-gray-600
        chartAxisLine: '9CA3AF', // border-gray-400
        chartAxisTitle: '374151', // text-gray-700
        rightColTitle: '0E7490', // text-cyan-700
        rightColBorder: 'A5F3FC', // border-cyan-200
        insightTitle: '155E75', // text-cyan-800
        insightText: '4B5563', // text-gray-600
        policyBoxBg: 'CFFAFE', // bg-cyan-50
        policyBoxBorder: '22D3EE', // border-cyan-500
        policyBoxText: '374151', // text-gray-700
        footerText: '6B7280', // text-gray-500
        chartLines: {
            solar: 'facc15',
            onshore: '3b82f6',
            offshore: '1e3a8a',
            coal: '1f2937',
            gas: '9ca3af'
        }
    };

    // Font Sizes (Professional & Consistent)
    const FONT_SIZES = {
        mainTitle: 16, // text-4xl -> 16
        chartTitle: 11, // text-xl -> 11
        chartAxisLabel: 8,
        chartAxisTitle: 9,
        rightColTitle: 12, // text-2xl -> 12
        insightTitle: 10, // 1.2em -> 10
        insightText: 9, // 1.1em -> 9
        legendText: 9,
        footer: 8
    };

    // Layout Calculations
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 5.7; // 3/5 width approx.
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.4;
    const RIGHT_COL_W = SLIDE_WIDTH - RIGHT_COL_X - SAFE_MARGIN;

    // =======================================================================
    // 2. SLIDE BACKGROUND & HEADER (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    // Main Title
    slide.addText("The Evolving Economics of Renewables & Policy Influence", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: SLIDE_WIDTH - (2 * SAFE_MARGIN),
        h: 0.5,
        fontSize: FONT_SIZES.mainTitle,
        color: COLORS.titleText,
        bold: true,
        valign: 'top'
    });

    // =======================================================================
    // 3. LEFT COLUMN: CHART (Robust & Corruption-Proof)
    // =======================================================================

    // Chart Title
    slide.addText("Levelized Cost of Energy (LCOE) Trends (2010-2023)", {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        h: 0.3,
        fontSize: FONT_SIZES.chartTitle,
        color: COLORS.chartAxisText,
        bold: true,
        align: 'center'
    });

    const chartX = LEFT_COL_X + 0.4;
    const chartY = CONTENT_START_Y + 0.4;
    const chartW = LEFT_COL_W - 0.4;
    const chartH = 3.2;

    // Chart Data (PROVEN SAFE FORMAT)
    const chartData = [
        {
            name: "Solar PV",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [150, 110, 80, 60, 40]
        },
        {
            name: "Onshore Wind",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [80, 65, 50, 40, 35]
        },
        {
            name: "Offshore Wind",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [180, 140, 110, 90, 70]
        },
        {
            name: "Coal",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [100, 100, 100, 100, 100]
        },
        {
            name: "Natural Gas",
            labels: ["2010", "2013", "2016", "2019", "2023"],
            values: [60, 70, 80, 75, 75]
        }
    ];

    // Chart Options (ULTRA-SAFE WHITELIST - NO CORRUPTION)
    const chartOptions = {
        x: chartX, y: chartY, w: chartW, h: chartH,
        chartColors: [
            COLORS.chartLines.solar,
            COLORS.chartLines.onshore,
            COLORS.chartLines.offshore,
            COLORS.chartLines.coal,
            COLORS.chartLines.gas
        ],
        lineSize: 2,
        showLegend: false, // Build custom legend for professional control
        valAxisTitle: 'LCOE (USD/MWh)',
        valAxisTitleFontSize: FONT_SIZES.chartAxisTitle,
        valAxisTitleColor: COLORS.chartAxisTitle,
        valAxisLabelFontSize: FONT_SIZES.chartAxisLabel,
        valAxisMaxVal: 200,
        valAxisMinVal: 0,
        valAxisMajorUnit: 25,
        catAxisLabelFontSize: FONT_SIZES.chartAxisLabel,
        // CRITICAL: No lineDash, gridLine, plotArea, or other corruption-causing options
    };

    // Add the chart
    slide.addChart(pptx.ChartType.line, chartData, chartOptions);

    // =======================================================================
    // 4. RIGHT COLUMN: INSIGHTS & LEGEND (Professional Layout)
    // =======================================================================

    let currentY = CONTENT_START_Y;

    // Right Column Border
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X - 0.2, y: currentY, w: 0, h: 4.0,
        line: { color: 'E5E7EB', width: 2 }
    });

    // Right Column Title
    slide.addText("Cost & Policy Insights", {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: 0.3,
        fontSize: FONT_SIZES.rightColTitle,
        color: COLORS.rightColTitle,
        bold: true
    });
    currentY += 0.3;
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: 0,
        line: { color: COLORS.rightColBorder, width: 2 }
    });
    currentY += 0.3;

    // Insight 1: Cost Reduction
    slide.addText("Dramatic Cost Reduction", {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: 0.25,
        fontSize: FONT_SIZES.insightTitle,
        color: COLORS.insightTitle,
        bold: true
    });
    currentY += 0.25;
    slide.addText("Solar PV and wind technologies have seen a steep decline in costs, making them highly competitive against traditional fossil fuels, which have remained stable or fluctuated.", {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: 0.6,
        fontSize: FONT_SIZES.insightText,
        color: COLORS.insightText
    });
    currentY += 0.7;

    // Insight 2: Role of Policy
    slide.addText("The Role of Policy", {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: 0.25,
        fontSize: FONT_SIZES.insightTitle,
        color: COLORS.insightTitle,
        bold: true
    });
    currentY += 0.25;

    // Policy Box
    const policyBoxH = 0.9;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: policyBoxH,
        fill: { color: COLORS.policyBoxBg },
        rectRadius: 0.1
    });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: RIGHT_COL_X, y: currentY, w: 0.05, h: policyBoxH,
        fill: { color: COLORS.policyBoxBorder }
    });
    slide.addText("Government incentives like feed-in tariffs and renewable portfolio standards have been crucial. They create stable markets, reduce investment risks, and are a primary driver for the observed cost reductions and accelerated deployment.", {
        x: RIGHT_COL_X + 0.15, y: currentY + 0.05, w: RIGHT_COL_W - 0.2, h: policyBoxH - 0.1,
        fontSize: FONT_SIZES.insightText,
        color: COLORS.policyBoxText
    });
    currentY += policyBoxH + 0.2;

    // Custom Legend
    const legendItems = [
        { text: 'Solar PV', color: COLORS.chartLines.solar, type: 'rect' },
        { text: 'Onshore Wind', color: COLORS.chartLines.onshore, type: 'rect' },
        { text: 'Offshore Wind', color: COLORS.chartLines.offshore, type: 'rect' },
        { text: 'Coal (Dashed)', color: COLORS.chartLines.coal, type: 'line' },
        { text: 'Natural Gas', color: COLORS.chartLines.gas, type: 'rect' }
    ];

    legendItems.forEach(item => {
        if (item.type === 'rect') {
            slide.addShape(pptx.shapes.RECTANGLE, {
                x: RIGHT_COL_X, y: currentY, w: 0.2, h: 0.2,
                fill: { color: item.color },
                line: { color: 'D1D5DB', width: 1 }
            });
        } else { // Dashed line for Coal
            // PptxGenJS does not support dashed lines in shapes reliably.
            // We simulate it with a solid line as a safe fallback.
            slide.addShape(pptx.shapes.LINE, {
                x: RIGHT_COL_X, y: currentY + 0.1, w: 0.2, h: 0,
                line: { color: item.color, width: 2 }
            });
        }
        slide.addText(item.text, {
            x: RIGHT_COL_X + 0.3, y: currentY - 0.05, w: RIGHT_COL_W - 0.3, h: 0.3,
            fontSize: FONT_SIZES.legendText,
            color: COLORS.insightText
        });
        currentY += 0.25;
    });

    // =======================================================================
    // 5. FOOTER (Professional & Compact)
    // =======================================================================
    slide.addText("Source: IRENA Renewable Power Generation Costs in 2024, Govt. Energy Agency Reports", {
        x: RIGHT_COL_X,
        y: MAX_CONTENT_Y,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: FONT_SIZES.footer,
        color: COLORS.footerText,
        align: 'right'
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide6_general_economics.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
