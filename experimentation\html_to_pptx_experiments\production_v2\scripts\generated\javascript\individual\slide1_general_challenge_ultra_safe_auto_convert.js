const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CONSTANTS & CONFIGURATION
    // =======================================================================
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0; // Higher start for dense content
    const MAX_CONTENT_Y = 4.8;
    const COLORS = {
        leftPanelBg: '0D1B2A',
        leftPanelText: 'FFFFFF',
        rightPanelBg: 'F5F7FA',
        rightPanelText: '1C2833',
        title: '1A237E',
        border: 'D0D5DD'
    };
    const FONT_SIZES = {
        title: 16,
        panelTitle: 12,
        bulletTitle: 10,
        bulletDescription: 9
    };

    // =======================================================================
    // 2. HELPER FUNCTIONS
    // =======================================================================
    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x,
                y: options.y,
                w: options.w,
                h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            slide.addShape(pptx.shapes.RECTANGLE, {
                x: options.x,
                y: options.y,
                w: options.w,
                h: options.h,
                fill: { color: 'F3F4F6' },
                line: { color: 'D1D5DB', width: 1 }
            });
            slide.addText(fallbackText, {
                x: options.x + 0.1,
                y: options.y + options.h / 2 - 0.1,
                w: options.w - 0.2,
                h: 0.2,
                fontSize: 8,
                color: '6B7280',
                align: 'center',
                valign: 'middle'
            });
        }
    }


    // =======================================================================
    // 3. SLIDE CONTENT
    // =======================================================================
    slide.background = { color: 'FFFFFF' };

    // Title
    slide.addText('The Challenge — Legacy COBOL Application', {
        x: SAFE_MARGIN,
        y: 0.3,
        w: SLIDE_WIDTH - 2 * SAFE_MARGIN,
        h: 0.5,
        fontSize: FONT_SIZES.title,
        color: COLORS.title,
        bold: true
    });

    // Split Container Backgrounds
    const panelWidth = (SLIDE_WIDTH - 2 * SAFE_MARGIN - 0.1) / 2; // Account for border
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: CONTENT_START_Y,
        w: panelWidth,
        h: MAX_CONTENT_Y - CONTENT_START_Y,
        fill: { color: COLORS.leftPanelBg }
    });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN + panelWidth + 0.1,
        y: CONTENT_START_Y,
        w: panelWidth,
        h: MAX_CONTENT_Y - CONTENT_START_Y,
        fill: { color: COLORS.rightPanelBg }
    });


    // Panel Content - Function for Reusability
    function addPanelContent(panelData, x, color) {
        let currentY = CONTENT_START_Y + 0.3;
        slide.addText(panelData.title, { x: x, y: currentY, w: panelWidth, h: 0.4, fontSize: FONT_SIZES.panelTitle, color: color, bold: true, align: 'center' });
        currentY += 0.5;

        panelData.items.forEach(item => {
            addImageWithFallback(slide, item.icon, { x: x + 0.1, y: currentY + 0.05, w: 0.3, h: 0.3 }, 'Icon');
            slide.addText(item.title, { x: x + 0.5, y: currentY, w: panelWidth - 0.6, h: 0.3, fontSize: FONT_SIZES.bulletTitle, color: color, bold: true });
            currentY += 0.35;
            slide.addText(item.description, { x: x + 0.5, y: currentY, w: panelWidth - 0.6, h: 0.4, fontSize: FONT_SIZES.bulletDescription, color: color });
            currentY += 0.5;
        });
    }

    const leftPanelData = {
        title: 'Technical Debt',
        items: [
            { icon: 'https://www.svgrepo.com/show/527627/box.svg', title: 'Monolithic & Tightly Coupled', description: 'Shared state and cross-cutting dependencies make even small changes high-risk and time-consuming.' },
            { icon: 'https://www.svgrepo.com/show/421594/break-broken-chain.svg', title: 'Fragile Batch Orchestration', description: 'Complex job chains, limited automated tests, and lengthy regressions slow safe delivery.' },
            { icon: 'https://www.svgrepo.com/show/444838/money.svg', title: 'High Run/Maintain Cost', description: 'Mainframe MIPS/licensing, manual deployments, and specialized operations inflate OPEX.' },
            { icon: 'https://www.svgrepo.com/show/441823/delete-person.svg', title: 'Talent Scarcity & Key-Person Risk', description: 'Shrinking COBOL workforce, SME bottlenecks, and undocumented tribal knowledge increase risk.' },
            { icon: 'https://www.svgrepo.com/show/470395/lock.svg', title: 'Hardware & Vendor Lock-in', description: 'Fixed capacity and long procurement cycles limit responsiveness; DR is tied to specific facilities.' }
        ]
    };

    const rightPanelData = {
        title: 'Business Constraints',
        items: [
            { icon: 'https://www.svgrepo.com/show/344428/arrows-expand.svg', title: 'Limited Scalability', description: 'Cannot elastically meet peak workloads; batch overruns jeopardize SLAs and downstream processes.' },
            { icon: 'https://www.svgrepo.com/show/414987/speedometer-speed-fast.svg', title: 'Slower Feature Velocity', description: 'Multi-month release cycles, heavy change approvals, and uncertain blast radius inhibit innovation.' },
            { icon: 'https://www.svgrepo.com/show/373314/node.svg', title: 'Integration Friction', description: 'Lacks modern APIs; reliance on EBCDIC/VSAM complicates data exchange with cloud/SaaS.' },
            { icon: 'https://www.svgrepo.com/show/493629/warning-triangle.svg', title: 'Operational Risk & Compliance', description: 'Minimal observability, weak auditability, and single-site resilience make RTO/RPO difficult to meet.' },
            { icon: 'https://www.svgrepo.com/show/503005/chat.svg', title: 'Customer Expectation Gaps', description: 'Real-time access, omnichannel experiences, and automated workflows are hard to deliver.' }
        ]
    };


    addPanelContent(leftPanelData, SAFE_MARGIN, COLORS.leftPanelText);
    addPanelContent(rightPanelData, SAFE_MARGIN + panelWidth + 0.1, COLORS.rightPanelText);


    return pptx.writeFile({ fileName: 'generated/presentations/slide1_general_challenge.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
