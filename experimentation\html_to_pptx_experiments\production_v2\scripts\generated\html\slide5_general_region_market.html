<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regional Market Dynamics: A Global Perspective</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Lato', sans-serif;
        }
        .slide-container {
            width: 1280px;
            height: 720px;
        }
        .map-container {
            background-image: url('https://img.freepik.com/premium-photo/world-map-visualization-showing-global-data-trends-with-colorcoded-regions_1314467-175172.jpg?w=740');
            background-size: cover;
            background-position: center;
            border-radius: 0.5rem;
        }
        .region-label {
            position: absolute;
            padding: 4px 10px;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            font-size: 0.9em;
            font-weight: bold;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body class="bg-gray-100 flex justify-center items-center h-screen">

    <div class="slide-container bg-white shadow-lg flex flex-col p-16">
        <!-- Slide Title -->
        <h1 class="text-4xl font-bold text-gray-800 mb-6">Regional Market Dynamics: A Global Perspective</h1>

        <!-- Main Content Area -->
        <div class="slide-content flex-grow flex items-center space-x-12">
            
            <!-- Left Column: World Map -->
            <div class="w-3/5 h-full flex flex-col items-center justify-center relative">
                <div class="map-container w-full h-full shadow-lg relative">
                    <!-- Labels for key regions -->
                    <div class="region-label" style="top: 25%; left: 65%;">
                        Asia
                        <div class="w-0 h-0 border-l-8 border-l-transparent border-t-8 border-t-black/60 border-r-8 border-r-transparent absolute -bottom-2 left-1/2 -translate-x-1/2"></div>
                    </div>
                    <div class="region-label" style="top: 30%; left: 45%;">
                        Europe
                        <div class="w-0 h-0 border-l-8 border-l-transparent border-t-8 border-t-black/60 border-r-8 border-r-transparent absolute -bottom-2 left-1/2 -translate-x-1/2"></div>
                    </div>
                    <div class="region-label" style="top: 35%; left: 15%;">
                        North America
                        <div class="w-0 h-0 border-l-8 border-l-transparent border-t-8 border-t-black/60 border-r-8 border-r-transparent absolute -bottom-2 left-1/2 -translate-x-1/2"></div>
                    </div>
                    <div class="region-label" style="top: 60%; left: 25%;">
                        South America
                        <div class="w-0 h-0 border-l-8 border-l-transparent border-t-8 border-t-black/60 border-r-8 border-r-transparent absolute -bottom-2 left-1/2 -translate-x-1/2"></div>
                    </div>
                     <div class="region-label" style="top: 65%; left: 50%;">
                        Africa
                        <div class="w-0 h-0 border-l-8 border-l-transparent border-t-8 border-t-black/60 border-r-8 border-r-transparent absolute -bottom-2 left-1/2 -translate-x-1/2"></div>
                    </div>
                </div>
                 <div class="w-full flex justify-start items-center mt-4 space-x-4">
                    <span class="text-sm font-semibold text-gray-600">Capacity:</span>
                    <div class="h-4 w-16 bg-green-100 border border-gray-200 rounded"></div>
                    <span class="text-xs text-gray-500">Low</span>
                    <div class="h-4 w-16 bg-green-300 border border-gray-200 rounded"></div>
                    <div class="h-4 w-16 bg-green-500 border border-gray-200 rounded"></div>
                    <div class="h-4 w-16 bg-green-700 border border-gray-200 rounded"></div>
                    <span class="text-xs text-gray-500">High</span>
                </div>
            </div>

            <!-- Right Column: Key Insights -->
            <div class="w-2/5 flex flex-col self-stretch">
                <h2 class="text-2xl font-bold text-teal-700 mb-4 border-b-2 border-teal-200 pb-2">Regional Highlights</h2>
                <div class="space-y-4 text-gray-700" style="font-size: 1.1em;">
                    
                    <div class="p-3 bg-green-50 rounded-lg border-l-4 border-green-700">
                        <h3 class="font-bold text-teal-800">Asia (Leading)</h3>
                        <p class="text-gray-600 text-sm">Led by China and India, the region dominates global capacity, particularly in solar and wind installations.</p>
                    </div>

                    <div class="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                        <h3 class="font-bold text-teal-800">Europe & North America (Strong Growth)</h3>
                        <p class="text-gray-600 text-sm">Driven by strong policy (EU targets, US IRA), both regions show significant investment and deployment across multiple technologies.</p>
                    </div>

                    <div class="p-3 bg-green-50 rounded-lg border-l-4 border-green-300">
                        <h3 class="font-bold text-teal-800">South America (Hydro Hub)</h3>
                        <p class="text-gray-600 text-sm">Brazil's leadership in hydropower and bioenergy anchors the continent's renewable profile.</p>
                    </div>

                    <div class="p-3 bg-green-50 rounded-lg border-l-4 border-green-200">
                        <h3 class="font-bold text-teal-800">Africa (High Potential)</h3>
                        <p class="text-gray-600 text-sm">Possesses vast solar and wind potential but requires investment in infrastructure and financing to unlock it.</p>
                    </div>

                </div>
                <div class="mt-auto text-xs text-gray-500 text-right pt-4">
                    Source: IEA Renewables 2024 Report
                </div>
            </div>
        </div>
    </div>

</body>
</html>