name: pptx-planner-staging-trigger
description: Build and deploy to Cloud Run staging service pptx-planner-staging on push to "^staging$" or "^develop$"
github:
  name: pptx-planner
  owner: DuyquanDuc
  push:
    branch: ^(staging|develop)$
filename: cloudbuild_stg.yaml
includeBuildLogs: INCLUDE_BUILD_LOGS_WITH_STATUS
serviceAccount: projects/gen-lang-client-**********/serviceAccounts/<EMAIL>
substitutions:
  _TRIGGER_ID: STAGING_TRIGGER_ID_PLACEHOLDER
  _AR_PROJECT_ID: gen-lang-client-**********
  _PLATFORM: managed
  _SERVICE_NAME: pptx-planner-staging
  _DEPLOY_REGION: asia-northeast1
  _AR_HOSTNAME: asia-northeast1-docker.pkg.dev
  _AR_REPOSITORY: cloud-run-source-deploy
tags:
  - gcp-cloud-build-deploy-cloud-run
  - gcp-cloud-build-deploy-cloud-run-managed
  - pptx-planner-staging
  - staging
