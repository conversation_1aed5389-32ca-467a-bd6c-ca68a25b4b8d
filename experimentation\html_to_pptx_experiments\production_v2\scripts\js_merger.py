"""
JavaScript merger (production_v2) - copied from the proven test script

This is a direct port of the working merger from:
experimentation/html_to_pptx_experiments/development/testing/test_js_merging.py

Only minimal adjustments were made:
- CLI support for optional js-dir and output-name
- Outputs organized under production_v2/generated
- No new logic added (avoid regressions)
"""

import re
import subprocess
import argparse
from pathlib import Path
from datetime import datetime
import shutil


def create_session_backup(out_root: Path) -> Path:
    """Create a timestamped session backup directory and move old files"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    session_dir = out_root / "javascript" / "sessions" / timestamp
    session_dir.mkdir(parents=True, exist_ok=True)

    # Move any existing merged files to session backup
    merged_dir = out_root / "javascript" / "merged"
    if merged_dir.exists():
        for merged_file in merged_dir.glob("*.js"):
            backup_file = session_dir / merged_file.name
            shutil.move(str(merged_file), str(backup_file))
            print(f"📦 Backed up previous merged file: {backup_file}")

    return session_dir


def find_existing_js_files(explicit_dir: Path | None = None):
    """Find existing JavaScript files (optionally in an explicit directory)"""
    if explicit_dir is not None:
        if not explicit_dir.exists():
            raise FileNotFoundError(f"JavaScript directory not found: {explicit_dir}")
        # Only process individual slide files, exclude merged files to prevent duplication
        js_files = []
        for js_file in explicit_dir.glob('*.js'):
            if (not js_file.name.endswith('_test.js') and
                '_merged' not in js_file.name and
                'consolidated' not in js_file.name):
                js_files.append(js_file)
        return sorted(js_files)

    # Session-based search - prioritize individual/ directory to prevent duplication
    search_dirs = [
        Path("generated/javascript/individual"),  # NEW: Individual slides only
        Path("scripts/generated/javascript/individual"),  # From parent directory
        Path("generated/javascript"),  # Fallback: old flat structure
        Path("scripts/generated/javascript"),  # From parent directory
        Path("generated_js"),  # OLD: Legacy location for compatibility
        Path("../development/testing/generated_js"),  # Fallback for testing
    ]

    js_files = []
    for search_dir in search_dirs:
        if search_dir.exists():
            for js_file in search_dir.glob("*.js"):
                # Exclude test files and merged files to prevent duplication
                if (not js_file.name.endswith('_test.js') and
                    '_merged' not in js_file.name and
                    'consolidated' not in js_file.name):
                    js_files.append(js_file)
            if js_files:  # If we found files, stop searching
                break

    return js_files


def extract_function_from_js(js_file_path: Path) -> dict | None:
    """Extract the main function from a JavaScript file and convert to function-scoped"""

    content = js_file_path.read_text(encoding='utf-8')

    # More robust extraction: find the createPresentation function and extract everything between its braces
    # This handles complex nested structures better

    # Find the start of the function
    function_start = content.find('function createPresentation()')
    if function_start == -1:
        print(f"⚠️ Could not find createPresentation function in {js_file_path.name}")
        return None

    # Find the opening brace
    brace_start = content.find('{', function_start)
    if brace_start == -1:
        print(f"⚠️ Could not find opening brace in {js_file_path.name}")
        return None

    # Find the matching closing brace by counting braces
    brace_count = 0
    brace_end = -1

    for i in range(brace_start, len(content)):
        if content[i] == '{':
            brace_count += 1
        elif content[i] == '}':
            brace_count -= 1
            if brace_count == 0:
                brace_end = i
                break

    if brace_end == -1:
        print(f"⚠️ Could not find matching closing brace in {js_file_path.name}")
        return None

    # Extract the function body (everything between the braces)
    function_body = content[brace_start + 1:brace_end].strip()

    # Extract slide name from filename
    slide_name = js_file_path.stem
    function_name = f"generate{slide_name.title().replace('_', '').replace('-', '')}"

    # Minimal cleaning - only remove the specific patterns we know are problematic
    cleaned_body = function_body

    # Remove PptxGenJS instantiation (these are always at the start)
    cleaned_body = re.sub(r'^\s*const pptx = new PptxGenJS\(\);?\s*', '', cleaned_body)
    cleaned_body = re.sub(r'^\s*pptx\.layout = [^;]+;?\s*', '', cleaned_body, flags=re.MULTILINE)

    # Remove slide declarations (these are always early in the function)
    cleaned_body = re.sub(r'^\s*const slide = pptx\.addSlide\([^)]*\);?\s*', '', cleaned_body, flags=re.MULTILINE)
    cleaned_body = re.sub(r'^\s*let slide = pptx\.addSlide\([^)]*\);?\s*', '', cleaned_body, flags=re.MULTILINE)
    cleaned_body = re.sub(r'^\s*var slide = pptx\.addSlide\([^)]*\);?\s*', '', cleaned_body, flags=re.MULTILINE)

    # Remove return statement (these are always at the end)
    cleaned_body = re.sub(r'\s*return pptx\.writeFile[^;]*;?\s*$', '', cleaned_body, flags=re.DOTALL)

    # Clean up extra whitespace
    cleaned_body = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_body)
    cleaned_body = cleaned_body.strip()

    # Convert to function-scoped approach (proven working structure)
    function_scoped_js = f"""
function {function_name}(pptx) {{
    // Function-scoped variables for {slide_name}
    const slide = pptx.addSlide();

    // Slide content
{cleaned_body}

    return slide;
}}"""

    return {
        'slide_name': slide_name,
        'function_name': function_name,
        'function_code': function_scoped_js
    }


def create_consolidated_js(js_functions: list, output_name: str, out_root: Path) -> Path:
    """Create consolidated JavaScript file from individual functions (unchanged logic)"""
    if not js_functions:
        raise Exception("No JavaScript functions provided")

    functions_code = '\n'.join([func['function_code'] for func in js_functions])
    function_calls = '\n'.join([f"    {func['function_name']}(pptx);" for func in js_functions])

    consolidated_content = f"""
// Consolidated Multi-Slide PowerPoint Generator
// Generated by JavaScript-based merging approach (production_v2)

const PptxGenJS = require('pptxgenjs');

{functions_code}

// Main presentation creation function
function createPresentation() {{
    const pptx = new PptxGenJS();

    console.log('Creating multi-slide presentation with {len(js_functions)} slides...');

{function_calls}

    console.log(`✅ Created presentation with ${{pptx.slides.length}} slides`);

    return pptx.writeFile({{ fileName: '{output_name}.pptx' }});
}}

// Execute the presentation creation
createPresentation()
    .then(() => {{
        console.log('✅ Multi-slide PowerPoint generated successfully!');
        console.log('📄 File saved as: {output_name}.pptx');
        process.exit(0);
    }})
    .catch(error => {{
        console.error('❌ Error generating presentation:', error);
        console.error(error.stack);
        process.exit(1);
    }});
"""

    # Save merged files to dedicated merged/ subdirectory
    merged_dir = out_root / "javascript" / "merged"
    merged_dir.mkdir(parents=True, exist_ok=True)
    consolidated_path = merged_dir / f"{output_name}_merged.js"
    consolidated_path.write_text(consolidated_content, encoding='utf-8')
    print(f"✅ Consolidated JavaScript saved: {consolidated_path}")
    return consolidated_path


def execute_consolidated_js(js_file_path: Path, out_root: Path) -> bool:
    """Execute consolidated JavaScript with Node.js (unchanged logic)"""
    print("🔄 Executing consolidated JavaScript with Node.js...")

    try:
        working_dir = js_file_path.parent
        result = subprocess.run(
            ['node', js_file_path.name],
            cwd=working_dir,
            capture_output=True,
            text=True,
            timeout=90
        )

        if result.returncode == 0:
            print("✅ Node.js execution successful!")
            print(f"📄 Output: {result.stdout}")

            # The PowerPoint filename is determined by the JavaScript content, not the JS filename
            # Extract the actual filename from the JS file stem (remove _merged suffix)
            base_name = js_file_path.stem.replace('_merged', '')
            expected_pptx_name = f"{base_name}.pptx"

            # Look for the PowerPoint file in the working directory (where Node.js creates it)
            pptx_file = working_dir / expected_pptx_name

            if pptx_file.exists():
                # Move to correct presentations directory
                presentations_dir = out_root / "presentations"
                presentations_dir.mkdir(parents=True, exist_ok=True)
                final_path = presentations_dir / pptx_file.name
                if final_path.exists():
                    final_path.unlink()
                pptx_file.rename(final_path)
                print(f"📄 PowerPoint file created: {final_path} ({final_path.stat().st_size:,} bytes)")
                return True
            else:
                # Fallback: search for any .pptx files in the working directory
                pptx_files = list(working_dir.glob("*.pptx"))
                if pptx_files:
                    pptx_file = pptx_files[0]  # Take the first one found
                    presentations_dir = out_root / "presentations"
                    presentations_dir.mkdir(parents=True, exist_ok=True)
                    final_path = presentations_dir / pptx_file.name
                    if final_path.exists():
                        final_path.unlink()
                    pptx_file.rename(final_path)
                    print(f"📄 PowerPoint file found and moved: {final_path} ({final_path.stat().st_size:,} bytes)")
                    return True
                else:
                    print(f"⚠️ Node.js succeeded but PowerPoint file not found. Expected: {expected_pptx_name}")
                    print(f"⚠️ Working directory: {working_dir}")
                    print(f"⚠️ Files in directory: {list(working_dir.glob('*'))}")
                    return False
        else:
            print(f"❌ Node.js execution failed with exit code {result.returncode}")
            print(f"❌ STDERR: {result.stderr}")
            print(f"❌ STDOUT: {result.stdout}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ Node.js execution timed out")
        return False
    except FileNotFoundError:
        print("❌ Node.js not found. Please install Node.js and ensure it's in PATH")
        return False
    except Exception as e:
        print(f"❌ Error executing Node.js: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='JS Merger (production_v2) - session-based approach')
    parser.add_argument('--js-dir', type=str, help='Directory with individual slide JS files')
    parser.add_argument('--output-name', type=str, default='merged_presentation', help='Output file base name')
    parser.add_argument('--no-backup', action='store_true', help='Skip session backup of previous merged files')
    args = parser.parse_args()

    # Use standardized generated directory structure
    out_root = Path(__file__).parent / 'generated'

    # Create session backup unless disabled
    if not args.no_backup:
        session_dir = create_session_backup(out_root)
        print(f"📦 Session backup created: {session_dir}")
    else:
        print("⚠️ Session backup skipped")

    # 1) Collect JS files (prefer explicit dir; otherwise search known locations)
    explicit_dir = Path(args.js_dir) if args.js_dir else None
    js_files = find_existing_js_files(explicit_dir)
    if not js_files:
        print("❌ No JavaScript files found to merge")
        return 1

    print(f"✅ Found {len(js_files)} JavaScript files:")
    for f in js_files:
        print(f"   - {f}")

    # 2) Extract functions
    js_functions = []
    for js_file in js_files:
        info = extract_function_from_js(js_file)
        if info:
            js_functions.append(info)
            print(f"   ✅ Extracted function: {info['function_name']}")

    if not js_functions:
        print("❌ No functions could be extracted from JavaScript files")
        return 1

    # 3) Create consolidated JS
    consolidated = create_consolidated_js(js_functions, args.output_name, out_root)

    # 4) Execute consolidated JS with Node
    success = execute_consolidated_js(consolidated, out_root)
    return 0 if success else 1


if __name__ == '__main__':
    raise SystemExit(main())
