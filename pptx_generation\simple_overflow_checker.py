"""
Simple overflow checker that doesn't require <PERSON><PERSON>
Uses heuristics and text analysis instead of browser automation
"""
import re
from typing import Dict, Any

def estimate_content_overflow(html_content: str, slide_width: int = 1280, slide_height: int = 720) -> Dict[str, Any]:
    """
    Estimate if content might overflow using simple heuristics
    Much faster and more reliable than Playwright approach
    """
    
    # Extract text content
    text_content = extract_text_from_html(html_content)
    
    # Count content elements
    content_metrics = analyze_content_density(html_content)
    
    # Estimate overflow risk
    overflow_risk = calculate_overflow_risk(content_metrics, text_content)
    
    result = {
        'status': 'no_overflow',
        'message': 'Content appears to fit within slide boundaries',
        'has_overflow': False,
        'overflow_risk': overflow_risk,
        'content_metrics': content_metrics,
        'recommendations': []
    }
    
    # Determine if overflow is likely (more sensitive thresholds)
    if overflow_risk > 0.5:  # Lowered from 0.7
        result['status'] = 'likely_overflow'
        result['has_overflow'] = True
        result['message'] = 'Content likely overflows slide boundaries'
        result['recommendations'] = generate_overflow_recommendations(content_metrics)
    elif overflow_risk > 0.3:  # Lowered from 0.4
        result['status'] = 'possible_overflow'
        result['has_overflow'] = True  # Also mark as overflow for possible cases
        result['message'] = 'Content might overflow - recommend optimization'
        result['recommendations'] = ['Consider reducing font sizes or content density']
    
    return result

def extract_text_from_html(html_content: str) -> str:
    """Extract visible text content from HTML"""
    # Remove script and style tags
    html_clean = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
    html_clean = re.sub(r'<style[^>]*>.*?</style>', '', html_clean, flags=re.DOTALL | re.IGNORECASE)
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', html_clean)
    
    # Clean up whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def analyze_content_density(html_content: str) -> Dict[str, int]:
    """Analyze content density indicators"""
    metrics = {
        'total_length': len(html_content),
        'text_elements': len(re.findall(r'<(h[1-6]|p|li|div)[^>]*>', html_content, re.IGNORECASE)),
        'list_items': len(re.findall(r'<li[^>]*>', html_content, re.IGNORECASE)),
        'paragraphs': len(re.findall(r'<p[^>]*>', html_content, re.IGNORECASE)),
        'headings': len(re.findall(r'<h[1-6][^>]*>', html_content, re.IGNORECASE)),
        'images': len(re.findall(r'<img[^>]*>', html_content, re.IGNORECASE)),
        'tables': len(re.findall(r'<table[^>]*>', html_content, re.IGNORECASE)),
        'nested_divs': len(re.findall(r'<div[^>]*>.*?<div', html_content, re.DOTALL | re.IGNORECASE))
    }
    
    return metrics

def calculate_overflow_risk(content_metrics: Dict[str, int], text_content: str) -> float:
    """Calculate overflow risk score (0.0 to 1.0)"""
    risk_score = 0.0

    # Text length risk (more aggressive)
    text_length = len(text_content)
    if text_length > 1500:  # Lowered threshold
        risk_score += 0.4
    elif text_length > 800:  # Lowered threshold
        risk_score += 0.2

    # Element count risk (more sensitive)
    total_elements = content_metrics['text_elements']
    if total_elements > 15:  # Lowered threshold
        risk_score += 0.3
    elif total_elements > 8:  # Lowered threshold
        risk_score += 0.15

    # List items risk (much more sensitive)
    if content_metrics['list_items'] > 10:  # Lowered threshold
        risk_score += 0.3
    elif content_metrics['list_items'] > 6:  # Lowered threshold
        risk_score += 0.15

    # Multiple sections risk (new check)
    if content_metrics['headings'] > 3:
        risk_score += 0.2
    elif content_metrics['headings'] > 2:
        risk_score += 0.1

    # Complex layout risk
    if content_metrics['nested_divs'] > 5:
        risk_score += 0.15

    # Table risk (tables can overflow easily)
    if content_metrics['tables'] > 0:
        risk_score += 0.1

    # HTML size risk (more aggressive)
    if content_metrics['total_length'] > 12000:  # Lowered threshold
        risk_score += 0.25
    elif content_metrics['total_length'] > 6000:  # Lowered threshold
        risk_score += 0.15

    # Specific pattern detection for dense content
    if 'section-item' in text_content or 'content-section' in text_content:
        risk_score += 0.2  # Multi-section layouts often overflow

    return min(risk_score, 1.0)  # Cap at 1.0

def generate_overflow_recommendations(content_metrics: Dict[str, int]) -> list:
    """Generate specific recommendations based on content analysis"""
    recommendations = []
    
    if content_metrics['list_items'] > 10:
        recommendations.append("Consider breaking long lists into multiple slides")
    
    if content_metrics['paragraphs'] > 8:
        recommendations.append("Reduce paragraph count or use bullet points")
    
    if content_metrics['total_length'] > 12000:
        recommendations.append("Content is very dense - consider splitting into multiple slides")
    
    if content_metrics['nested_divs'] > 5:
        recommendations.append("Simplify layout structure to prevent overflow")
    
    if content_metrics['tables'] > 0:
        recommendations.append("Tables may cause overflow - consider simplifying data presentation")
    
    if not recommendations:
        recommendations.append("Reduce font sizes or content density")
    
    return recommendations

# Async wrapper for compatibility
async def check_simple_overflow_async(html_content: str, slide_width: int = 1280, slide_height: int = 720) -> Dict[str, Any]:
    """Async wrapper for the simple overflow checker"""
    return estimate_content_overflow(html_content, slide_width, slide_height)
