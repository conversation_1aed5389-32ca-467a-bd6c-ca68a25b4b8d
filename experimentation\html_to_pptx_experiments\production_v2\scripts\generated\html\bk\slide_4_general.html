<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zero Trust: Never Trust, Always Verify</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f2f5;
            font-family: 'Roboto', sans-serif;
        }

        .slide-container {
            width: 1280px; /* 720p resolution width */
            height: 720px;
            background-color: #0a192f;
            background-image:
                linear-gradient(rgba(10, 25, 47, 0.85), rgba(10, 25, 47, 0.85)),
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a3a6e' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            color: #e6f1ff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 60px;
            box-sizing: border-box;
        }

        .slide-content {
            flex-grow: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            width: 100%;
            padding-top: 0;
        }

        .slide-content h1 {
            font-size: 2.8em;
            color: #64ffda;
            font-weight: 700;
            margin: 0 0 15px 0;
            line-height: 1.2;
        }
        
        .title-divider {
            width: 120px;
            height: 3px;
            background-color: #64ffda;
            margin-bottom: 40px;
        }

        .main-layout {
            display: flex;
            width: 100%;
            height: calc(100% - 120px); /* Adjust height to fill space below title */
            gap: 40px;
        }

        .left-column {
            width: 55%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .right-column {
            width: 45%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }

        .principles-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .principles-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 18px;
            font-size: 1.1em;
            color: #ccd6f6;
            line-height: 1.5;
        }

        .principles-list .icon {
            width: 22px;
            height: 22px;
            margin-right: 15px;
            flex-shrink: 0;
            margin-top: 4px;
            filter: invert(84%) sepia(29%) saturate(399%) hue-rotate(99deg) brightness(104%) contrast(101%); /* #64ffda */
        }

        .principles-list strong {
            font-weight: 700;
            color: #a8b2d1;
        }

        .conclusion-text {
            font-size: 1.15em;
            color: #a8b2d1;
            line-height: 1.6;
            border-left: 3px solid #64ffda;
            padding-left: 20px;
            margin-top: 20px;
        }

        .conclusion-text strong {
            color: #64ffda;
            font-weight: 700;
        }

        .diagram-card {
            background-color: rgba(42, 68, 110, 0.3);
            border: 1px solid #1a3a6e;
            border-radius: 8px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            gap: 20px;
            transition: all 0.3s ease;
        }

        .diagram-card:hover {
            border-color: #64ffda;
            transform: translateY(-5px);
        }

        .diagram-icon {
            width: 50px;
            height: 50px;
            flex-shrink: 0;
            filter: invert(84%) sepia(29%) saturate(399%) hue-rotate(99deg) brightness(104%) contrast(101%); /* #64ffda */
        }

        .diagram-text h3 {
            margin: 0 0 5px 0;
            font-size: 1.2em;
            color: #ccd6f6;
            font-weight: 700;
        }

        .diagram-text p {
            margin: 0;
            font-size: 0.95em;
            color: #a8b2d1;
            line-height: 1.4;
        }

    </style>
</head>
<body>

    <div class="slide-container">
        <div class="slide-content">
            <h1>Zero Trust: Never Trust, Always Verify</h1>
            <div class="title-divider"></div>
            <div class="main-layout">
                <div class="left-column">
                    <div>
                        <ul class="principles-list">
                            <li>
                                <img src="https://www.svgrepo.com/show/506443/user-id.svg" alt="Icon" class="icon">
                                <div><strong>Identity-Centric Security:</strong> Verify every user and device before granting access.</div>
                            </li>
                            <li>
                                <img src="https://www.svgrepo.com/show/493736/microsegmentation.svg" alt="Icon" class="icon">
                                <div><strong>Microsegmentation:</strong> Isolate applications and data to limit the blast radius of a breach.</div>
                            </li>
                            <li>
                                <img src="https://www.svgrepo.com/show/489159/monitor.svg" alt="Icon" class="icon">
                                <div><strong>Continuous Monitoring:</strong> Constantly monitor user activity, device health, and network traffic for suspicious behavior.</div>
                            </li>
                            <li>
                                <img src="https://www.svgrepo.com/show/508938/data-protection.svg" alt="Icon" class="icon">
                                <div><strong>Data-Centric Protection:</strong> Protect sensitive data at rest and in transit with encryption and DLP.</div>
                            </li>
                            <li>
                                <img src="https://www.svgrepo.com/show/494752/automation-robot-1.svg" alt="Icon" class="icon">
                                <div><strong>Automation & Orchestration:</strong> Automate security tasks and workflows to improve efficiency and reduce response times.</div>
                            </li>
                        </ul>
                    </div>
                    <div class="conclusion-text">
                        <strong>Why it Works:</strong> Zero Trust eliminates implicit trust, making it significantly harder for attackers to gain access to sensitive data.
                    </div>
                </div>
                <div class="right-column">
                    <div class="diagram-card">
                        <img src="https://www.svgrepo.com/show/460224/add-user-circle.svg" alt="Verify Explicitly" class="diagram-icon">
                        <div class="diagram-text">
                            <h3>Verify Explicitly</h3>
                            <p>Authenticate and authorize based on all available data points.</p>
                        </div>
                    </div>
                    <div class="diagram-card">
                        <img src="https://www.svgrepo.com/show/417139/security-shield.svg" alt="Least Privilege Access" class="diagram-icon">
                        <div class="diagram-text">
                            <h3>Least Privilege Access</h3>
                            <p>Limit user access with just-in-time and just-enough-access (JIT/JEA).</p>
                        </div>
                    </div>
                    <div class="diagram-card">
                        <img src="https://www.svgrepo.com/show/375386/cloud-security-scanner.svg" alt="Assume Breach" class="diagram-icon">
                        <div class="diagram-text">
                            <h3>Assume Breach</h3>
                            <p>Minimize blast radius and segment access. Verify all sessions are encrypted.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>