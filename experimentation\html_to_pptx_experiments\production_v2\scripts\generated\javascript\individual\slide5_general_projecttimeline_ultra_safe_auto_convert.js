const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION
    // =======================================================================

    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0; // Higher start for dense content
    const MAX_CONTENT_Y = 4.8;

    const COLORS = {
        darkBlue: '1A237E',
        gray: '3C4043',
        lightGray: 'E0E0E0',
        workBar: '4285F4',
        idleBar: 'E8F0FE',
        milestoneBlue: '1A73E8',
        cardBg: 'F8F9FA',
        legendGray: '5F6368'
    };

    const FONT_SIZES = {
        title: 16,
        label: 10,
        subLabel: 9,
        legend: 9,
        months: 9,
        milestoneTitle: 11,
        milestoneBody: 9
    };

    // Gantt chart configuration
    const GANTT_MONTH_COUNT = 18;
    const GANTT_BAR_HEIGHT = 0.25;
    const GANTT_ROW_HEIGHT = 0.3;
    const GANTT_LABEL_WIDTH = 3.5;
    const GANTT_BAR_WIDTH = (SLIDE_WIDTH - SAFE_MARGIN * 2 - GANTT_LABEL_WIDTH) / GANTT_MONTH_COUNT;


    // =======================================================================
    // 2. HELPER FUNCTIONS
    // =======================================================================

    function addTextWithDefaults(slide, text, options) {
        slide.addText(text, {
            x: SAFE_MARGIN,
            w: SLIDE_WIDTH - SAFE_MARGIN * 2,
            ...options
        });
    }

    // =======================================================================
    // 3. SLIDE CONTENT
    // =======================================================================

    slide.background = { color: 'FFFFFF' };

    let currentY = SAFE_MARGIN;

    // Title
    addTextWithDefaults(slide, 'Project Timeline: A Phased Approach', {
        y: currentY, h: 0.5, fontSize: FONT_SIZES.title, color: COLORS.darkBlue, bold: true
    });
    currentY += 0.6;


    // Gantt Chart
    const ganttData = [
        { label: 'P1 Assessment & Replatform', bars: ['work', 'work', 'work', 'work', 'work', 'work', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle'] },
        // ... other gantt rows
    ];

    const subItemsData = [
        { label: 'Analysis & Inventory', bars: ['work', 'work', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle', 'idle'] },
        // ... other sub-items rows
    ];

    function addGanttRow(slide, label, bars, y, isSubItem) {
        addTextWithDefaults(slide, label, {
            x: SAFE_MARGIN + (isSubItem ? 0.5 : 0),
            y: y,
            w: GANTT_LABEL_WIDTH,
            h: GANTT_ROW_HEIGHT,
            fontSize: isSubItem ? FONT_SIZES.subLabel : FONT_SIZES.label,
            color: isSubItem ? COLORS.gray : COLORS.darkBlue,
            bold: !isSubItem
        });

        let barX = SAFE_MARGIN + GANTT_LABEL_WIDTH;
        bars.forEach(barType => {
            slide.addShape(pptx.shapes.RECTANGLE, {
                x: barX,
                y: y + (GANTT_ROW_HEIGHT - GANTT_BAR_HEIGHT) / 2,
                w: GANTT_BAR_WIDTH,
                h: GANTT_BAR_HEIGHT,
                fill: { color: barType === 'work' ? COLORS.workBar : COLORS.idleBar }
            });
            barX += GANTT_BAR_WIDTH;
        });
    }

    // Gantt Header
    addTextWithDefaults(slide, '█ Work · Idle ▲ Milestone ⚑ Phase Gate', {
        x: SAFE_MARGIN,
        y: currentY,
        fontSize: FONT_SIZES.legend,
        color: COLORS.legendGray
    });

    let monthX = SAFE_MARGIN + GANTT_LABEL_WIDTH;
    for (let i = 1; i <= GANTT_MONTH_COUNT; i++) {
        addTextWithDefaults(slide, i.toString().padStart(2, '0'), {
            x: monthX,
            y: currentY,
            w: GANTT_BAR_WIDTH,
            fontSize: FONT_SIZES.months,
            color: COLORS.gray,
            align: 'center'
        });
        if (i === 6 || i === 14) {
            slide.addShape(pptx.shapes.LINE, { x: monthX + GANTT_BAR_WIDTH, y: currentY, w: 0, h: 0.3, line: { color: COLORS.lightGray, width: 1 } });
        }
        monthX += GANTT_BAR_WIDTH;
    }

    currentY += 0.4;

    ganttData.forEach(row => {
        addGanttRow(slide, row.label, row.bars, currentY, false);
        currentY += GANTT_ROW_HEIGHT;
    });

    subItemsData.forEach(row => {
        addGanttRow(slide, row.label, row.bars, currentY, true);
        currentY += GANTT_ROW_HEIGHT;
    });


    // Milestones
    const milestonesData = [
        { title: 'Phase 1 Milestones', items: ['▲ Architecture sign-off (M03)', '▲ PoC exit criteria met (M03)', '⚑ Gate: Proceed to P2 (end M06)'] },
        // ... other milestone groups
    ];

    currentY += 0.5; // Add some spacing before milestones

    const milestoneWidth = (SLIDE_WIDTH - SAFE_MARGIN * 2 - 0.6) / 3; // Three columns with gaps

    let milestoneX = SAFE_MARGIN;
    milestonesData.forEach(group => {
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: milestoneX,
            y: currentY,
            w: milestoneWidth,
            h: 1.5, // Adjust height as needed
            fill: { color: COLORS.cardBg },
            line: { color: COLORS.milestoneBlue, width: 2 }
        });

        let itemY = currentY + 0.1;
        addTextWithDefaults(slide, group.title, { x: milestoneX + 0.1, y: itemY, w: milestoneWidth - 0.2, fontSize: FONT_SIZES.milestoneTitle, color: COLORS.darkBlue, bold: true });
        itemY += 0.3;

        group.items.forEach(item => {
            addTextWithDefaults(slide, item, { x: milestoneX + 0.1, y: itemY, w: milestoneWidth - 0.2, fontSize: FONT_SIZES.milestoneBody, color: COLORS.gray });
            itemY += 0.25;
        });

        milestoneX += milestoneWidth + 0.2;
    });


    return pptx.writeFile({ fileName: 'generated/presentations/slide5_general_projecttimeline.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
