const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0; // Higher placement for dense content
    const MAX_CONTENT_Y = 4.8;
    const FOOTER_Y = 5.2;

    // Color Palette (extracted from HTML)
    const COLORS = {
        background: 'FFFFFF',
        titleText: '1F2937', // text-gray-800
        bodyText: '4B5563', // text-gray-600
        bodyTextDark: '374151', // text-gray-700
        footerText: '6B7280', // text-gray-500
        footerBorder: 'E5E7EB', // border-gray-200
        chartAxis: 'D1D5DB', // border-gray-300
        insightTitle: '14532D', // text-green-800
        insightBorder: 'BBF7D0', // border-green-200
        insightCardBg: 'F0FDF4', // bg-green-50
        growthText: '16A34A', // text-green-600
        highlightText: '15803D', // text-green-700
        barColors: ['A7F3D0', '6EE7B7', '34D399', '10B981', '059669'] // green-200 to green-600
    };

    // Font Sizes (Professional & Consistent - Translated from HTML)
    const FONT_SIZES = {
        mainTitle: 16, // text-4xl -> 16
        sectionTitle: 12, // text-2xl -> 12
        subHeading: 10, // text-lg -> 10
        body: 9, // 1.1em -> 9
        small: 8, // text-sm -> 8
        growthMetric: 18 // text-3xl -> 18 (made larger for emphasis)
    };

    // Layout Calculations
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN);
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = CONTENT_WIDTH * 0.6 - 0.15; // 60% width with gap
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.3;
    const RIGHT_COL_W = CONTENT_WIDTH * 0.4 - 0.15; // 40% width with gap

    // =======================================================================
    // 2. SLIDE BACKGROUND & HEADER (Professional Layout)
    // =======================================================================

    slide.background = { color: COLORS.background };

    // Main Title (Smart Sizing: text-4xl -> 16)
    slide.addText("Global Renewable Energy Capacity Growth: A Year of Expansion", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: CONTENT_WIDTH,
        h: 0.5,
        fontSize: FONT_SIZES.mainTitle,
        color: COLORS.titleText,
        bold: true,
        valign: 'top'
    });

    // =======================================================================
    // 3. LEFT COLUMN: BAR CHART (Robust & Safe Implementation)
    // =======================================================================

    // Chart Data (Extracted from HTML)
    const chartData = [{
        name: "Renewable Capacity",
        labels: ["2019", "2020", "2021", "2022", "2023"],
        values: [2500, 2750, 3000, 3300, 3800]
    }];

    // Chart Options (Proven Safe Options)
    const chartOptions = {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        h: 3.8,
        chartColors: COLORS.barColors,
        barDir: 'col',
        valAxisTitle: 'Total Capacity (GW)',
        valAxisTitleFontSize: 9,
        valAxisTitleColor: COLORS.bodyTextDark,
        valAxisLabelFontSize: 8,
        valAxisMaxVal: 4000,
        valAxisMinVal: 2000,
        valAxisMajorUnit: 500,
        catAxisLabelFontSize: 9,
        showLegend: false,
        showValAxis: true,
        showCatAxis: true,
        border: { type: 'solid', pt: 1, color: COLORS.chartAxis }
    };

    // Add the chart to the slide
    slide.addChart(pptx.ChartType.bar, chartData, chartOptions);

    // =======================================================================
    // 4. RIGHT COLUMN: KEY INSIGHTS (Content Preservation & Smart Sizing)
    // =======================================================================

    let currentY = CONTENT_START_Y;

    // "Key Insights" Title (Smart Sizing: text-2xl -> 12)
    slide.addText("Key Insights", {
        x: RIGHT_COL_X,
        y: currentY,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: FONT_SIZES.sectionTitle,
        color: COLORS.insightTitle,
        bold: true
    });
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X,
        y: currentY + 0.3,
        w: RIGHT_COL_W,
        h: 0,
        line: { color: COLORS.insightBorder, width: 2 }
    });
    currentY += 0.5;

    // "Total Installed Capacity" Section
    slide.addText("Total Installed Capacity (GW)", {
        x: RIGHT_COL_X,
        y: currentY,
        w: RIGHT_COL_W,
        h: 0.25,
        fontSize: FONT_SIZES.subHeading,
        color: COLORS.bodyTextDark,
        bold: true
    });
    currentY += 0.3;

    const capacityData = [
        { year: "2019:", value: "2500 GW", isHighlight: false },
        { year: "2020:", value: "2750 GW", isHighlight: false },
        { year: "2021:", value: "3000 GW", isHighlight: false },
        { year: "2022:", value: "3300 GW", isHighlight: false },
        { year: "2023:", value: "3800 GW", isHighlight: true },
    ];

    capacityData.forEach(item => {
        slide.addText("•", {
            x: RIGHT_COL_X,
            y: currentY,
            w: 0.2,
            h: 0.22,
            fontSize: FONT_SIZES.body,
            color: item.isHighlight ? COLORS.highlightText : COLORS.bodyText,
            bold: true
        });
        slide.addText(item.year, {
            x: RIGHT_COL_X + 0.2,
            y: currentY,
            w: 0.8,
            h: 0.22,
            fontSize: FONT_SIZES.body,
            color: item.isHighlight ? COLORS.highlightText : COLORS.bodyText,
            bold: true
        });
        slide.addText(item.value, {
            x: RIGHT_COL_X + 1.0,
            y: currentY,
            w: RIGHT_COL_W - 1.0,
            h: 0.22,
            fontSize: FONT_SIZES.body,
            color: item.isHighlight ? COLORS.highlightText : COLORS.bodyText
        });
        currentY += 0.25; // Tight spacing for dense list
    });
    currentY += 0.2; // Extra space after list

    // "Impressive Growth" Card
    const cardY = currentY;
    const cardH = 1.1;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: RIGHT_COL_X,
        y: cardY,
        w: RIGHT_COL_W,
        h: cardH,
        fill: { color: COLORS.insightCardBg },
        rectRadius: 0.1
    });
    slide.addText("Impressive Growth", {
        x: RIGHT_COL_X + 0.15,
        y: cardY + 0.1,
        w: RIGHT_COL_W - 0.3,
        h: 0.25,
        fontSize: FONT_SIZES.subHeading,
        color: COLORS.insightTitle,
        bold: true
    });
    slide.addText("15.15%", {
        x: RIGHT_COL_X + 0.15,
        y: cardY + 0.3,
        w: RIGHT_COL_W - 0.3,
        h: 0.5,
        fontSize: FONT_SIZES.growthMetric,
        color: COLORS.growthText,
        bold: true
    });
    slide.addText("Year-over-Year Growth (2022-2023)", {
        x: RIGHT_COL_X + 0.15,
        y: cardY + 0.8,
        w: RIGHT_COL_W - 0.3,
        h: 0.2,
        fontSize: FONT_SIZES.small,
        color: COLORS.bodyText
    });
    currentY += cardH + 0.2;

    // "Main Drivers" Section
    slide.addText("Main Drivers", {
        x: RIGHT_COL_X,
        y: currentY,
        w: RIGHT_COL_W,
        h: 0.25,
        fontSize: FONT_SIZES.subHeading,
        color: COLORS.bodyTextDark,
        bold: true
    });
    currentY += 0.3;

    const drivers = [
        "Policy support & tax credits",
        "Declining technology costs",
        "Rising corporate demand"
    ];

    drivers.forEach(driver => {
        slide.addText("▶", {
            x: RIGHT_COL_X,
            y: currentY,
            w: 0.2,
            h: 0.22,
            fontSize: FONT_SIZES.body,
            color: COLORS.growthText
        });
        slide.addText(driver, {
            x: RIGHT_COL_X + 0.25,
            y: currentY,
            w: RIGHT_COL_W - 0.25,
            h: 0.22,
            fontSize: FONT_SIZES.body,
            color: COLORS.bodyText
        });
        currentY += 0.25; // Tight spacing
    });

    // =======================================================================
    // 5. FOOTER (Professional & Compact)
    // =======================================================================

    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_MARGIN,
        y: FOOTER_Y,
        w: CONTENT_WIDTH,
        h: 0,
        line: { color: COLORS.footerBorder, width: 1 }
    });
    slide.addText("Source: IRENA Renewable Capacity Statistics 2024", {
        x: SAFE_MARGIN,
        y: FOOTER_Y + 0.1,
        w: CONTENT_WIDTH,
        h: 0.2,
        fontSize: FONT_SIZES.small,
        color: COLORS.footerText,
        align: 'right'
    });

    // =======================================================================
    // 6. FINAL EXPORT
    // =======================================================================

    return pptx.writeFile({ fileName: 'generated/presentations/slide3_general_renewable.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
