<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team for Success: A Zero Trust Approach</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f2f5;
            font-family: 'Roboto', sans-serif;
        }

        .slide-container {
            width: 1280px; /* 720p resolution width */
            height: 720px;
            background-color: #0a192f;
            background-image:
                linear-gradient(rgba(10, 25, 47, 0.85), rgba(10, 25, 47, 0.85)),
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a3a6e' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            color: #e6f1ff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 60px;
            box-sizing: border-box;
        }

        .slide-content {
            flex-grow: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            width: 100%;
            padding-top: 0;
        }

        .slide-content h1 {
            font-size: 3.2em;
            color: #64ffda;
            font-weight: 700;
            margin: 0 0 15px 0;
            line-height: 1.2;
        }
        
        .title-divider {
            width: 120px;
            height: 4px;
            background-color: #64ffda;
            margin-bottom: 40px;
        }

        .content-body {
            display: flex;
            width: 100%;
            height: 100%;
            align-items: flex-start;
            justify-content: space-between;
        }

        .team-list {
            list-style: none;
            padding: 0;
            margin: 0;
            width: 55%; /* Allocate space for the list */
            display: flex;
            flex-direction: column;
            gap: 20px; /* Space between list items */
        }

        .team-list li {
            display: flex;
            align-items: flex-start;
            font-size: 1.1em;
            color: #ccd6f6;
            line-height: 1.5;
        }

        .team-list .icon {
            width: 32px;
            height: 32px;
            margin-right: 20px;
            flex-shrink: 0;
            margin-top: 5px;
            filter: invert(84%) sepia(29%) saturate(399%) hue-rotate(99deg) brightness(104%) contrast(101%); /* #64ffda color */
        }

        .team-list strong {
            font-weight: 700;
            color: #a8b2d1;
            font-size: 1.15em;
            display: block;
            margin-bottom: 4px;
        }
        
        .team-list .sub-list {
            list-style-type: disc;
            padding-left: 25px;
            margin-top: 8px;
            font-size: 0.95em;
            color: #8892b0;
        }

        .icon-container {
            width: 40%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding-left: 40px;
        }

        .main-icon {
            width: 300px;
            height: 300px;
            opacity: 0.2;
            filter: invert(84%) sepia(29%) saturate(399%) hue-rotate(99deg) brightness(104%) contrast(101%); /* #64ffda color */
        }

    </style>
</head>
<body>

    <div class="slide-container">
        <div class="slide-content">
            <h1>Assembling the Right Team for Success</h1>
            <div class="title-divider"></div>
            <div class="content-body">
                <ul class="team-list">
                    <li>
                        <img src="https://www.svgrepo.com/show/417139/security-shield.svg" alt="Security Icon" class="icon">
                        <div>
                            <strong>Dedicated Security Team</strong>
                            Requires experienced security engineers, architects, and analysts to lead the implementation.
                        </div>
                    </li>
                    <li>
                        <img src="https://www.svgrepo.com/show/489925/cloud.svg" alt="Cloud Icon" class="icon">
                        <div>
                            <strong>Cloud Expertise</strong>
                            Personnel with deep knowledge of chosen cloud platforms (Azure, AWS, or Google Cloud).
                        </div>
                    </li>
                    <li>
                        <img src="https://www.svgrepo.com/show/415567/card-id-identity.svg" alt="IAM Icon" class="icon">
                        <div>
                            <strong>IAM Specialists</strong>
                            Experts in identity and access management solutions are crucial for the core of Zero Trust.
                        </div>
                    </li>
                    <li>
                        <img src="https://www.svgrepo.com/show/417135/presentation.svg" alt="Training Icon" class="icon">
                        <div>
                            <strong>Training & Awareness Programs</strong>
                            <ul class="sub-list">
                                <li>Educate all employees on Zero Trust principles and secure work practices.</li>
                                <li>Provide in-depth technical training for IT staff on new technologies.</li>
                            </ul>
                        </div>
                    </li>
                    <li>
                        <img src="https://www.svgrepo.com/show/331729/management-service.svg" alt="Change Management Icon" class="icon">
                        <div>
                            <strong>Change Management</strong>
                            A dedicated team to manage the cultural and operational transition, ensuring user adoption.
                        </div>
                    </li>
                </ul>
                <div class="icon-container">
                    <img src="https://www.svgrepo.com/show/475311/team.svg" alt="Team Icon" class="main-icon">
                </div>
            </div>
        </div>
    </div>

</body>
</html>