from tavily import TavilyClient
import os
from dotenv import load_dotenv
from loguru import logger
from typing import Dict, Any

# Load environment variables
load_dotenv('local.env')

TAVILY_API_KEY = os.getenv("TAVILY_API_KEY") or os.getenv("TAVILY_API_KEY1")
if not TAVILY_API_KEY:
    logger.warning("⚠️ No Tavily API key found in environment variables")
    client = None
else:
    client = TavilyClient(api_key=TAVILY_API_KEY)
    logger.info(f"✅ Tavily client initialized with API key: {TAVILY_API_KEY[:10]}...")

# Global registry to track Tavily image URLs and their metadata
tavily_image_registry: Dict[str, Dict[str, Any]] = {}

def image_tavily(search_term: str) -> dict:
    """
    ### ONLY USE WHEN SPECIFY

    tool_name: image_tavily

    You use image_tavily to search for images/logos/and illustration given a search term image_tavily will return image urls with the image corresponding description.

    Args:
        search_term: The keyword or phrase for the image search.

    Returns:
        A list of dictionaries, each representing an image result with its
        URL and description. Returns an empty list if no images are found.
    """
    logger.info(f"🔍 Tavily API call - Searching for images: '{search_term}'")

    if not client:
        logger.error("❌ Tavily client not initialized - no API key available")
        return []

    try:
        response = client.search(
            query=search_term,
            include_images=True,
            include_image_descriptions=True
        )

        images = response.get("images", [])
        logger.info(f"✅ Tavily API response - Found {len(images)} images for '{search_term}'")

        # Store URLs in registry and log results
        for i, img in enumerate(images):
            url = img.get('url', '')
            description = img.get('description', 'No description')

            if url:
                # Store in global registry for later identification
                tavily_image_registry[url] = {
                    'search_term': search_term,
                    'description': description,
                    'timestamp': 'current_session'
                }

            # Log first few results for debugging
            if i < 3:
                desc_preview = (description or "No description")[:100]
                logger.info(f"   Image {i+1}: {url} - {desc_preview}...")

        return images

    except Exception as e:
        logger.error(f"❌ Tavily API error for search '{search_term}': {str(e)}")
        return []

def log_tavily_usage_in_html(html_content: str, slide_number: int = None) -> None:
    """
    Analyze HTML content to find and log which Tavily image URLs are actually being used
    Uses the global registry to accurately identify Tavily images
    """
    import re

    # Extract all image URLs from HTML (src attributes)
    img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
    img_matches = re.findall(img_pattern, html_content, re.IGNORECASE)

    # Extract background-image URLs from CSS
    bg_pattern = r'background-image:\s*url\(["\']?([^"\')\s]+)["\']?\)'
    bg_matches = re.findall(bg_pattern, html_content, re.IGNORECASE)

    all_urls = img_matches + bg_matches

    if all_urls:
        slide_info = f"slide {slide_number}" if slide_number else "HTML content"
        logger.info(f"🖼️ Images used in {slide_info}:")

        tavily_count = 0
        other_count = 0

        for i, url in enumerate(all_urls, 1):
            # Check if URL is in our Tavily registry (exact match)
            if url in tavily_image_registry:
                tavily_data = tavily_image_registry[url]
                search_term = tavily_data.get('search_term', 'unknown')
                description = tavily_data.get('description', 'No description')

                logger.info(f"   🔍 [CONFIRMED Tavily] Image {i}: {url}")
                logger.info(f"      └─ Search: '{search_term}' | Desc: {description[:80]}...")
                tavily_count += 1
            else:
                # Check if it's an HTTP/HTTPS URL (likely external image)
                if url.startswith(('http://', 'https://')):
                    logger.info(f"   🌐 [External URL] Image {i}: {url}")
                else:
                    logger.info(f"   📷 [Local/Other] Image {i}: {url}")
                other_count += 1

        # Summary
        logger.info(f"   📊 Summary: {tavily_count} Tavily images, {other_count} other sources")

    else:
        slide_info = f"slide {slide_number}" if slide_number else "HTML content"
        logger.info(f"📭 No images found in {slide_info}")

def get_tavily_registry_stats() -> Dict[str, Any]:
    """
    Get statistics about Tavily image usage
    """
    return {
        'total_tavily_images': len(tavily_image_registry),
        'search_terms': list(set(data.get('search_term', '') for data in tavily_image_registry.values())),
        'sample_urls': list(tavily_image_registry.keys())[:5]
    }

# Example usage:
if __name__ == "__main__":
    images = image_tavily("find logo of Honda")
    for img in images:
        print(f"URL: {img.get('url')}")
        print(f"Description: {img.get('description')}")
