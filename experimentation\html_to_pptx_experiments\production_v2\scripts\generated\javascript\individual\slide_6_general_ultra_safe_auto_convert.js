const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE POSITIONING & COLOR CONSTANTS
    const SAFE_MARGIN = 0.3;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer values
    const MAX_CONTENT_Y = 4.8;

    // Colors from CSS analysis
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_TEXT_TERTIARY = '8892b0';

    // Layout constants for two columns
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.5;
    const RIGHT_COL_X = 5.2;
    const RIGHT_COL_W = 3.3; // Ends at 8.5 (SAFE)

    // Font size constants
    const FONT_SIZE_TITLE = 16;
    const FONT_SIZE_HEADING = 10;
    const FONT_SIZE_BODY = 9;
    const FONT_SIZE_SUB_BODY = 8;

    // Vertical positioning
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const DIVIDER_Y = TITLE_Y + TITLE_H;
    const CONTENT_START_Y = DIVIDER_Y + 0.4; // Start content below divider

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Add Title
    slide.addText("Assembling the Right Team for Success", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: CONTENT_WIDTH,
        h: TITLE_H,
        fontSize: FONT_SIZE_TITLE,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        align: 'left'
    });

    // Add Title Divider (using a shape)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: DIVIDER_Y,
        w: 1.2, // 120px equivalent
        h: 0.04, // 4px equivalent
        fill: { color: COLOR_PRIMARY_ACCENT }
    });

    // --- Left Column: Team List ---
    const teamListData = [
        {
            icon: 'shield',
            title: "Dedicated Security Team",
            text: "Requires experienced security engineers, architects, and analysts to lead the implementation."
        },
        {
            icon: 'cloud',
            title: "Cloud Expertise",
            text: "Personnel with deep knowledge of chosen cloud platforms (Azure, AWS, or Google Cloud)."
        },
        {
            icon: 'id',
            title: "IAM Specialists",
            text: "Experts in identity and access management solutions are crucial for the core of Zero Trust."
        },
        {
            icon: 'presentation',
            title: "Training & Awareness Programs",
            subItems: [
                "Educate all employees on Zero Trust principles and secure work practices.",
                "Provide in-depth technical training for IT staff on new technologies."
            ]
        },
        {
            icon: 'management',
            title: "Change Management",
            text: "A dedicated team to manage the cultural and operational transition, ensuring user adoption."
        }
    ];

    let currentY = CONTENT_START_Y;
    const ICON_SIZE = 0.25;
    const ICON_TEXT_GAP = 0.1;
    const ITEM_SPACING = 0.2; // Space between major list items

    // Function to add an icon shape safely
    function addIcon(iconType, x, y) {
        const iconOptions = {
            x: x,
            y: y + 0.05, // Align icon slightly lower
            w: ICON_SIZE,
            h: ICON_SIZE,
            fill: { color: COLOR_PRIMARY_ACCENT }
        };
        // Use valid PptxGenJS shapes to represent icons
        switch (iconType) {
            case 'shield':
                // Using a rounded rectangle as a stand-in for a shield
                slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, { ...iconOptions, rectRadius: 0.1 });
                break;
            case 'cloud':
                // Using an oval as a stand-in for a cloud
                slide.addShape(pptx.shapes.OVAL, iconOptions);
                break;
            case 'id':
            case 'presentation':
            case 'management':
            default:
                // Default to a simple rectangle for other icons
                slide.addShape(pptx.shapes.RECTANGLE, iconOptions);
                break;
        }
    }

    teamListData.forEach(item => {
        if (currentY > MAX_CONTENT_Y - 0.5) return; // Stop if approaching vertical limit

        const textX = LEFT_COL_X + ICON_SIZE + ICON_TEXT_GAP;
        const textW = LEFT_COL_W - ICON_SIZE - ICON_TEXT_GAP;

        // Add Icon
        addIcon(item.icon, LEFT_COL_X, currentY);

        // Add Title
        slide.addText(item.title, {
            x: textX,
            y: currentY,
            w: textW,
            h: 0.2,
            fontSize: FONT_SIZE_HEADING,
            color: COLOR_TEXT_SECONDARY,
            bold: true
        });
        currentY += 0.2;

        // Add Body Text or Sub-list
        if (item.text) {
            slide.addText(item.text, {
                x: textX,
                y: currentY,
                w: textW,
                h: 0.3,
                fontSize: FONT_SIZE_BODY,
                color: COLOR_TEXT_PRIMARY
            });
            currentY += 0.3 + ITEM_SPACING;
        } else if (item.subItems) {
            item.subItems.forEach(subItem => {
                if (currentY > MAX_CONTENT_Y - 0.3) return;
                // Add bullet point and text for sub-items
                slide.addText(`• ${subItem}`, {
                    x: textX + 0.2, // Indent sub-items
                    y: currentY,
                    w: textW - 0.2,
                    h: 0.3,
                    fontSize: FONT_SIZE_SUB_BODY,
                    color: COLOR_TEXT_TERTIARY
                });
                currentY += 0.2;
            });
            currentY += ITEM_SPACING;
        }
    });

    // --- Right Column: Main Icon ---
    // Recreate the "team" icon using basic, valid shapes for an abstract representation.
    const iconCenterX = RIGHT_COL_X + RIGHT_COL_W / 2;
    const iconCenterY = CONTENT_START_Y + (MAX_CONTENT_Y - CONTENT_START_Y) / 2;
    const iconFill = { color: COLOR_PRIMARY_ACCENT, transparency: 80 }; // opacity: 0.2

    // Central "body"
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - 0.5, y: iconCenterY - 0.2, w: 1.0, h: 1.0, fill: iconFill
    });
    // "Head"
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - 0.25, y: iconCenterY - 0.6, w: 0.5, h: 0.5, fill: iconFill
    });
    // Two side "members"
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX - 1.2, y: iconCenterY, w: 0.8, h: 0.8, fill: iconFill
    });
    slide.addShape(pptx.shapes.OVAL, {
        x: iconCenterX + 0.4, y: iconCenterY, w: 0.8, h: 0.8, fill: iconFill
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_6_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
