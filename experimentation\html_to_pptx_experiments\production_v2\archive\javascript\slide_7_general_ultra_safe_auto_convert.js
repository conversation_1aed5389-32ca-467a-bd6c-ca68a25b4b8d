const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE POSITIONING & STYLE CONSTANTS (GENERIC, NOT SLIDE-SPECIFIC)
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer values

    // Colors from HTML
    const BG_COLOR = '0a192f';
    const PRIMARY_COLOR = '64ffda';
    const TEXT_COLOR_LIGHT = 'ccd6f6';
    const TEXT_COLOR_MEDIUM = 'a8b2d1';
    const TEXT_COLOR_DARK = '0a192f';
    const BORDER_COLOR = '1a3a6e'; // Derived from gray-700 and background pattern

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Title
    slide.addText("A Strategic Roadmap to Zero Trust", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16, // Ultra-safe max title size
        color: PRIMARY_COLOR,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.5, // 120px is roughly 1.5 inches
        h: 0,
        line: { color: PRIMARY_COLOR, width: 2 }
    });

    // Gantt Chart Layout Constants
    const GANTT_START_Y = 1.2;
    const PHASE_COL_X = SAFE_MARGIN;
    const PHASE_COL_W = 2.2;
    const TIMELINE_COL_X = PHASE_COL_X + PHASE_COL_W + 0.1; // 2.6
    const TIMELINE_COL_W = 6.0; // Ends at 8.6, slightly over, will use 5.8 for safety
    const SAFE_TIMELINE_W = 5.8; // 2.6 + 5.8 = 8.4 (SAFE)
    const QUARTER_W = SAFE_TIMELINE_W / 4; // 1.45
    const ROW_HEIGHT = 0.7;

    // Timeline Header
    const timelineHeaders = ["3 Months", "6 Months", "9 Months", "12 Months"];
    timelineHeaders.forEach((header, index) => {
        slide.addText(header, {
            x: TIMELINE_COL_X + (index * QUARTER_W),
            y: GANTT_START_Y,
            w: QUARTER_W,
            h: 0.2,
            align: 'center',
            fontSize: 9, // Ultra-safe content font size
            color: TEXT_COLOR_MEDIUM
        });
    });

    // Timeline Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: TIMELINE_COL_X,
        y: GANTT_START_Y + 0.25,
        w: SAFE_TIMELINE_W,
        h: 0,
        line: { color: BORDER_COLOR, width: 1 }
    });

    // Gantt Chart Data
    const phases = [
        {
            title: "Phase 1: Assessment & Planning",
            desc: "Conduct security assessment, define policies, select technologies.",
            duration: 1, // in quarters
            opacity: 'CC' // 80%
        },
        {
            title: "Phase 2: Identity & Access",
            desc: "Implement passwordless auth, conditional access, behavioral biometrics.",
            duration: 2, // in quarters
            opacity: 'B3' // 70%
        },
        {
            title: "Phase 3: Device Security",
            desc: "Deploy endpoint management, enforce device posture, implement RBI.",
            duration: 3, // in quarters
            opacity: '99' // 60%
        },
        {
            title: "Phase 4: Network Microsegmentation",
            desc: "Implement SDP, configure security groups, deploy AI anomaly detection.",
            duration: 4, // in quarters
            opacity: '80' // 50%
        },
        {
            title: "Phase 5: Data Security & Monitoring",
            desc: "Implement DLP, encrypt data, and continuously monitor environment.",
            duration: 4, // in quarters
            opacity: '66', // 40%
            ongoing: true
        }
    ];

    let currentY = GANTT_START_Y + 0.4;

    // Function to add elements safely and prevent overflow
    function addElementSafely(yPosition, elementHeight) {
        return (yPosition + elementHeight) <= MAX_CONTENT_Y;
    }

    phases.forEach(phase => {
        if (!addElementSafely(currentY, ROW_HEIGHT)) {
            console.warn(`Skipping phase "${phase.title}" to prevent vertical overflow.`);
            return;
        }

        // Phase Title and Description
        slide.addText([
            { text: phase.title, options: { fontSize: 10, bold: true, color: TEXT_COLOR_MEDIUM, breakLine: true } },
            { text: phase.desc, options: { fontSize: 8, color: TEXT_COLOR_LIGHT } }
        ], {
            x: PHASE_COL_X,
            y: currentY,
            w: PHASE_COL_W,
            h: ROW_HEIGHT - 0.1
        });

        // Gantt Bar
        const barWidth = phase.duration * QUARTER_W;
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: TIMELINE_COL_X,
            y: currentY + 0.1,
            w: barWidth,
            h: 0.4,
            fill: { color: PRIMARY_COLOR, transparency: 100 - parseInt(phase.opacity, 16) / 2.55 },
            rectRadius: 0.1
        });

        // Bar Text
        let barText = `${phase.duration * 3} Months`;
        if (phase.ongoing) {
            barText = "Ongoing";
        }
        slide.addText(barText, {
            x: TIMELINE_COL_X,
            y: currentY + 0.1,
            w: barWidth,
            h: 0.4,
            align: phase.ongoing ? 'left' : 'center',
            inset: phase.ongoing ? 0.15 : 0,
            fontSize: 9,
            bold: true,
            color: TEXT_COLOR_DARK
        });

        currentY += ROW_HEIGHT;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_7_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
