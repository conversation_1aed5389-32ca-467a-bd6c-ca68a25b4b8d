{"cells": [{"cell_type": "markdown", "id": "cc68eb36", "metadata": {}, "source": ["## Planner prototyping\n", "Notebook for prototyping the powerpoint planner"]}, {"cell_type": "code", "execution_count": 2, "id": "201e3156", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'dotenv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01msys\u001b[39;00m\n\u001b[0;32m      2\u001b[0m sys\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mappend(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m../\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;66;03m# go to parent dir\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mllm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mllmwrapper\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m LLM\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m logger\n", "File \u001b[1;32mc:\\Users\\<USER>\\OneDrive\\Desktop\\VS Code\\pptx-planner\\experimentation\\..\\llm\\llmwrapper.py:2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mos\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mdotenv\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m load_dotenv\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mllm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Gemini_LLM\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mllm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m OpenAI_LLM\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'dotenv'"]}], "source": ["import sys\n", "sys.path.append(\"../\") # go to parent dir\n", "from llm.llmwrapper import LLM\n", "from loguru import logger"]}, {"cell_type": "code", "execution_count": 3, "id": "0db91b5c", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'dotenv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01msys\u001b[39;00m\n\u001b[0;32m      2\u001b[0m sys\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mappend(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m../\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;66;03m# go to parent dir\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mllm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mllmwrapper\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m LLM\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m logger\n", "File \u001b[1;32mc:\\Users\\<USER>\\OneDrive\\Desktop\\VS Code\\pptx-planner\\experimentation\\..\\llm\\llmwrapper.py:2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mos\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mdotenv\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m load_dotenv\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mllm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Gemini_LLM\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mllm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m OpenAI_LLM\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'dotenv'"]}], "source": ["import sys\n", "sys.path.append(\"../\") # go to parent dir\n", "from llm.llmwrapper import LLM\n", "from loguru import logger"]}, {"cell_type": "code", "execution_count": 6, "id": "156ed197", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'LLM' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m llm \u001b[38;5;241m=\u001b[39m \u001b[43mLLM\u001b[49m(provider\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgemini\u001b[39m\u001b[38;5;124m\"\u001b[39m, model\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgemini-1.5-pro\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'LLM' is not defined"]}], "source": ["llm = LLM(provider=\"gemini\", model=\"gemini-1.5-pro\")"]}, {"cell_type": "markdown", "id": "b5161442", "metadata": {}, "source": ["### Step 1: brainstorm"]}, {"cell_type": "code", "execution_count": 7, "id": "8fd91a5b", "metadata": {}, "outputs": [], "source": ["query = \"Make a presentation about building a system which takes an internal company database of contract documents, and based on a user query, helps find the most relevant contract document\""]}, {"cell_type": "code", "execution_count": 18, "id": "1a4a0534", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"Make a presentation about building a system which takes an internal company database of contract documents, and based on a user query, helps find the most relevant contract document\"\n", "\n", "Before starting work on the request, you need to brainstorm.\n", "From a technical perspective, how could something like this be done? \n", "Please use the following pointers to guide your thought process:\n", "- What is the most cutting-edge way to do this?\n", "- How can this be done using cloud services?\n", "\n"]}], "source": ["brainstorm_prompt = \\\n", "f\"\"\"\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"{query}\"\n", "\n", "Before starting work on the request, you need to brainstorm.\n", "From a technical perspective, how could something like this be done? \n", "Please use the following pointers to guide your thought process:\n", "- What is the most cutting-edge way to do this?\n", "- How can this be done using cloud services?\n", "\"\"\"\n", "print(brainstorm_prompt)"]}, {"cell_type": "code", "execution_count": 19, "id": "4a59112e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["## Brainstorming: Contract Document Search System\n", "\n", "**Goal:** Build a system that takes an internal company database of contract documents and, based on a user query, helps find the most relevant contract document.\n", "\n", "**Cutting-Edge Approach:**\n", "\n", "Leveraging a combination of **vector databases** and **large language models (LLMs)** offers the most cutting-edge solution. This approach allows for semantic search, going beyond keyword matching to understand the meaning and context of both the query and the contract documents.\n", "\n", "**Technical Breakdown:**\n", "\n", "1. **Document Preprocessing:**\n", "    * **Optical Character Recognition (OCR):** If contracts are scanned images, OCR is crucial for extracting text.\n", "    * **Cleaning and Normalization:** Remove noise, standardize formatting, and handle special characters.\n", "    * **Chunking:** Divide contracts into smaller, manageable chunks (paragraphs or sections) for efficient processing.\n", "\n", "2. **Embedding Generation:**\n", "    * **LLM-based Embeddings:** Utilize a powerful LLM (e.g., OpenAI's embeddings API, Sentence Transformers) to generate vector representations (embeddings) of each chunk. These embeddings capture the semantic meaning of the text.\n", "\n", "3. **Vector Database:**\n", "    * **Store Embeddings:** Use a vector database (e.g., Pinecone, Weaviate, FAISS) to store the generated embeddings. These databases are optimized for similarity search.\n", "\n", "4. **Query Processing:**\n", "    * **Embed the Query:**  When a user submits a query, generate its embedding using the same LLM used for document embeddings.\n", "    * **Similarity Search:** Perform a similarity search in the vector database using the query embedding. This retrieves the most semantically similar document chunks.\n", "    * **Ranking and Retrieval:** Rank the retrieved chunks based on similarity scores and potentially other factors like contract date, type, etc.  Return the most relevant contract documents containing these chunks.\n", "\n", "5. **User Interface:**\n", "    * **Intuitive Search Interface:** Develop a user-friendly interface for submitting queries and displaying results.\n", "    * **Contextual Snippets:** Show relevant snippets from the retrieved contracts alongside the document title and other metadata.\n", "    * **Filtering and Faceting:** Allow users to filter results based on metadata like contract date, type, parties involved, etc.\n", "\n", "**Cloud Services Integration:**\n", "\n", "This system can be effectively built using cloud services:\n", "\n", "* **Document Storage:** AWS S3, Azure Blob Storage, or Google Cloud Storage for storing original contract documents.\n", "* **Compute:** AWS Lambda, Azure Functions, or Google Cloud Functions for serverless processing of documents and queries.\n", "* **Vector Database:** Managed vector database services like Pinecone or Weaviate, or self-hosted solutions on cloud VMs.\n", "* **LLM API:** Integrate with OpenAI's embeddings API or other cloud-based LLM services.\n", "* **Search Interface:** Build a web application using cloud-based frameworks and deploy it on platforms like AWS Elastic Beanstalk, Azure App Service, or Google App Engine.\n", "\n", "\n", "**Additional Considerations:**\n", "\n", "* **Security:** Implement appropriate security measures to protect sensitive contract data.\n", "* **Scalability:** Design the system to handle a growing number of contracts and user queries.\n", "* **Maintainability:** Use modular design and version control for easy maintenance and updates.\n", "* **Cost Optimization:** Choose cost-effective cloud services and optimize resource utilization.\n", "* **Legal and Compliance:** Ensure the system complies with relevant legal and regulatory requirements.\n", "\n", "\n", "This approach provides a robust and scalable solution for searching contract documents based on semantic understanding, leveraging the power of cutting-edge technologies and cloud services.\n", "\n"]}], "source": ["brainstorm_response = llm.call(query=brainstorm_prompt)\n", "print(brainstorm_response['text'])"]}, {"cell_type": "markdown", "id": "668afbcd", "metadata": {}, "source": ["### Step 2: Plan structure of presentation"]}, {"cell_type": "code", "execution_count": 24, "id": "c009023d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"Make a presentation about building a system which takes an internal company database of contract documents, and based on a user query, helps find the most relevant contract document\"\n", "\n", "After consulting with a senior software engineer, he has provided you the following approach to build such a system:\n", "\"## Brainstorming: Contract Document Search System\n", "\n", "**Goal:** Build a system that takes an internal company database of contract documents and, based on a user query, helps find the most relevant contract document.\n", "\n", "**Cutting-Edge Approach:**\n", "\n", "Leveraging a combination of **vector databases** and **large language models (LLMs)** offers the most cutting-edge solution. This approach allows for semantic search, going beyond keyword matching to understand the meaning and context of both the query and the contract documents.\n", "\n", "**Technical Breakdown:**\n", "\n", "1. **Document Preprocessing:**\n", "    * **Optical Character Recognition (OCR):** If contracts are scanned images, OCR is crucial for extracting text.\n", "    * **Cleaning and Normalization:** Remove noise, standardize formatting, and handle special characters.\n", "    * **Chunking:** Divide contracts into smaller, manageable chunks (paragraphs or sections) for efficient processing.\n", "\n", "2. **Embedding Generation:**\n", "    * **LLM-based Embeddings:** Utilize a powerful LLM (e.g., OpenAI's embeddings API, Sentence Transformers) to generate vector representations (embeddings) of each chunk. These embeddings capture the semantic meaning of the text.\n", "\n", "3. **Vector Database:**\n", "    * **Store Embeddings:** Use a vector database (e.g., Pinecone, Weaviate, FAISS) to store the generated embeddings. These databases are optimized for similarity search.\n", "\n", "4. **Query Processing:**\n", "    * **Embed the Query:**  When a user submits a query, generate its embedding using the same LLM used for document embeddings.\n", "    * **Similarity Search:** Perform a similarity search in the vector database using the query embedding. This retrieves the most semantically similar document chunks.\n", "    * **Ranking and Retrieval:** Rank the retrieved chunks based on similarity scores and potentially other factors like contract date, type, etc.  Return the most relevant contract documents containing these chunks.\n", "\n", "5. **User Interface:**\n", "    * **Intuitive Search Interface:** Develop a user-friendly interface for submitting queries and displaying results.\n", "    * **Contextual Snippets:** Show relevant snippets from the retrieved contracts alongside the document title and other metadata.\n", "    * **Filtering and Faceting:** Allow users to filter results based on metadata like contract date, type, parties involved, etc.\n", "\n", "**Cloud Services Integration:**\n", "\n", "This system can be effectively built using cloud services:\n", "\n", "* **Document Storage:** AWS S3, Azure Blob Storage, or Google Cloud Storage for storing original contract documents.\n", "* **Compute:** AWS Lambda, Azure Functions, or Google Cloud Functions for serverless processing of documents and queries.\n", "* **Vector Database:** Managed vector database services like Pinecone or Weaviate, or self-hosted solutions on cloud VMs.\n", "* **LLM API:** Integrate with OpenAI's embeddings API or other cloud-based LLM services.\n", "* **Search Interface:** Build a web application using cloud-based frameworks and deploy it on platforms like AWS Elastic Beanstalk, Azure App Service, or Google App Engine.\n", "\n", "\n", "**Additional Considerations:**\n", "\n", "* **Security:** Implement appropriate security measures to protect sensitive contract data.\n", "* **Scalability:** Design the system to handle a growing number of contracts and user queries.\n", "* **Maintainability:** Use modular design and version control for easy maintenance and updates.\n", "* **Cost Optimization:** Choose cost-effective cloud services and optimize resource utilization.\n", "* **Legal and Compliance:** Ensure the system complies with relevant legal and regulatory requirements.\n", "\n", "\n", "This approach provides a robust and scalable solution for searching contract documents based on semantic understanding, leveraging the power of cutting-edge technologies and cloud services.\n", "\"\n", "\n", "It is now time for you to create the proposal slides.\n", "Before deciding on what content should go on the slides, \n", "create a well-thought out plan of the structure that this presentation should follow.\n", "\n", "Some of the sections you should include are:\n", "- Title slide\n", "- Executive summary slide\n", "- The background of the problem\n", "- Your proposed solution and why it will work / benefits of the solution\n", "- The infrastructure and tech stack\n", "- The required human resources\n", "- The timeline\n", "- The cost involved in this project\n", "- A proper conclusion slide\n", "\n", "Depending on the situation, be creative and add in any other sections that you think might add value.\n", "If this proposal is successful, you will get a big raise!\n", "\n"]}], "source": ["skeleton_prompt = \\\n", "f\"\"\"\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"{query}\"\n", "\n", "After consulting with a senior software engineer, he has provided you the following approach to build such a system:\n", "\"{brainstorm_response['text']}\"\n", "\n", "It is now time for you to create the proposal slides.\n", "Before deciding on what content should go on the slides, \n", "create a well-thought out plan of the structure that this presentation should follow.\n", "\n", "Some of the sections you should include are:\n", "- Title slide\n", "- Executive summary slide\n", "- The background of the problem\n", "- Your proposed solution and why it will work / benefits of the solution\n", "- The infrastructure and tech stack\n", "- The required human resources\n", "- The timeline\n", "- The cost involved in this project\n", "- A proper conclusion slide\n", "\n", "Depending on the situation, be creative and add in any other sections that you think might add value.\n", "If this proposal is successful, you will get a big raise!\n", "\"\"\"\n", "print(skeleton_prompt)"]}, {"cell_type": "code", "execution_count": 25, "id": "302a5d24", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["## Presentation Structure Plan: Contract Document Search System\n", "\n", "This plan outlines the structure of a presentation proposing a cutting-edge contract document search system. The goal is to secure approval and funding for the project by clearly articulating the problem, the proposed solution, its benefits, and the associated resources and costs.\n", "\n", "**I. Title Slide (1 slide)**\n", "\n", "* **Title:**  Intelligent Contract Search: Empowering Your Business with Cutting-Edge Technology\n", "* **Subtitle:**  Unlocking the Value of Your Contract Data\n", "* **Your Name/Title**\n", "* **Company Logo/Date**\n", "\n", "**II. Executive Summary (1 slide)**\n", "\n", "* **Problem:** Briefly state the current challenges with contract document retrieval. (Inefficient, keyword-based, time-consuming)\n", "* **Solution:** Concisely describe the proposed AI-powered semantic search solution.\n", "* **Key Benefits:** Highlight 2-3 major advantages (e.g., improved efficiency, better insights, reduced risk).\n", "* **Expected ROI:** Briefly mention potential return on investment (e.g., time saved, improved compliance).\n", "\n", "**III. The Problem: Current State of Contract Search (2 slides)**\n", "\n", "* **Slide 1: Pain Points:**  Describe the current inefficiencies and limitations of manual or keyword-based search.  Use visuals (e.g., flowchart depicting a cumbersome process, graph showing time wasted).\n", "* **Slide 2: Business Impact:** Quantify the negative impact of these inefficiencies. (e.g., time spent searching, missed deadlines, potential legal risks).  Include concrete examples or case studies if possible.\n", "\n", "**IV. The Solution: AI-Powered Semantic Search (3 slides)**\n", "\n", "* **Slide 1: Overview:** Introduce the concept of semantic search and how it differs from traditional keyword search. Explain how LLMs and vector databases enable this. Use simple, non-technical language.\n", "* **Slide 2: System Architecture:** Present a clear visual diagram of the system architecture (document preprocessing, embedding generation, vector database, query processing, user interface).\n", "* **Slide 3: Benefits of Semantic Search:**  Elaborate on the key advantages: improved accuracy, faster retrieval, better understanding of contract content, enhanced compliance, and reduced risk.\n", "\n", "**V. Technology Stack and Infrastructure (2 slides)**\n", "\n", "* **Slide 1: Core Technologies:**  Present the chosen technologies (OCR engine, LLM provider, vector database, cloud platform) and justify their selection based on performance, scalability, security, and cost-effectiveness.\n", "* **Slide 2: Infrastructure Diagram:**  Visualize the cloud infrastructure, highlighting key components (document storage, compute services, database, API integrations) and their interactions.\n", "\n", "**VI. Human Resources and Expertise (1 slide)**\n", "\n", "* **Team Composition:** Outline the required roles and expertise (e.g., data scientists, software engineers, cloud architects, legal experts).  Emphasize the existing internal expertise and any necessary external resources.\n", "\n", "**VII. Project Timeline (1 slide)**\n", "\n", "* **Phased Approach:** Present a clear timeline with key milestones (e.g., data preprocessing, system development, testing, deployment, training).  Use a Gantt chart or similar visual.\n", "\n", "**VIII. Cost and Budget (1 slide)**\n", "\n", "* **Cost Breakdown:**  Provide a detailed breakdown of estimated costs (e.g., cloud services, software licenses, personnel, consulting).\n", "* **ROI Projection:**  Reiterate the potential return on investment, emphasizing long-term cost savings and efficiency gains.\n", "\n", "**IX.  Demo (1-2 slides + Live Demo)**\n", "\n", "* **Pre-recorded Demo:** Showcase a short, pre-recorded demo of the system in action, highlighting key features and user experience.\n", "* **Live Demo (Optional):** If feasible, include a brief live demo to further impress the audience.\n", "\n", "**X. Conclusion and Call to Action (1 slide)**\n", "\n", "* **Recap Key Benefits:** Briefly summarize the key advantages of the proposed solution.\n", "* **Next Steps:** Clearly state the desired next steps (e.g., project approval, budget allocation).\n", "* **Reinforce Value Proposition:** End with a strong statement emphasizing the value and transformative potential of the intelligent contract search system.\n", "\n", "\n", "**XI. Q&A (Open)**\n", "\n", "* Be prepared to answer questions about the technical details, implementation plan, budget, and potential challenges.\n", "\n", "\n", "This structured approach will ensure a clear, concise, and compelling presentation that effectively communicates the value proposition of the intelligent contract search system and increases the likelihood of project approval.  The inclusion of visuals, quantifiable benefits, and a clear call to action will further strengthen the proposal.\n", "\n"]}], "source": ["skeleton_response = llm.call(query=skeleton_prompt)\n", "print(skeleton_response['text'])"]}, {"cell_type": "markdown", "id": "80b8e497", "metadata": {}, "source": ["### Step 3: Create slide content"]}, {"cell_type": "code", "execution_count": 26, "id": "1eedc5d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"Make a presentation about building a system which takes an internal company database of contract documents, and based on a user query, helps find the most relevant contract document\"\n", "\n", "After consulting with a senior software engineer, he has provided you the following approach to build such a system:\n", "\"## Brainstorming: Contract Document Search System\n", "\n", "**Goal:** Build a system that takes an internal company database of contract documents and, based on a user query, helps find the most relevant contract document.\n", "\n", "**Cutting-Edge Approach:**\n", "\n", "Leveraging a combination of **vector databases** and **large language models (LLMs)** offers the most cutting-edge solution. This approach allows for semantic search, going beyond keyword matching to understand the meaning and context of both the query and the contract documents.\n", "\n", "**Technical Breakdown:**\n", "\n", "1. **Document Preprocessing:**\n", "    * **Optical Character Recognition (OCR):** If contracts are scanned images, OCR is crucial for extracting text.\n", "    * **Cleaning and Normalization:** Remove noise, standardize formatting, and handle special characters.\n", "    * **Chunking:** Divide contracts into smaller, manageable chunks (paragraphs or sections) for efficient processing.\n", "\n", "2. **Embedding Generation:**\n", "    * **LLM-based Embeddings:** Utilize a powerful LLM (e.g., OpenAI's embeddings API, Sentence Transformers) to generate vector representations (embeddings) of each chunk. These embeddings capture the semantic meaning of the text.\n", "\n", "3. **Vector Database:**\n", "    * **Store Embeddings:** Use a vector database (e.g., Pinecone, Weaviate, FAISS) to store the generated embeddings. These databases are optimized for similarity search.\n", "\n", "4. **Query Processing:**\n", "    * **Embed the Query:**  When a user submits a query, generate its embedding using the same LLM used for document embeddings.\n", "    * **Similarity Search:** Perform a similarity search in the vector database using the query embedding. This retrieves the most semantically similar document chunks.\n", "    * **Ranking and Retrieval:** Rank the retrieved chunks based on similarity scores and potentially other factors like contract date, type, etc.  Return the most relevant contract documents containing these chunks.\n", "\n", "5. **User Interface:**\n", "    * **Intuitive Search Interface:** Develop a user-friendly interface for submitting queries and displaying results.\n", "    * **Contextual Snippets:** Show relevant snippets from the retrieved contracts alongside the document title and other metadata.\n", "    * **Filtering and Faceting:** Allow users to filter results based on metadata like contract date, type, parties involved, etc.\n", "\n", "**Cloud Services Integration:**\n", "\n", "This system can be effectively built using cloud services:\n", "\n", "* **Document Storage:** AWS S3, Azure Blob Storage, or Google Cloud Storage for storing original contract documents.\n", "* **Compute:** AWS Lambda, Azure Functions, or Google Cloud Functions for serverless processing of documents and queries.\n", "* **Vector Database:** Managed vector database services like Pinecone or Weaviate, or self-hosted solutions on cloud VMs.\n", "* **LLM API:** Integrate with OpenAI's embeddings API or other cloud-based LLM services.\n", "* **Search Interface:** Build a web application using cloud-based frameworks and deploy it on platforms like AWS Elastic Beanstalk, Azure App Service, or Google App Engine.\n", "\n", "\n", "**Additional Considerations:**\n", "\n", "* **Security:** Implement appropriate security measures to protect sensitive contract data.\n", "* **Scalability:** Design the system to handle a growing number of contracts and user queries.\n", "* **Maintainability:** Use modular design and version control for easy maintenance and updates.\n", "* **Cost Optimization:** Choose cost-effective cloud services and optimize resource utilization.\n", "* **Legal and Compliance:** Ensure the system complies with relevant legal and regulatory requirements.\n", "\n", "\n", "This approach provides a robust and scalable solution for searching contract documents based on semantic understanding, leveraging the power of cutting-edge technologies and cloud services.\n", "\"\n", "\n", "Based on the advice of the senior software engineer, you have planned out your presentation:\n", "\"## Presentation Structure Plan: Contract Document Search System\n", "\n", "This plan outlines the structure of a presentation proposing a cutting-edge contract document search system. The goal is to secure approval and funding for the project by clearly articulating the problem, the proposed solution, its benefits, and the associated resources and costs.\n", "\n", "**I. Title Slide (1 slide)**\n", "\n", "* **Title:**  Intelligent Contract Search: Empowering Your Business with Cutting-Edge Technology\n", "* **Subtitle:**  Unlocking the Value of Your Contract Data\n", "* **Your Name/Title**\n", "* **Company Logo/Date**\n", "\n", "**II. Executive Summary (1 slide)**\n", "\n", "* **Problem:** Briefly state the current challenges with contract document retrieval. (Inefficient, keyword-based, time-consuming)\n", "* **Solution:** Concisely describe the proposed AI-powered semantic search solution.\n", "* **Key Benefits:** Highlight 2-3 major advantages (e.g., improved efficiency, better insights, reduced risk).\n", "* **Expected ROI:** Briefly mention potential return on investment (e.g., time saved, improved compliance).\n", "\n", "**III. The Problem: Current State of Contract Search (2 slides)**\n", "\n", "* **Slide 1: Pain Points:**  Describe the current inefficiencies and limitations of manual or keyword-based search.  Use visuals (e.g., flowchart depicting a cumbersome process, graph showing time wasted).\n", "* **Slide 2: Business Impact:** Quantify the negative impact of these inefficiencies. (e.g., time spent searching, missed deadlines, potential legal risks).  Include concrete examples or case studies if possible.\n", "\n", "**IV. The Solution: AI-Powered Semantic Search (3 slides)**\n", "\n", "* **Slide 1: Overview:** Introduce the concept of semantic search and how it differs from traditional keyword search. Explain how LLMs and vector databases enable this. Use simple, non-technical language.\n", "* **Slide 2: System Architecture:** Present a clear visual diagram of the system architecture (document preprocessing, embedding generation, vector database, query processing, user interface).\n", "* **Slide 3: Benefits of Semantic Search:**  Elaborate on the key advantages: improved accuracy, faster retrieval, better understanding of contract content, enhanced compliance, and reduced risk.\n", "\n", "**V. Technology Stack and Infrastructure (2 slides)**\n", "\n", "* **Slide 1: Core Technologies:**  Present the chosen technologies (OCR engine, LLM provider, vector database, cloud platform) and justify their selection based on performance, scalability, security, and cost-effectiveness.\n", "* **Slide 2: Infrastructure Diagram:**  Visualize the cloud infrastructure, highlighting key components (document storage, compute services, database, API integrations) and their interactions.\n", "\n", "**VI. Human Resources and Expertise (1 slide)**\n", "\n", "* **Team Composition:** Outline the required roles and expertise (e.g., data scientists, software engineers, cloud architects, legal experts).  Emphasize the existing internal expertise and any necessary external resources.\n", "\n", "**VII. Project Timeline (1 slide)**\n", "\n", "* **Phased Approach:** Present a clear timeline with key milestones (e.g., data preprocessing, system development, testing, deployment, training).  Use a Gantt chart or similar visual.\n", "\n", "**VIII. Cost and Budget (1 slide)**\n", "\n", "* **Cost Breakdown:**  Provide a detailed breakdown of estimated costs (e.g., cloud services, software licenses, personnel, consulting).\n", "* **ROI Projection:**  Reiterate the potential return on investment, emphasizing long-term cost savings and efficiency gains.\n", "\n", "**IX.  Demo (1-2 slides + Live Demo)**\n", "\n", "* **Pre-recorded Demo:** Showcase a short, pre-recorded demo of the system in action, highlighting key features and user experience.\n", "* **Live Demo (Optional):** If feasible, include a brief live demo to further impress the audience.\n", "\n", "**X. Conclusion and Call to Action (1 slide)**\n", "\n", "* **Recap Key Benefits:** Briefly summarize the key advantages of the proposed solution.\n", "* **Next Steps:** Clearly state the desired next steps (e.g., project approval, budget allocation).\n", "* **Reinforce Value Proposition:** End with a strong statement emphasizing the value and transformative potential of the intelligent contract search system.\n", "\n", "\n", "**XI. Q&A (Open)**\n", "\n", "* Be prepared to answer questions about the technical details, implementation plan, budget, and potential challenges.\n", "\n", "\n", "This structured approach will ensure a clear, concise, and compelling presentation that effectively communicates the value proposition of the intelligent contract search system and increases the likelihood of project approval.  The inclusion of visuals, quantifiable benefits, and a clear call to action will further strengthen the proposal.\n", "\"\n", "\n", "Following the plan you have created above, and referencing the technical advice of the senior software engineer,\n", "describe the content that will appear on EACH slide in detail.\n", "\n", "Pay extra attention to the following points:\n", "1) If a diagram or image should go on a slide (e.g. an infrastructure diagram, organization chart or a GANTT chart etc.), \n", "you must describe it with enough detail such that someone reading the description would be able to reproduce it perfectly.\n", "\n", "2) This slide content plan will be passed on to another person, so the slide descriptions must be as precise and specific as possible.\n", "\n", "3) Think carefully about whether or not the needs of the client are being met with this proposal.\n", "\n", "4) Make sure to include the content that should appear on the title slide.\n", "\n", "If this proposal is successful, you will get a big raise!\n", "\n", "IMPORTANT: Make sure to separate the content of each slide with the following markers <Slide X START> and <Slide X END>, where X represents the slide number.\n", "\n"]}], "source": ["slide_content_prompt = \\\n", "f\"\"\"\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"{query}\"\n", "\n", "After consulting with a senior software engineer, he has provided you the following approach to build such a system:\n", "\"{brainstorm_response['text']}\"\n", "\n", "Based on the advice of the senior software engineer, you have planned out your presentation:\n", "\"{skeleton_response['text']}\"\n", "\n", "Following the plan you have created above, and referencing the technical advice of the senior software engineer,\n", "describe the content that will appear on EACH slide in detail.\n", "\n", "Pay extra attention to the following points:\n", "1) If a diagram or image should go on a slide (e.g. an infrastructure diagram, organization chart or a GANTT chart etc.), \n", "you must describe it with enough detail such that someone reading the description would be able to reproduce it perfectly.\n", "\n", "2) This slide content plan will be passed on to another person, so the slide descriptions must be as precise and specific as possible.\n", "\n", "3) Think carefully about whether or not the needs of the client are being met with this proposal.\n", "\n", "4) Make sure to include the content that should appear on the title slide.\n", "\n", "If this proposal is successful, you will get a big raise!\n", "\n", "IMPORTANT: Make sure to separate the content of each slide with the following markers <Slide X START> and <Slide X END>, where X represents the slide number.\n", "\"\"\"\n", "print(slide_content_prompt)"]}, {"cell_type": "code", "execution_count": 27, "id": "2c7363fe", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide 1 START>\n", "\n", "* **Title:** Intelligent Contract Search: Empowering Your Business with Cutting-Edge Technology\n", "* **Subtitle:** Unlocking the Value of Your Contract Data\n", "* **Your Name/Title:** [Your Name/Title]\n", "* **Company Logo/Date:** [Your Company Logo/Date]\n", "\n", "<Slide 1 END>\n", "\n", "<Slide 2 START>\n", "\n", "* **Title:** Executive Summary\n", "* **Problem:**  Locating specific contract information is currently a slow, manual, and error-prone process, relying on inefficient keyword searches and often requiring review of numerous irrelevant documents.\n", "* **Solution:** We propose an AI-powered semantic search system that understands the meaning of your queries and retrieves the most relevant contracts quickly and accurately.\n", "* **Key Benefits:**  Drastically reduce contract search time, improve compliance by ensuring access to the correct information, and gain valuable insights from your contract data.\n", "* **Expected ROI:**  Save hundreds of hours annually in search time, minimize legal risks associated with inaccurate information, and improve overall operational efficiency.\n", "\n", "<Slide 2 END>\n", "\n", "<Slide 3 START>\n", "\n", "* **Title:** The Problem: Current State of Contract Search - Pain Points\n", "* **Content:**\n", "    * **Visual:** A flowchart depicting the current contract search process.  The flowchart should start with \"User needs contract information,\" then proceed through steps like \"Keyword search in document repository,\" \"Manual review of multiple documents,\" \"Potential for missed information,\" and finally end with \"Information retrieved (potentially inaccurate/incomplete).\"  Use clear and concise labels for each step.\n", "    * **Bullet Points:**\n", "        * Difficulty finding specific clauses or information within lengthy contracts.\n", "        * Reliance on imprecise keyword searches leading to irrelevant results.\n", "        * Time-consuming manual review of numerous documents.\n", "        * Inconsistent search results due to variations in terminology and phrasing.\n", "\n", "<Slide 3 END>\n", "\n", "<Slide 4 START>\n", "\n", "* **Title:** The Problem: Current State of Contract Search - Business Impact\n", "* **Content:**\n", "    * **Visual:** A bar graph showing estimated time wasted per employee per week on manual contract searches.  The graph should have \"Employees\" on the x-axis and \"Hours Wasted\" on the y-axis. Include data labels above each bar representing the average hours wasted.\n", "    * **Bullet Points:**\n", "        * **Quantified Impact:**  \"On average, employees spend X hours per week searching for contract information, resulting in Y lost productivity hours annually.\" (Replace X and Y with estimated values).\n", "        * **Financial Impact:** \"This translates to an estimated annual cost of $Z in lost productivity.\" (Replace Z with estimated cost).\n", "        * **Risk:** \"Inefficient contract search increases the risk of missed deadlines, compliance violations, and potential legal disputes.\"\n", "        * **Example:** \"In a recent case, a missed clause in a contract resulted in [describe a negative consequence, e.g., a financial penalty].\"\n", "\n", "<Slide 4 END>\n", "\n", "<Slide 5 START>\n", "\n", "* **Title:** The Solution: AI-Powered Semantic Search - Overview\n", "* **Content:**\n", "    * **Headline:** \"Moving Beyond Keywords: Understanding the Meaning Behind Your Search\"\n", "    * **Explanation:** \"Semantic search uses advanced AI techniques to understand the *meaning* of both your search query and the content of your contracts.  This goes beyond simple keyword matching to find truly relevant information, even if the exact wording doesn't match.\"\n", "    * **How it Works (briefly):** \"Large Language Models (LLMs) convert text into numerical representations (embeddings) that capture semantic meaning.  These embeddings are stored in a specialized vector database, allowing for fast and efficient similarity searches.\"\n", "\n", "<Slide 5 END>\n", "\n", "<Slide 6 START>\n", "\n", "* **Title:** The Solution: AI-Powered Semantic Search - System Architecture\n", "* **Content:** A diagram illustrating the system architecture. The diagram should consist of boxes representing each stage, connected by arrows indicating the flow of data.\n", "    * **Boxes (from left to right):**\n", "        * \"Document Preprocessing (OCR, Cleaning, Chunking)\"\n", "        * \"Embedding Generation (LLM)\"\n", "        * \"Vector Database (Pinecone/Weaviate)\"\n", "        * \"Query Processing (Embedding, Similarity Search, Ranking)\"\n", "        * \"User Interface (Search, Results, Filtering)\"\n", "    * **Arrows:** <PERSON>s should connect each box in the sequence described above, showing the flow of data from document preprocessing to the user interface.\n", "\n", "<Slide 6 END>\n", "\n", "<Slide 7 START>\n", "\n", "* **Title:** The Solution: AI-Powered Semantic Search - Benefits\n", "* **Content:**\n", "    * **Improved Accuracy:** Find the most relevant contracts, even if they don't contain the exact keywords.\n", "    * **Faster Retrieval:**  Reduce search time from hours to seconds.\n", "    * **Better Understanding:** <PERSON>ain deeper insights into contract content and identify key clauses quickly.\n", "    * **Enhanced Compliance:** Ensure access to the correct information for audits and regulatory requirements.\n", "    * **Reduced Risk:** Minimize the risk of missed deadlines, errors, and legal disputes.\n", "\n", "<Slide 7 END>\n", "\n", "<Slide 8 START>\n", "\n", "* **Title:** Technology Stack and Infrastructure - Core Technologies\n", "* **Content:**\n", "    * **OCR Engine:**  [Chosen OCR engine, e.g., Tesseract, Amazon Textract] - Justification: [e.g., High accuracy, cost-effective, cloud-based]\n", "    * **LLM Provider:** [Chosen LLM, e.g., OpenAI embeddings API, Sentence Transformers] - Justification: [e.g., State-of-the-art performance, readily available API]\n", "    * **Vector Database:** [Chosen database, e.g., Pinecone, Weaviate] - Justification: [e.g., Optimized for similarity search, scalable, managed service]\n", "    * **Cloud Platform:** [Chosen platform, e.g., AWS, Azure, GCP] - Justification: [e.g., Existing infrastructure, cost-effective, reliable]\n", "\n", "<Slide 8 END>\n", "\n", "<Slide 9 START>\n", "\n", "* **Title:** Technology Stack and Infrastructure - Infrastructure Diagram\n", "* **Content:** A diagram visualizing the cloud infrastructure.\n", "    * **Boxes:**\n", "        * \"Document Storage (AWS S3/Azure Blob Storage/GCP Storage)\"\n", "        * \"Compute (AWS Lambda/Azure Functions/GCP Functions)\"\n", "        * \"Vector Database (Pinecone/Weaviate)\"\n", "        * \"LLM API (OpenAI/etc.)\"\n", "        * \"User Interface (Web Application)\"\n", "    * **Arrows:** Arrows should connect the boxes to show the flow of data and interactions between components.  For example, an arrow should go from \"Document Storage\" to \"Compute,\" indicating that the compute service processes documents from storage.  Similarly, arrows should connect \"Compute\" to \"Vector Database\" and \"LLM API,\" and \"Vector Database\" to \"User Interface.\"\n", "\n", "<Slide 9 END>\n", "\n", "<Slide 10 START>\n", "\n", "* **Title:** Human Resources and Expertise\n", "* **Content:**\n", "    * **Data Scientist:**  For model training and optimization. (1 FTE)\n", "    * **Software Engineers:** For system development and integration. (2 FTEs)\n", "    * **Cloud Architect:** For infrastructure setup and management. (1 FTE)\n", "    * **Legal Expert:** For compliance and data privacy guidance. (Part-time/Consultant)\n", "    * **Note:** Leverage existing internal IT team for ongoing support.\n", "\n", "<Slide 10 END>\n", "\n", "<Slide 11 START>\n", "\n", "* **Title:** Project Timeline\n", "* **Content:** A Gantt chart visualizing the project timeline.\n", "    * **Tasks (Rows):**\n", "        * Data Preprocessing\n", "        * System Development\n", "        * Testing\n", "        * Deployment\n", "        * Training\n", "    * **Timeline (Columns):**  Divide the timeline into months or quarters, spanning the expected project duration.\n", "    * **Bars:**  Horizontal bars representing the duration of each task, placed within the appropriate timeframe on the chart.\n", "\n", "<Slide 11 END>\n", "\n", "<Slide 12 START>\n", "\n", "* **Title:** Cost and Budget\n", "* **Content:**\n", "    * **Cost Breakdown:**\n", "        * Cloud Services: $[Estimated cost]\n", "        * Software Licenses: $[Estimated cost]\n", "        * Personnel: $[Estimated cost]\n", "        * Consulting: $[Estimated cost]\n", "        * **Total:** $[Total estimated cost]\n", "    * **ROI Projection:**  \"Based on estimated time savings and improved efficiency, we project a return on investment within [timeframe] through reduced operational costs and minimized legal risks.\"  Quantify the ROI with specific figures if possible.\n", "\n", "<Slide 12 END>\n", "\n", "<Slide 13 START>\n", "\n", "* **Title:** Demo - Pre-recorded\n", "* **Content:**  A screenshot or still image from the pre-recorded demo, with a caption like \"See the intelligent contract search in action!\"\n", "\n", "<Slide 13 END>\n", "\n", "\n", "<Slide 14 START> (Optional Live Demo Slide - If applicable)\n", "\n", "* **Title:** Demo - Live\n", "* **Content:**  \"Live Demo - Witness the power of semantic search firsthand.\"\n", "\n", "<Slide 14 END>\n", "\n", "\n", "<Slide 15 START>\n", "\n", "* **Title:** Conclusion and Call to Action\n", "* **Content:**\n", "    * **Recap:** \"This intelligent contract search system will revolutionize how your company accesses and utilizes contract data, saving time, reducing risk, and improving decision-making.\"\n", "    * **Next Steps:** \"We recommend approving this project and allocating the necessary budget to begin implementation immediately.\"\n", "    * **Value Proposition:** \"Invest in the future of your contract management and unlock the full potential of your valuable contract data.\"\n", "\n", "<Slide 15 END>\n", "\n", "<Slide 16 START>\n", "\n", "* **Title:** Q&A\n", "\n", "<Slide 16 END>\n", "\n"]}], "source": ["slide_content_response = llm.call(query=slide_content_prompt)['text']\n", "print(slide_content_response)"]}, {"cell_type": "code", "execution_count": null, "id": "3ba262a7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "auto_script", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}