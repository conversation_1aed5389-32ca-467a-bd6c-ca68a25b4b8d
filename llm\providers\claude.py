# llm/providers/anthropic.py
import anthropic
import asyncio
import base64
import io
from typing import List, Union, Dict, Any, Optional
from PIL import Image # For handling image inputs

class Anthropic_LLM:
    def __init__(self, api_key: str, model: str = "claude-3-opus-20240229", temperature: float = 0.7, max_tokens: int = 1024):
        """
        Initializes the Anthropic_LLM client.

        Args:
            api_key (str): Your Anthropic API key.
            model (str): The model to use (default: "claude-3-opus-20240229").
            temperature (float): The sampling temperature (default: 0.7).
            max_tokens (int): The maximum number of tokens to generate (default: 1024).
                              This is a required parameter for Anthrop<PERSON>'s messages API.
        """
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens

        self.client = anthropic.Anthropic(api_key=self.api_key)

    async def call(self, query: str, **kwargs: Any) -> Dict[str, Any]:
        """
        Makes an asynchronous text generation call to the Anthropic API.

        Args:
            query (str): The user's prompt.
            **kwargs (Any): Additional keyword arguments to pass to the
                            client.messages.create method (e.g., system, stop_sequences).

        Returns:
            Dict[str, Any]: A dictionary containing the model's response,
                            typically {'text': <generated_text>}.
                            Returns {'error': <error_message>} on failure.
        """
        messages = [
            {"role": "user", "content": query}
        ]

        try:
            # Anthropic's client.messages.create is synchronous, so run in a thread
            response = await asyncio.to_thread(
                self.client.messages.create,
                model=self.model,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                messages=messages,
                **kwargs # Allow passing other parameters like system, stop_sequences etc.
            )
            # Anthropic's response content is a list of content blocks
            if response.content and isinstance(response.content, list):
                text_content = ""
                for block in response.content:
                    if hasattr(block, 'text'):
                        text_content += block.text
                return {'text': text_content}
            else:
                return {'text': ""} # Or handle as an error if no text content
        except Exception as e:
            print(f"Error calling Anthropic API (text): {e}")
            return {'error': str(e)}

    async def call_with_images(self, query: str, images: Union[List[Image.Image], Image.Image], **kwargs: Any) -> Dict[str, Any]:
        """
        Makes an asynchronous multimodal generation call (text + images) to the Anthropic API.

        Args:
            query (str): The user's text prompt.
            images (Union[List[PIL.Image.Image], PIL.Image.Image]): One or more PIL Image objects.
            **kwargs (Any): Additional keyword arguments to pass to the
                            client.messages.create method.

        Returns:
            Dict[str, Any]: A dictionary containing the model's response,
                            typically {'text': <generated_text>}.
                            Returns {'error': <error_message>} on failure.
        """
        if not isinstance(images, list):
            images = [images]

        content_blocks = []
        # Add the text query first
        content_blocks.append({"type": "text", "text": query})

        # Process each image
        for img in images:
            buffered = io.BytesIO()
            # Anthropic supports JPEG, PNG, GIF, WEBP
            # We'll default to JPEG for simplicity, but you could add logic for PNG if transparency is needed
            img_format = "JPEG"
            mime_type = "image/jpeg"
            if img.mode == 'RGBA': # Check for transparency
                img_format = "PNG"
                mime_type = "image/png"
            
            img.save(buffered, format=img_format)
            img_base64 = base64.b64encode(buffered.getvalue()).decode("utf-8")
            
            content_blocks.append({
                "type": "image",
                "source": {
                    "type": "base64",
                    "media_type": mime_type,
                    "data": img_base64,
                },
            })

        messages = [
            {"role": "user", "content": content_blocks}
        ]

        try:
            response = await asyncio.to_thread(
                self.client.messages.create,
                model=self.model,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                messages=messages,
                **kwargs
            )
            if response.content and isinstance(response.content, list):
                text_content = ""
                for block in response.content:
                    if hasattr(block, 'text'):
                        text_content += block.text
                return {'text': text_content}
            else:
                return {'text': ""}
        except Exception as e:
            print(f"Error calling Anthropic API (multimodal): {e}")
            return {'error': str(e)}