<!DOCTYPE html>
<html>
<head>
<title>Strategy Trade-off Radar</title>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<style>
    body, html {
        margin: 0;
        padding: 0;
        font-family: 'Roboto', sans-serif;
        background-color: #f0f0f0;
    }
    .slide-container {
        width: 1280px;
        height: 720px;
        background-color: #ffffff;
        position: relative;
        overflow: hidden;
    }
    .slide-content {
        width: 100%;
        height: 100%;
        padding: 40px 60px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
    }
    .slide-title {
        font-size: 2.5em;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 20px;
        flex-shrink: 0;
    }
</style>
</head>
<body>

<div class="slide-container">
    <div class="slide-content">
        <h1 class="slide-title">Strategy Trade-off Radar — COBOL Migration Options to AWS</h1>

        <div class="grid grid-cols-12 gap-x-8 flex-grow min-h-0">
            <!-- Left Panel: Radar Chart -->
            <div class="col-span-7 flex flex-col h-full">
                <div id="radar-chart" class="w-full flex-grow"></div>
            </div>

            <!-- Right Panel: Highlight Tiles -->
            <div class="col-span-5 flex flex-col justify-center space-y-4">
                <div class="bg-blue-50 border-l-4 border-blue-500 p-3 rounded-r-lg">
                    <h3 class="text-lg font-bold text-blue-800">Fastest Path to Stabilize: Rehost</h3>
                    <p class="text-md text-gray-700">Speed: <span class="font-semibold">5 / 5</span></p>
                </div>
                <div class="bg-teal-50 border-l-4 border-teal-500 p-3 rounded-r-lg">
                    <h3 class="text-lg font-bold text-teal-800">Lowest Change Risk: Rehost</h3>
                    <p class="text-md text-gray-700">Risk Mitigation: <span class="font-semibold">4.5 / 5</span></p>
                </div>
                <div class="bg-purple-50 border-l-4 border-purple-500 p-3 rounded-r-lg">
                    <h3 class="text-lg font-bold text-purple-800">Maximum Cloud-Native Value: Re-architect</h3>
                    <p class="text-md text-gray-700">Benefits & Agility: <span class="font-semibold">5 / 5</span></p>
                </div>
                <div class="bg-orange-50 border-l-4 border-orange-500 p-3 rounded-r-lg">
                    <h3 class="text-lg font-bold text-orange-800">Best Balanced ROI: Refactor</h3>
                    <p class="text-md text-gray-700">Composite Score: <span class="font-semibold">≈ 3.8 / 5</span></p>
                </div>
            </div>
        </div>

        <!-- Bottom Panel: Insights -->
        <div class="mt-4 pt-4 border-t-2 border-gray-200">
             <div class="grid grid-cols-2 grid-rows-2 gap-x-6 gap-y-3">
                <div class="text-sm text-gray-600"><strong class="text-indigo-700">Rehost:</strong> Minimizes disruption now, but yields limited modernization benefits; plan it as a stepping stone.</div>
                <div class="text-sm text-gray-600"><strong class="text-indigo-700">Refactor:</strong> Offers the best balance of risk, cost efficiency, and cloud gains—ideal for mission-critical cores.</div>
                <div class="text-sm text-gray-600"><strong class="text-indigo-700">Re-architect:</strong> Unlocks maximum agility, but requires significant time, automated testing, and phased cutovers.</div>
                <div class="text-sm text-gray-600"><strong class="text-indigo-700">Replace:</strong> Fits commodity domains; validate SLAs, fit-gap, data ownership, and integration cost before committing.</div>
            </div>
            <p class="text-center text-md font-semibold text-white bg-indigo-800 p-2 mt-4 rounded-lg">
                <strong>Takeaway:</strong> Sequence by domain—stabilize fast (Rehost/Replatform), then target Refactor/Re-architect where differentiation and long-term agility matter most.
            </p>
        </div>
    </div>
</div>

<script>
    var chartDom = document.getElementById('radar-chart');
    var myChart = echarts.init(chartDom);
    var option;

    option = {
        color: ['#3b82f6', '#14b8a6', '#f97316', '#8b5cf6', '#6b7280'],
        tooltip: {
            trigger: 'item'
        },
        legend: {
            data: ['Rehost', 'Replatform', 'Refactor', 'Re-architect', 'Replace'],
            bottom: 0,
            textStyle: {
                fontSize: 14
            }
        },
        radar: {
            indicator: [
                { name: 'Speed', max: 5, axisLabel: { show: true, fontSize: 12, color: '#333', formatter: function (v) { return v + ' ⚡'; } } },
                { name: 'Risk', max: 5, axisLabel: { show: true, fontSize: 12, color: '#333', formatter: function (v) { return v + ' 🛡️'; } } },
                { name: 'Cost', max: 5, axisLabel: { show: true, fontSize: 12, color: '#333', formatter: function (v) { return v + ' 💲'; } } },
                { name: 'Benefits', max: 5, axisLabel: { show: true, fontSize: 12, color: '#333', formatter: function (v) { return v + ' ☁️'; } } },
                { name: 'Agility', max: 5, axisLabel: { show: true, fontSize: 12, color: '#333', formatter: function (v) { return v + ' ♻️'; } } }
            ],
            radius: '65%',
            center: ['50%', '50%'],
            splitNumber: 5,
            axisName: {
                color: '#1a237e',
                fontSize: 16,
                fontWeight: 'bold'
            },
            splitArea: {
                areaStyle: {
                    color: ['rgba(250, 250, 250, 0.1)', 'rgba(230, 230, 230, 0.2)'],
                    shadowColor: 'rgba(0, 0, 0, 0.2)',
                    shadowBlur: 10
                }
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(150, 150, 150, 0.5)'
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(200, 200, 200, 0.5)'
                }
            }
        },
        series: [
            {
                name: 'Migration Strategies',
                type: 'radar',
                data: [
                    {
                        value: [5, 4.5, 2, 1, 2],
                        name: 'Rehost',
                        areaStyle: { opacity: 0.6 },
                        label: { show: true, formatter: '{c}' }
                    },
                    {
                        value: [4, 3.5, 3, 3, 3],
                        name: 'Replatform',
                        areaStyle: { opacity: 0.6 },
                        label: { show: true, formatter: '{c}' }
                    },
                    {
                        value: [3, 3, 4, 4, 4],
                        name: 'Refactor',
                        areaStyle: { opacity: 0.6 },
                        label: { show: true, formatter: '{c}' }
                    },
                    {
                        value: [2, 2, 3, 5, 5],
                        name: 'Re-architect',
                        areaStyle: { opacity: 0.6 },
                        label: { show: true, formatter: '{c}' }
                    },
                    {
                        value: [3, 2.5, 4, 4, 4],
                        name: 'Replace',
                        areaStyle: { opacity: 0.6 },
                        label: { show: true, formatter: '{c}' }
                    }
                ]
            }
        ]
    };

    option && myChart.setOption(option);
    window.addEventListener('resize', myChart.resize);
</script>

</body>
</html>