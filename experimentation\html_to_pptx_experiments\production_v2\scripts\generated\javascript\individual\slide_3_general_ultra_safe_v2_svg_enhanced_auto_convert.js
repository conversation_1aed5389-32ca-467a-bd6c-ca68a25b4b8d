const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // --- ULTRA-SAFE CONSTANTS ---

    // Base64 image constants (pre-converted from SVG)
    const CASTLE_BASE64 = "data:image/png;base64,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";
    const UNLOCKED_PADLOCK_BASE64 = "data:image/png;base64,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";
    const WARNING_ICON_BASE64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABmJLR0QA/wD/AP+gvaeTAAABhElEQVRIia2VS07CQABA30xLS8EE3BiQsDJp8AoYF95DT+AJTFz0KKx7ALZcwZVhIVa3DRsloEIBN/LvtDOGWTWdzrz3pmkKBiMIenYQ9GyTNUYPy5Jz+3fZ0V0jdB8Mw9DqvzeeBVjz8bQVBDeJlpQuoP/WuBPgAxdbJccBhGFoIXiAJbBECB5034UWYGXv12P8egwGFbmAbfur1oDryxdMKnIB2/a10w/Oqp9GFZmAffvVMKnIBOzbj7pDRt2hUYUSkGa/mCxYTOZGFUrAvj2AcATCtQC0K1IBqrMXrkS6m49fpyIVkGYPIF2JcDdLdCoOACp7AFGSCM/auZdXcQBQ2QPIsoU82QXkVewAsuwBrEoBu3p41FkVO4AsewC77mDX3IP7WRVrQJ49wDT64ud1kjqnqlgD8uwBknhGEs9S51QVUtceoNyuUG5XlPNpFfbaXuAXnYSnqAlRU7lJ3ig6Cd/TwqqiI7b+tf6/d00fg/l42rKj6LwkpLw/8uYAeF7B+wXDWckIeLEX1gAAAABJRU5ErkJggg==";

    // Color Palette
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';

    // Positioning & Sizing
    const SAFE_MARGIN = 0.3;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 3.8;
    const RIGHT_COL_X = 4.3;
    const RIGHT_COL_W = 5.4; // Adjusted for more text space

    // --- SLIDE CONTENT ---

    // Background
    slide.background = { color: COLOR_BACKGROUND };

    // Title
    slide.addText('The Weaknesses of Our Current Security Posture', {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16,
        color: COLOR_PRIMARY,
        bold: true,
        align: 'left',
        valign: 'top'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2,
        h: 0.03,
        fill: { color: COLOR_PRIMARY }
    });

    // --- LEFT COLUMN (VISUALS) ---
    const VISUAL_CENTER_X = LEFT_COL_X + LEFT_COL_W / 2;

    // Castle Icon (using pre-converted base64 PNG)
    slide.addImage({
        data: CASTLE_BASE64,
        x: VISUAL_CENTER_X - 1.5, // Center the 3.0" wide image
        y: 1.5,
        w: 3.0,
        h: 3.0
    });

    // Unlocked Padlock Icon (using pre-converted base64 PNG)
    slide.addImage({
        data: UNLOCKED_PADLOCK_BASE64,
        x: VISUAL_CENTER_X - 0.35, // Center the 0.7" wide image
        y: 2.4, // Positioned visually inside the castle
        w: 0.7,
        h: 0.7
    });

    // Visual Label
    slide.addText('Perimeter Security', {
        x: VISUAL_CENTER_X - 1.0, // Center the 2.0" wide text box
        y: 4.6,
        w: 2.0,
        h: 0.3,
        fontSize: 12,
        bold: true,
        color: COLOR_PRIMARY,
        align: 'center',
        valign: 'middle',
        border: { color: COLOR_PRIMARY, width: 1.5 }
    });

    // --- RIGHT COLUMN (TEXT) ---
    const weaknesses = [
        { strong: 'Increased Sophistication of Cyberattacks:', text: 'Attackers are bypassing traditional perimeter defenses.' },
        { strong: 'Insider Threats:', text: 'Malicious or negligent employees can compromise sensitive data.' },
        { strong: 'Complex IT Environment:', text: 'Cloud adoption, remote work, and BYOD create new attack vectors.' },
        { strong: 'Compliance Requirements:', text: 'Regulations like GDPR and CCPA demand stronger data protection measures.' },
        { strong: 'Lack of Visibility:', text: 'Difficult to track user activity and identify suspicious behavior across the network.' },
        { strong: 'The Cost of Inaction:', text: 'Breaches result in significant financial, reputational, and legal liabilities.' }
    ];

    let currentY = CONTENT_START_Y + 0.2;
    const ICON_SIZE = 0.2;
    const ICON_MARGIN = 0.15;
    const TEXT_X = RIGHT_COL_X + ICON_SIZE + ICON_MARGIN;
    const TEXT_W = RIGHT_COL_W - (ICON_SIZE + ICON_MARGIN);
    const LINE_HEIGHT = 0.6; // Generous spacing for readability

    weaknesses.forEach(item => {
        // Add warning icon (using pre-converted base64 PNG)
        slide.addImage({
            data: WARNING_ICON_BASE64,
            x: RIGHT_COL_X,
            y: currentY + 0.05, // Vertically align with text
            w: ICON_SIZE,
            h: ICON_SIZE
        });

        // Add weakness text with mixed formatting
        slide.addText([
            { text: item.strong + ' ', options: { color: COLOR_TEXT_SECONDARY, bold: true } },
            { text: item.text, options: { color: COLOR_TEXT_PRIMARY, bold: false } }
        ], {
            x: TEXT_X,
            y: currentY,
            w: TEXT_W,
            h: LINE_HEIGHT,
            fontSize: 10,
            lineSpacing: 14 // Corresponds to 1.6 line-height
        });

        currentY += LINE_HEIGHT;
    });

    return pptx.writeFile({ fileName: 'scripts/generated/presentations/slide_3_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
