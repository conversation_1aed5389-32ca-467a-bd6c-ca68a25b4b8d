const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE PALETTE & STYLES
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_SHAPE_FILL = '2a4365'; // Derived from rgba(42, 67, 101, 0.5)
    const COLOR_SHAPE_BORDER = '1d3b66';

    // ULTRA-SAFE LAYOUT CONSTANTS (GENERIC, NO SLIDE-SPECIFIC NAMES)
    const SAFE_MARGIN = 0.3;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.4;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Two-column layout dimensions
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 3.8;
    const RIGHT_COL_X = 4.5;
    const RIGHT_COL_W = 4.2; // Ends at 8.7, adjusted to 4.0 to be safe
    const SAFE_RIGHT_COL_W = 4.0; // Ends at 8.5 (ULTRA-SAFE)

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Add Title
    slide.addText("Building a Secure Foundation with Cloud Technologies", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16,
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        valign: 'top'
    });

    // Add Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.75,
        w: 1.2, // 120px equivalent
        h: 0.03, // 3px equivalent
        fill: { color: COLOR_PRIMARY_ACCENT }
    });

    // --- LEFT COLUMN: Architecture Diagram ---
    const architectureLayers = [
        { text: "Identity:", detail: "Azure AD Conditional Access" },
        { text: "Application:", detail: "Web Apps / Microservices" },
        { text: "Data:", detail: "S3 Buckets / Azure Blob Storage" },
        { text: "Compute:", detail: "EC2 Instances / Azure VMs" },
        { text: "Network:", detail: "Azure Virtual Network / NSGs" },
        { text: "Infrastructure:", detail: "Cloud Provider" }
    ];

    let currentY = CONTENT_START_Y;
    const layerHeight = 0.55;
    const layerSpacing = 0.1;

    architectureLayers.forEach(layer => {
        if ((currentY + layerHeight) > MAX_CONTENT_Y) return; // Prevent vertical overflow

        // Layer Box (Rounded Rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: LEFT_COL_X,
            y: currentY,
            w: LEFT_COL_W,
            h: layerHeight,
            fill: { color: COLOR_SHAPE_FILL },
            line: { color: COLOR_SHAPE_BORDER, width: 1 },
            rectRadius: 0.1
        });

        // Layer Text
        slide.addText([
            { text: layer.text, options: { bold: true, color: COLOR_TEXT_SECONDARY, fontSize: 9 } },
            { text: ` ${layer.detail}`, options: { color: COLOR_TEXT_PRIMARY, fontSize: 9 } }
        ], {
            x: LEFT_COL_X + 0.15,
            y: currentY,
            w: LEFT_COL_W - 0.6, // Leave space for icon
            h: layerHeight,
            valign: 'middle'
        });

        // Lock Icon (recreated with basic shapes for safety)
        const iconX = LEFT_COL_X + LEFT_COL_W - 0.45;
        const iconY = currentY + (layerHeight / 2) - 0.125;
        slide.addShape(pptx.shapes.OVAL, {
            x: iconX,
            y: iconY,
            w: 0.25,
            h: 0.25,
            line: { color: COLOR_PRIMARY_ACCENT, width: 1.5 }
        });

        currentY += layerHeight + layerSpacing;
    });

    // --- RIGHT COLUMN: Technology Details ---
    const techItems = [
        { title: "Identity & Access Management (IAM)", details: "Azure AD Conditional Access + Passwordless Auth + Behavioral Biometrics" },
        { title: "Device Security & Endpoint Management", details: "Microsoft Endpoint Manager (Intune) + DPA + Remote Browser Isolation" },
        { title: "Network Security & Microsegmentation", details: "Azure VNets/NSGs + Cloudflare ZTNA + AI Anomaly Detection" },
        { title: "Data Security & Protection", details: "Microsoft Purview + Cloud-Native Key Management (KMS)" },
        { title: "Automation & Orchestration", details: "Azure Logic Apps + Ansible / Terraform (IaC)" }
    ];

    currentY = CONTENT_START_Y;
    const itemSpacing = 0.15;

    techItems.forEach(item => {
        const titleHeight = 0.25;
        const detailHeight = 0.4;
        const totalItemHeight = titleHeight + detailHeight + itemSpacing;

        if ((currentY + totalItemHeight) > MAX_CONTENT_Y) return; // Prevent vertical overflow

        // Tech Item Title
        slide.addText(item.title, {
            x: RIGHT_COL_X,
            y: currentY,
            w: SAFE_RIGHT_COL_W,
            h: titleHeight,
            fontSize: 10,
            color: COLOR_PRIMARY_ACCENT,
            bold: true
        });

        // Tech Item Details
        slide.addText(item.details, {
            x: RIGHT_COL_X,
            y: currentY + titleHeight,
            w: SAFE_RIGHT_COL_W,
            h: detailHeight,
            fontSize: 9,
            color: COLOR_TEXT_SECONDARY,
            valign: 'top'
        });

        currentY += totalItemHeight;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_5_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
