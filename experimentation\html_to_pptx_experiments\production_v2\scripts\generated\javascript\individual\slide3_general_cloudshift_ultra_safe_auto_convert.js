const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION (Professional Organization)
    // =======================================================================

    // Slide Dimensions & Professional Spacing
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0; // Higher placement for dense content
    const MAX_CONTENT_Y = 5.2; // Allow content to go slightly lower to fit everything

    // Color Palette (extracted from HTML)
    const COLORS = {
        headerBg: '0078D4',
        headerText: 'FFFFFF',
        title: '005A9E',
        sectionHeader: '0078D4',
        bodyText: '374151', // text-gray-700
        cardBg: 'F9FAFB', // bg-gray-50
        cardBorder: 'E5E7EB', // border-gray-200
        cardTitle: '005A9E',
        cardSourceText: '6B7280', // text-gray-500
        barAws: '6B7280', // bg-gray-500
        barAzure: '0078D4',
        barGcp: 'EA4335',
        mapLegendCore: '0078D4',
        mapLegendAi: '6B46C1',
        mapLegendCompliance: '16A34A',
    };

    // Font Sizes (Professional & Consistent, translated from large HTML sizes)
    const FONT_SIZES = {
        header: 12, // text-[1.2em] -> 12
        mainTitle: 18, // text-[2.5em] -> 18 (large but fits)
        sectionTitle: 12, // text-[1.3em] -> 12
        body: 10, // text-[1.2em] -> 10
        list: 9, // text-[1.2em] -> 9 (for lists to be tighter)
        cardTitle: 10, // text-[1.1em] -> 10
        cardSource: 8,
        barLabel: 8,
        mapLegend: 8,
    };

    // Layout Calculations (Clean & Efficient)
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN);
    const LEFT_COL_X = SAFE_MARGIN;
    const COL_W = (CONTENT_WIDTH - 0.4) / 2;
    const RIGHT_COL_X = LEFT_COL_X + COL_W + 0.4;

    // =======================================================================
    // 2. HELPER FUNCTIONS (Robust Edge Case Handling)
    // =======================================================================

    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            console.warn(`⚠️ Image failed, using fallback: ${imagePath}`);
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' }, line: { color: 'D1D5DB', width: 1 },
                rectRadius: 0.1
            });
            slide.addText(fallbackText, {
                x: options.x + 0.1, y: options.y + (options.h / 2) - 0.1,
                w: options.w - 0.2, h: 0.2, fontSize: 8, color: '6B7280',
                align: 'center', valign: 'middle'
            });
        }
    }

    // =======================================================================
    // 3. SLIDE BACKGROUND & HEADER (Professional Layout)
    // =======================================================================

    slide.background = { color: 'FFFFFF' };

    // Header with professional styling
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: 0, w: SLIDE_WIDTH, h: 0.7,
        fill: { color: COLORS.headerBg }
    });

    addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/azure-icon.svg', {
        x: SAFE_MARGIN, y: 0.15, w: 0.4, h: 0.4
    }, 'Azure Icon');

    slide.addText('Microsoft Azure', {
        x: SAFE_MARGIN + 0.5, y: 0.15, w: 3, h: 0.4,
        fontSize: FONT_SIZES.header,
        color: COLORS.headerText,
        bold: true,
        valign: 'middle'
    });

    // Main Title
    slide.addText('The Shift to Cloud: Why Azure?', {
        x: SAFE_MARGIN, y: 0.9, w: CONTENT_WIDTH, h: 0.5,
        fontSize: FONT_SIZES.mainTitle,
        color: COLORS.title,
        bold: true
    });

    let currentY = 1.5;

    // =======================================================================
    // 4. LEFT COLUMN: NARRATIVE CONTENT
    // =======================================================================
    let leftColY = currentY;

    // Section: Cloud Adoption
    slide.addText('Cloud Adoption: The Momentum', {
        x: LEFT_COL_X, y: leftColY, w: COL_W, h: 0.3,
        fontSize: FONT_SIZES.sectionTitle, color: COLORS.sectionHeader, bold: true
    });
    leftColY += 0.3;
    slide.addText(
        'Organizations are accelerating cloud adoption to drive digital transformation, support remote/hybrid work, manage explosive data growth, scale on demand, optimize costs via pay-as-you-go models, and strengthen business continuity with resilient, geo-distributed architectures.', {
            x: LEFT_COL_X, y: leftColY, w: COL_W, h: 0.9,
            fontSize: FONT_SIZES.body, color: COLORS.bodyText
        }
    );
    leftColY += 1.0;

    // Section: Problem / Opportunity
    slide.addText('Problem / Opportunity', {
        x: LEFT_COL_X, y: leftColY, w: COL_W, h: 0.3,
        fontSize: FONT_SIZES.sectionTitle, color: COLORS.sectionHeader, bold: true
    });
    leftColY += 0.3;
    const problems = [
        'On‑prem constraints: high maintenance and refresh costs, limited scalability during peak loads.',
        'Security exposure: inconsistent patching, fragmented tooling, and audit/compliance gaps.',
        'Data sprawl: rapidly growing volumes without unified governance, analytics, or AI readiness.'
    ];
    problems.forEach(item => {
        slide.addText('•', { x: LEFT_COL_X, y: leftColY, w: 0.2, h: 0.2, fontSize: FONT_SIZES.list, color: COLORS.bodyText });
        slide.addText(item, { x: LEFT_COL_X + 0.2, y: leftColY, w: COL_W - 0.2, h: 0.3, fontSize: FONT_SIZES.list, color: COLORS.bodyText });
        leftColY += 0.35;
    });
    leftColY += 0.1;

    // Section: Azure's Value Proposition
    slide.addText('Azure’s Value Proposition', {
        x: LEFT_COL_X, y: leftColY, w: COL_W, h: 0.3,
        fontSize: FONT_SIZES.sectionTitle, color: COLORS.sectionHeader, bold: true
    });
    leftColY += 0.3;
    const valueProps = [
        [{ text: 'Global Reach:', options: { bold: true } }, { text: ' The broadest cloud footprint for low latency and data residency. Example regions relevant to global operations: East US, West Europe, Southeast Asia, Japan East, Australia East, Brazil South, UAE North.' }],
        [{ text: 'Hybrid Cloud Capabilities:', options: { bold: true } }, { text: ' Azure Arc and Azure Stack enable seamless management and gradual migration across on‑prem, edge, and multi-cloud.' }],
        [{ text: 'Comprehensive Services:', options: { bold: true } }, { text: ' Compute, storage, networking, databases, AI/ML, analytics, IoT, DevOps—covering modernization and innovation end‑to‑end.' }],
        [{ text: 'Security & Compliance:', options: { bold: true } }, { text: ' Native tooling (Microsoft Defender for Cloud, Microsoft Sentinel) and extensive certifications (e.g., HIPAA, GDPR, ISO/IEC 27001).' }]
    ];
    valueProps.forEach(prop => {
        slide.addText(prop, {
            x: LEFT_COL_X, y: leftColY, w: COL_W, h: 0.5,
            fontSize: FONT_SIZES.list, color: COLORS.bodyText,
            lineSpacing: 12
        });
        leftColY += (prop[1].text.length > 150) ? 0.8 : 0.6; // Dynamic height based on text length
    });

    // =======================================================================
    // 5. RIGHT COLUMN: VISUALS
    // =======================================================================
    let rightColY = currentY;

    // Visual 1: Market Share (recreated as a bar chart)
    const chartCardY = rightColY;
    const chartCardH = 1.6;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: RIGHT_COL_X, y: chartCardY, w: COL_W, h: chartCardH,
        fill: { color: COLORS.cardBg },
        line: { color: COLORS.cardBorder, width: 1 },
        rectRadius: 0.15
    });
    slide.addText('Cloud Market Share (illustrative)', {
        x: RIGHT_COL_X + 0.2, y: chartCardY + 0.1, w: COL_W - 1.2, h: 0.2,
        fontSize: FONT_SIZES.cardTitle, color: COLORS.cardTitle, bold: true
    });
    slide.addText('Source: Industry analysts (2024)', {
        x: RIGHT_COL_X + COL_W - 1.2, y: chartCardY + 0.1, w: 1.0, h: 0.2,
        fontSize: FONT_SIZES.cardSource, color: COLORS.cardSourceText, align: 'right'
    });

    const chartData = [{
        name: 'Market Share',
        labels: ['AWS', 'Azure', 'Google Cloud'],
        values: [32, 25, 10]
    }];
    const chartOptions = {
        x: RIGHT_COL_X + 0.2, y: chartCardY + 0.3, w: COL_W - 0.4, h: 0.9,
        chartArea: { fill: { color: COLORS.cardBg, transparency: 100 } },
        barDir: 'bar',
        showLegend: false,
        showValue: true,
        dataLabelColor: 'FFFFFF',
        dataLabelFontSize: 8,
        dataLabelPosition: 'ctr',
        catAxisLabelFontSize: FONT_SIZES.barLabel,
        valAxisHidden: true,
        catAxisHidden: false,
        chartColors: [COLORS.barAws, COLORS.barAzure, COLORS.barGcp],
        border: { pt: 0, color: COLORS.cardBg }
    };
    slide.addChart(pptx.ChartType.bar, chartData, chartOptions);

    slide.addText('Note: For visualization only; exact figures vary by source and timeframe.', {
        x: RIGHT_COL_X + 0.2, y: chartCardY + chartCardH - 0.25, w: COL_W - 0.4, h: 0.2,
        fontSize: FONT_SIZES.cardSource, color: COLORS.cardSourceText
    });
    rightColY += chartCardH + 0.2;

    // Visual 2: Global Reach Map
    const mapCardY = rightColY;
    const mapCardH = 2.5;
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: RIGHT_COL_X, y: mapCardY, w: COL_W, h: mapCardH,
        fill: { color: COLORS.cardBg },
        line: { color: COLORS.cardBorder, width: 1 },
        rectRadius: 0.15
    });
    slide.addText('Azure Global Reach (selected regions)', {
        x: RIGHT_COL_X + 0.2, y: mapCardY + 0.1, w: COL_W - 0.4, h: 0.2,
        fontSize: FONT_SIZES.cardTitle, color: COLORS.cardTitle, bold: true
    });

    const mapContainerY = mapCardY + 0.4;
    const mapContainerH = 1.5;
    addImageWithFallback(slide, 'https://upload.wikimedia.org/wikipedia/commons/8/80/World_map_-_low_resolution.svg', {
        x: RIGHT_COL_X + 0.2, y: mapContainerY, w: COL_W - 0.4, h: mapContainerH
    }, 'World Map');

    // Add region dots on map
    const mapW = COL_W - 0.4;
    const mapH = mapContainerH;
    const mapX = RIGHT_COL_X + 0.2;
    const mapY = mapContainerY;
    const regions = [
        { top: 0.38, left: 0.28, colors: [COLORS.mapLegendCore, COLORS.mapLegendAi] }, // East US
        { top: 0.35, left: 0.50, colors: [COLORS.mapLegendCore, COLORS.mapLegendCompliance] }, // West Europe
        { top: 0.56, left: 0.78, colors: [COLORS.mapLegendCore] }, // Southeast Asia
        { top: 0.75, left: 0.85, colors: [COLORS.mapLegendCore] }, // Australia East
        { top: 0.38, left: 0.81, colors: [COLORS.mapLegendCore, COLORS.mapLegendAi] }, // Japan East
        { top: 0.70, left: 0.36, colors: [COLORS.mapLegendCore] }, // Brazil South
        { top: 0.42, left: 0.60, colors: [COLORS.mapLegendCore, COLORS.mapLegendCompliance] }, // UAE North
    ];
    regions.forEach(region => {
        region.colors.forEach((color, index) => {
            slide.addShape(pptx.shapes.OVAL, {
                x: mapX + (region.left * mapW) + (index * 0.1),
                y: mapY + (region.top * mapH),
                w: 0.08, h: 0.08,
                fill: { color: color }
            });
        });
    });

    // Map Legend
    const legendY = mapCardY + mapCardH - 0.4;
    const legendItems = [
        { text: 'Core services', color: COLORS.mapLegendCore },
        { text: 'AI/ML services', color: COLORS.mapLegendAi },
        { text: 'Compliance-ready', color: COLORS.mapLegendCompliance }
    ];
    let legendX = RIGHT_COL_X + 0.3;
    legendItems.forEach(item => {
        slide.addShape(pptx.shapes.OVAL, { x: legendX, y: legendY + 0.05, w: 0.1, h: 0.1, fill: { color: item.color } });
        slide.addText(item.text, {
            x: legendX + 0.15, y: legendY, w: 1.2, h: 0.2,
            fontSize: FONT_SIZES.mapLegend, color: COLORS.bodyText
        });
        legendX += 1.4;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide3_general_cloudshift.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
