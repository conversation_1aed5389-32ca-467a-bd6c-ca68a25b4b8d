
const PptxGenJS = require('pptxgenjs');

import PptxGenJS from "pptxgenjs";

/**
 * Creates a PowerPoint presentation from HTML content.
 * This function replicates a strategic roadmap slide.
 *
 * @returns {Promise<string>} A promise that resolves with the name of the generated file.
 */
function createPresentation() {
    const pptx = new PptxGenJS();
    pptx.layout = 'LAYOUT_16x9';

    const slide = pptx.addSlide({ masterName: "RoadmapSlide" });

    // Phase 1: Deep HTML Analysis & Phase 3: Advanced Color Extraction
    // Background color and image from the .slide-container style.
    slide.background = {
        color: '0A192F',
        // The SVG background is complex and not directly translatable.
        // A solid color is used as a fallback.
    };

    // Phase 2: PowerPoint Coordinate Mapping & Phase 4: Content Preservation

    // Title (H1)
    slide.addText("A Strategic Roadmap to Zero Trust", {
        x: 0.5,
        y: 0.5,
        w: 9.0,
        h: 0.7,
        fontSize: 36,
        fontFace: 'Roboto',
        color: '64FFDA',
        bold: true,
        align: 'left',
        valign: 'top'
    });

    // Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: 0.5,
        y: 1.2,
        w: 1.2,
        h: 0,
        line: { color: '64FFDA', width: 3 }
    });

    // --- Gantt Chart Area ---
    const ganttStartY = 1.8;
    const phaseTextX = 0.5;
    const phaseTextW = 2.4;
    const barAreaX = 3.0;
    const barAreaW = 6.5;
    const rowHeight = 0.9;

    // Timeline Header
    slide.addText("3 Months", { x: barAreaX, y: ganttStartY - 0.4, w: barAreaW / 4, h: 0.3, align: 'center', color: 'A8B2D1', fontSize: 11, fontFace: 'Roboto' });
    slide.addText("6 Months", { x: barAreaX + (barAreaW / 4), y: ganttStartY - 0.4, w: barAreaW / 4, h: 0.3, align: 'center', color: 'A8B2D1', fontSize: 11, fontFace: 'Roboto' });
    slide.addText("9 Months", { x: barAreaX + (barAreaW / 2), y: ganttStartY - 0.4, w: barAreaW / 4, h: 0.3, align: 'center', color: 'A8B2D1', fontSize: 11, fontFace: 'Roboto' });
    slide.addText("12 Months", { x: barAreaX + (barAreaW * 3 / 4), y: ganttStartY - 0.4, w: barAreaW / 4, h: 0.3, align: 'center', color: 'A8B2D1', fontSize: 11, fontFace: 'Roboto' });

    // Timeline Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: barAreaX,
        y: ganttStartY - 0.1,
        w: barAreaW,
        h: 0,
        line: { color: '3A3A3A', width: 1 } // Approximating border-gray-700
    });

    // Phase 1: Assessment & Planning
    let currentY = ganttStartY;
    slide.addText([
        { text: 'Phase 1: Assessment & Planning', options: { bold: true, fontSize: 14, color: 'A8B2D1', fontFace: 'Roboto' } },
        { text: '\nConduct security assessment, define policies, select technologies.', options: { fontSize: 11, color: 'CCD6F6', fontFace: 'Roboto' } }
    ], { x: phaseTextX, y: currentY, w: phaseTextW, h: rowHeight });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: barAreaX,
        y: currentY + 0.1,
        w: barAreaW / 4,
        h: 0.4,
        fill: { color: '64FFDA', transparency: 20 },
        line: { color: '64FFDA', width: 0 }
    });
    slide.addText("3 Months", { x: barAreaX, y: currentY + 0.1, w: barAreaW / 4, h: 0.4, align: 'center', color: '0A192F', bold: true, fontSize: 11, fontFace: 'Roboto' });

    // Phase 2: Identity & Access
    currentY += rowHeight;
    slide.addText([
        { text: 'Phase 2: Identity & Access', options: { bold: true, fontSize: 14, color: 'A8B2D1', fontFace: 'Roboto' } },
        { text: '\nImplement passwordless auth, conditional access, behavioral biometrics.', options: { fontSize: 11, color: 'CCD6F6', fontFace: 'Roboto' } }
    ], { x: phaseTextX, y: currentY, w: phaseTextW, h: rowHeight });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: barAreaX,
        y: currentY + 0.1,
        w: barAreaW / 2,
        h: 0.4,
        fill: { color: '64FFDA', transparency: 30 },
        line: { color: '64FFDA', width: 0 }
    });
    slide.addText("6 Months", { x: barAreaX, y: currentY + 0.1, w: barAreaW / 2, h: 0.4, align: 'center', color: '0A192F', bold: true, fontSize: 11, fontFace: 'Roboto' });

    // Phase 3: Device Security
    currentY += rowHeight;
    slide.addText([
        { text: 'Phase 3: Device Security', options: { bold: true, fontSize: 14, color: 'A8B2D1', fontFace: 'Roboto' } },
        { text: '\nDeploy endpoint management, enforce device posture, implement RBI.', options: { fontSize: 11, color: 'CCD6F6', fontFace: 'Roboto' } }
    ], { x: phaseTextX, y: currentY, w: phaseTextW, h: rowHeight });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: barAreaX,
        y: currentY + 0.1,
        w: barAreaW * 3 / 4,
        h: 0.4,
        fill: { color: '64FFDA', transparency: 40 },
        line: { color: '64FFDA', width: 0 }
    });
    slide.addText("9 Months", { x: barAreaX, y: currentY + 0.1, w: barAreaW * 3 / 4, h: 0.4, align: 'center', color: '0A192F', bold: true, fontSize: 11, fontFace: 'Roboto' });

    // Phase 4: Network Microsegmentation
    currentY += rowHeight;
    slide.addText([
        { text: 'Phase 4: Network Microsegmentation', options: { bold: true, fontSize: 14, color: 'A8B2D1', fontFace: 'Roboto' } },
        { text: '\nImplement SDP, configure security groups, deploy AI anomaly detection.', options: { fontSize: 11, color: 'CCD6F6', fontFace: 'Roboto' } }
    ], { x: phaseTextX, y: currentY, w: phaseTextW, h: rowHeight });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: barAreaX,
        y: currentY + 0.1,
        w: barAreaW,
        h: 0.4,
        fill: { color: '64FFDA', transparency: 50 },
        line: { color: '64FFDA', width: 0 }
    });
    slide.addText("12 Months", { x: barAreaX, y: currentY + 0.1, w: barAreaW, h: 0.4, align: 'center', color: '0A192F', bold: true, fontSize: 11, fontFace: 'Roboto' });
    
    // Phase 5: Data Security & Monitoring
    currentY += rowHeight;
    slide.addText([
        { text: 'Phase 5: Data Security & Monitoring', options: { bold: true, fontSize: 14, color: 'A8B2D1', fontFace: 'Roboto' } },
        { text: '\nImplement DLP, encrypt data, and continuously monitor environment.', options: { fontSize: 11, color: 'CCD6F6', fontFace: 'Roboto' } }
    ], { x: phaseTextX, y: currentY, w: phaseTextW, h: rowHeight });
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: barAreaX,
        y: currentY + 0.1,
        w: barAreaW,
        h: 0.4,
        fill: { color: '64FFDA', transparency: 60 },
        line: { color: '64FFDA', width: 0 }
    });
    // The animated SVG icon is not supported. A static text is used.
    slide.addText("Ongoing", { x: barAreaX + 0.2, y: currentY + 0.1, w: barAreaW, h: 0.4, align: 'left', color: '0A192F', bold: true, fontSize: 11, fontFace: 'Roboto' });


    // Phase 5: Error Prevention & Filename Requirements
    // Save the presentation with the specified dynamic filename.
    return pptx.writeFile({ fileName: 'generated_presentations/slide_7_general.pptx' });
}

// To run this function, you would call it in your script:
// createPresentation().then(fileName => {
//     console.log(`Presentation created: ${fileName}`);
// }).catch(err => {
//     console.error(err);
// });

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
