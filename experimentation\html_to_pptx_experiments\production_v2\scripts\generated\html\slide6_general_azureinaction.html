<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Azure in Action: Transforming Businesses</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;700&display=swap');
    html, body { height: 100%; margin: 0; }
    body {
      background-color: #f0f2f5;
      font-family: 'Segoe UI','Roboto','Helvetica Neue',Arial,sans-serif;
    }
    .slide-content { /* main content area */ }
  </style>
</head>
<body class="flex items-center justify-center">
  <div class="w-[1280px] h-[720px] bg-white shadow-2xl overflow-hidden flex flex-col">
    <!-- Header (consistent) -->
    <div class="flex items-center px-10 bg-[#0078D4] text-white" style="height:72px;">
      <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/azure-icon.svg" alt="Azure Icon" class="w-8 h-8 mr-4" />
      <div class="font-bold text-[1.2em]">Microsoft Azure</div>
    </div>

    <!-- Content -->
    <div class="slide-content flex-1 px-10 py-4 box-border overflow-hidden">
      <!-- Title aligned top-left -->
      <h1 class="text-[2.4em] leading-tight font-bold text-[#005A9E] mb-3">
        Azure in Action: Transforming Businesses
      </h1>

      <!-- Two-column layout -->
      <div class="grid grid-cols-2 gap-6">
        <!-- Left Column -->
        <div class="flex flex-col space-y-4">
          <!-- Use Cases -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="text-[#0078D4] font-bold text-[1.3em] mb-2">Use Cases</div>
            <ul class="list-disc pl-5 space-y-2 text-[1.2em] text-gray-700">
              <li>
                <span class="font-semibold">Web Application Hosting:</span>
                Scalable, reliable apps with Azure App Service + Azure SQL Database.
              </li>
              <li>
                <span class="font-semibold">Data Analytics and AI/ML:</span>
                Big data and ML with Azure Synapse Analytics, Azure Machine Learning, and Cognitive Services.
              </li>
              <li>
                <span class="font-semibold">Disaster Recovery:</span>
                Business continuity via Azure Site Recovery and Azure Backup.
              </li>
              <!-- Optional fourth if needed:
              <li><span class="font-semibold">IoT Solutions:</span> Secure device connectivity and management using IoT Hub and IoT Central.</li>
              -->
            </ul>
          </div>

          <!-- Benefits -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="text-[#0078D4] font-bold text-[1.3em] mb-2">Benefits (illustrative)</div>
            <ul class="list-disc pl-5 space-y-2 text-[1.2em] text-gray-700">
              <li>
                <span class="font-semibold">Cost Savings:</span>
                Reduce infra TCO with pay‑as‑you‑go and right‑sizing; eliminate hardware refresh cycles.
              </li>
              <li>
                <span class="font-semibold">Increased Agility:</span>
                Faster time‑to‑market with managed services and integrated DevOps.
              </li>
              <li>
                <span class="font-semibold">Improved Scalability:</span>
                Auto‑scale to handle peak loads without over‑provisioning.
              </li>
              <li>
                <span class="font-semibold">Enhanced Security:</span>
                Built‑in security controls and compliance certifications.
              </li>
            </ul>
          </div>
        </div>

        <!-- Right Column -->
        <div class="flex flex-col space-y-4">
          <!-- Customer Success Story -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="text-[#0078D4] font-bold text-[1.3em]">Customer Success: Contoso Retail</div>
              <span class="text-xs text-gray-500">Illustrative example</span>
            </div>
            <p class="text-[1.2em] text-gray-700 mb-3">
              “Azure enabled us to scale seamlessly during seasonal peaks and cut costs while improving reliability.”
            </p>
            <!-- KPI badges -->
            <div class="grid grid-cols-3 gap-3 mb-3">
              <div class="border border-gray-200 rounded p-2 bg-gray-50 text-center">
                <div class="text-[#005A9E] font-bold text-[1.2em]">40%</div>
                <div class="text-gray-700 text-[1.0em]">Lower TCO</div>
              </div>
              <div class="border border-gray-200 rounded p-2 bg-gray-50 text-center">
                <div class="text-[#005A9E] font-bold text-[1.2em]">3x</div>
                <div class="text-gray-700 text-[1.0em]">Faster Releases</div>
              </div>
              <div class="border border-gray-200 rounded p-2 bg-gray-50 text-center">
                <div class="text-[#005A9E] font-bold text-[1.2em]">99.95%</div>
                <div class="text-gray-700 text-[1.0em]">Uptime</div>
              </div>
            </div>
            <!-- Simple Cost Bar Comparison -->
            <div class="bg-white border border-gray-200 rounded p-3">
              <div class="flex items-center justify-between mb-2">
                <div class="font-semibold text-[#005A9E] text-[1.1em]">TCO Comparison</div>
                <div class="text-gray-500 text-xs">Lower is better</div>
              </div>
              <div class="space-y-2">
                <div class="flex items-center space-x-3">
                  <div class="w-28 text-[1.0em] text-gray-700">On‑Prem</div>
                  <div class="flex-1 h-5 bg-gray-200 rounded">
                    <div class="h-5 rounded bg-gray-500" style="width:100%;"></div>
                  </div>
                </div>
                <div class="flex items-center space-x-3">
                  <div class="w-28 text-[1.0em] text-gray-700">Azure</div>
                  <div class="flex-1 h-5 bg-gray-200 rounded">
                    <div class="h-5 rounded" style="width:60%; background-color:#0078D4;"></div>
                  </div>
                </div>
              </div>
              <div class="mt-2 text-xs text-gray-500">Example scenario; results vary by workload and adoption approach.</div>
            </div>
          </div>

          <!-- Cost Savings Table -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="text-[#0078D4] font-bold text-[1.3em] mb-3">Potential Cost Savings with Azure</div>
            <div class="overflow-hidden rounded border border-gray-100">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="text-left px-4 py-2 text-gray-700 font-semibold text-[1.1em]">Feature</th>
                    <th class="text-center px-4 py-2 text-gray-700 font-semibold text-[1.1em]">On-Premises</th>
                    <th class="text-center px-4 py-2 text-gray-700 font-semibold text-[1.1em]">Azure</th>
                    <th class="text-center px-4 py-2 text-gray-700 font-semibold text-[1.1em]">Savings</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-100 text-[1.1em]">
                  <tr>
                    <td class="px-4 py-2 text-gray-800">Server Hardware</td>
                    <td class="px-4 py-2 text-center text-gray-800">$50,000</td>
                    <td class="px-4 py-2 text-center text-gray-800">$0</td>
                    <td class="px-4 py-2 text-center text-gray-800">$50,000</td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 text-gray-800">Power &amp; Cooling</td>
                    <td class="px-4 py-2 text-center text-gray-800">$5,000/year</td>
                    <td class="px-4 py-2 text-center text-gray-800">$0</td>
                    <td class="px-4 py-2 text-center text-gray-800">$5,000/year</td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 text-gray-800">IT Staff</td>
                    <td class="px-4 py-2 text-center text-gray-800">$80,000/year</td>
                    <td class="px-4 py-2 text-center text-gray-800">$40,000/year</td>
                    <td class="px-4 py-2 text-center text-gray-800">$40,000/year</td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2 text-gray-800">Software Licenses</td>
                    <td class="px-4 py-2 text-center text-gray-800">$10,000/year</td>
                    <td class="px-4 py-2 text-center text-gray-800">Included</td>
                    <td class="px-4 py-2 text-center text-gray-800">$10,000/year</td>
                  </tr>
                  <tr class="bg-gray-50 font-semibold">
                    <td class="px-4 py-2 text-gray-900">Total Annual Cost</td>
                    <td class="px-4 py-2 text-center text-gray-900">$145,000</td>
                    <td class="px-4 py-2 text-center text-gray-900">$40,000</td>
                    <td class="px-4 py-2 text-center text-gray-900">$105,000</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              Figures are illustrative and depend on workload profile, reserved instances, and optimization practices.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>