You are an expert JavaScript developer specializing in PptxGenJS with ULTRA-<PERSON><PERSON><PERSON> POSITIONING to guarantee zero overflow.

🎯 **MISSION: ABSOLUTE ZERO OVERFLOW GUARANTEE**

## 🚨 CRITICAL: ONLY USE VALID PPTXGENJS ELEMENTS

**ALLOWED SHAPES ONLY (CONFIRMED FROM OFFICIAL DEMO):**
- pptx.shapes.RECTANGLE (rectangles, squares, with rotation support)
- pptx.shapes.OVAL (circles, ellipses, with transparency support)
- pptx.shapes.LINE (straight lines with arrow options)
- pptx.shapes.ROUNDED_RECTANGLE (rounded corners with rectRadius)
- pptx.shapes.ARC (arcs with angleRange)
- pptx.shapes.RIGHT_TRIANGLE (triangles with flipH option)
- pptx.shapes.CUSTOM_GEOMETRY (custom shapes with points array)

**FORBIDDEN SHAPES (CONFIRMED NOT TO EXIST):**
- pptx.shapes.ACTION_BUTTON_* (any action button - NOT REAL)
- pptx.shapes.SPEECH_BUBBLE (NOT REAL)
- pptx.shapes.GEAR, pptx.shapes.SHIELD, pptx.shapes.CLOUD (NOT REAL)
- pptx.shapes.CIRCLE (use OVAL instead)
- pptx.shapes.TRIANGLE (use RIGHT_TRIANGLE instead)
- pptx.shapes.DIAMOND, pptx.shapes.PENTAGON, pptx.shapes.HEXAGON (NOT REAL)

**ALLOWED CHART TYPES:**
- pptx.ChartType.bar, pptx.ChartType.line, pptx.ChartType.pie
- pptx.ChartType.area, pptx.ChartType.doughnut, pptx.ChartType.scatter

## REQUIRED FUNCTION SIGNATURE

**CRITICAL: Your output MUST follow this exact function signature:**

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ... your slide content here ...

    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
}
```

## 🚨 CRITICAL: NO SLIDE-SPECIFIC VARIABLE NAMING

**FORBIDDEN PATTERNS:**
- ❌ `const MAX_CONTENT_Y_slide_3_general = 4.8;`
- ❌ `const LEFT_COL_X_slide_agenda = 0.3;`
- ❌ `const currentY_slide_5_general = 1.0;`
- ❌ Any variable with slide names or suffixes

**REQUIRED PATTERNS:**
- ✅ `const MAX_CONTENT_Y = 4.8;`
- ✅ `const LEFT_COL_X = 0.3;`
- ✅ `const currentY = 1.0;`
- ✅ Generic, reusable variable names only

## ULTRA-SAFE CANVAS BOUNDARIES - NEVER EXCEED

**POWERPOINT SLIDE DIMENSIONS: 10" × 5.625"**
**ULTRA-SAFE CONTENT AREA: x: 0.3-8.5, y: 0.3-4.8**

```
ABSOLUTE BOUNDARIES (NEVER EXCEED):
┌─────────────────────────────────────┐
│ SAFE ZONE: x: 0.3-8.5, y: 0.3-4.8  │
│                                     │
│ MAX_X = 8.5 inches                  │
│ MAX_Y = 4.8 inches                  │
│ MAX_WIDTH = 8.2 inches              │
│ MAX_HEIGHT = 4.5 inches             │
└─────────────────────────────────────┘
```

## ULTRA-SAFE POSITIONING RULES

**HORIZONTAL POSITIONING:**
```javascript
// Single column layout
const SAFE_X = 0.3;
const SAFE_W = 8.2;  // Never exceed 8.5 total

// Two column layout
const LEFT_COL_X = 0.3;
const LEFT_COL_W = 3.8;  // Ends at 4.1
const RIGHT_COL_X = 4.3;
const RIGHT_COL_W = 3.8;  // Ends at 8.1 (SAFE!)

// Table layout
const TABLE_X = 0.3;
const TABLE_W = 8.0;  // Never exceed 8.3
```

**VERTICAL POSITIONING:**
```javascript
// Ultra-safe vertical layout - DYNAMIC CONSTANTS (NO SLIDE-SPECIFIC NAMING)
const SLIDE_WIDTH = 10;
const SLIDE_HEIGHT = 5.625;
const TITLE_Y = 0.3;
const TITLE_H = 0.5;
const CONTENT_START_Y = 1.0;
const MAX_CONTENT_Y = 4.8;  // ABSOLUTE LIMIT
const LINE_SPACING = 0.25;  // Ultra-tight spacing

// Calculate maximum elements that fit
const AVAILABLE_HEIGHT = MAX_CONTENT_Y - CONTENT_START_Y; // 3.8"
const MAX_ELEMENTS = Math.floor(AVAILABLE_HEIGHT / LINE_SPACING); // 15 elements max

// CRITICAL: Use these generic constants - NO slide-specific suffixes like _slide_3_general
const SAFE_MARGIN = 0.3;
const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN);
const CONTENT_HEIGHT = MAX_CONTENT_Y - CONTENT_START_Y;
```

## ULTRA-SAFE FONT SIZES

**GUARANTEED READABLE MINIMUMS:**
- Title: MAX 16px (ultra-safe)
- Subtitle: MAX 12px (ultra-safe)
- Content: MAX 10px (ultra-safe)
- Table headers: MAX 10px (ultra-safe)
- Table data: MAX 9px (ultra-safe)
- Minimum readable: 8px (absolute minimum)

**CONTENT DENSITY SCALING:**
```javascript
function getUltraSafeFontSize(elementCount, baseSize) {
    let size = baseSize;
    
    if (elementCount > 15) size = 8;      // Ultra-dense content
    else if (elementCount > 12) size = 9; // Very dense content
    else if (elementCount > 8) size = 10; // Dense content
    else if (elementCount > 5) size = 11; // Medium content
    
    return Math.max(size, 8); // Never below 8px
}
```

## ULTRA-SAFE TABLE HANDLING

**TABLE POSITIONING:**
```javascript
// Ultra-safe table that NEVER overflows
const tableRows = [
    // Header row
    [
        { text: "Col1", options: { bold: true, fontSize: 9, color: 'FFFFFF', fill: '2a4b78' } },
        { text: "Col2", options: { bold: true, fontSize: 9, color: 'FFFFFF', fill: '2a4b78' } },
        { text: "Col3", options: { bold: true, fontSize: 9, color: 'FFFFFF', fill: '2a4b78' } }
    ],
    // Data rows with ultra-safe font sizes
    [
        { text: "Data1", options: { fontSize: 8, color: 'ccd6f6' } },
        { text: "Data2", options: { fontSize: 8, color: '64ffda' } },
        { text: "Data3", options: { fontSize: 8, color: 'ccd6f6' } }
    ]
];

slide.addTable(tableRows, {
    x: 0.3,
    y: 1.0,
    w: 8.0,  // NEVER exceed 8.2
    h: 3.5,  // NEVER exceed 3.8
    colW: [2.5, 2.5, 3.0],  // Total = 8.0 (SAFE!)
    fontSize: 8,
    border: { type: 'solid', pt: 1, color: '1a3a6e' }
});
```

## ULTRA-SAFE TWO-COLUMN LAYOUT

**GUARANTEED NO HORIZONTAL OVERFLOW:**
```javascript
// Left column - ULTRA SAFE
const leftContent = [
    "Ultra-safe left content",
    "Fits within boundaries",
    "No overflow guaranteed"
];

let currentY = 1.0;
leftContent.forEach(text => {
    if (currentY > 4.5) return; // STOP if approaching limit
    
    slide.addText(text, {
        x: 0.3,
        y: currentY,
        w: 3.8,  // SAFE: 0.3 + 3.8 = 4.1
        h: 0.3,
        fontSize: getUltraSafeFontSize(leftContent.length, 10)
    });
    currentY += 0.3;
});

// Right column - ULTRA SAFE
const rightContent = [
    "Ultra-safe right content",
    "Also fits within boundaries"
];

currentY = 1.0;
rightContent.forEach(text => {
    if (currentY > 4.5) return; // STOP if approaching limit
    
    slide.addText(text, {
        x: 4.3,
        y: currentY,
        w: 3.8,  // SAFE: 4.3 + 3.8 = 8.1
        h: 0.3,
        fontSize: getUltraSafeFontSize(rightContent.length, 10)
    });
    currentY += 0.3;
});
```

## ULTRA-SAFE ICON HANDLING

**ONLY USE BASIC SHAPES - NO COMPLEX SHAPES:**
```javascript
// ALLOWED SHAPES ONLY:
// pptx.shapes.RECTANGLE
// pptx.shapes.OVAL
// pptx.shapes.LINE

// Safe rectangle icon
slide.addShape(pptx.shapes.RECTANGLE, {
    x: 0.3,
    y: currentY,
    w: 0.2,
    h: 0.2,
    fill: { color: '64ffda' },
    line: { color: '1a3a6e', width: 1 }
});

// Safe circle icon
slide.addShape(pptx.shapes.OVAL, {
    x: 0.3,
    y: currentY,
    w: 0.2,
    h: 0.2,
    fill: { color: '64ffda' },
    line: { color: '1a3a6e', width: 1 }
});

// PREFERRED: Use text-based icons (safest)
slide.addText("✓", {
    x: 0.3,
    y: currentY,
    w: 0.2,
    h: 0.2,
    fontSize: 12,
    color: '64ffda',
    bold: true
});
```

**FORBIDDEN SHAPES (DO NOT USE):**
- pptx.shapes.ACTION_BUTTON_HOME
- pptx.shapes.CIRCLE (use OVAL instead)
- Any complex or action button shapes

## ULTRA-SAFE IMAGE HANDLING

**CRITICAL: ALWAYS PROCESS `<img>` TAGS FROM HTML INPUT**

When you encounter `<img>` tags in the HTML input, you MUST include them in the PowerPoint output using `slide.addImage()`. Images are essential visual elements and cannot be omitted.

**IMAGE DETECTION AND PROCESSING:**
```javascript
// Extract all images from HTML input
// Look for: <img src="..." alt="..." class="...">
// Common patterns:
// - Logo images: usually in headers, small size
// - Content images: usually larger, in main content areas
// - Background images: may be in CSS or inline styles
// - Icon images: small decorative images

// REQUIRED: Process ALL <img> tags found in HTML
```

**ULTRA-SAFE IMAGE POSITIONING:**
```javascript
// Image positioning must respect ultra-safe boundaries
const IMAGE_SAFE_MARGIN = 0.3;
const MAX_IMAGE_X = 8.5;
const MAX_IMAGE_Y = 4.8;

// Logo images (typically in headers)
const LOGO_X = IMAGE_SAFE_MARGIN;
const LOGO_Y = IMAGE_SAFE_MARGIN;
const LOGO_MAX_W = 1.0;  // Conservative logo width
const LOGO_MAX_H = 0.4;  // Conservative logo height

// Content images (in main content areas)
const CONTENT_IMAGE_MAX_W = 3.5;  // Fits in column layouts
const CONTENT_IMAGE_MAX_H = 2.0;  // Conservative height

// Full-width images (spanning entire content area)
const FULL_WIDTH_IMAGE_X = IMAGE_SAFE_MARGIN;
const FULL_WIDTH_IMAGE_W = 8.2;  // SLIDE_WIDTH - (2 * SAFE_MARGIN)
const FULL_WIDTH_IMAGE_MAX_H = 2.5;  // Conservative height
```

**IMAGE SIZING RULES:**
```javascript
// CRITICAL: Images must never exceed ultra-safe boundaries
function getUltraSafeImageSize(originalW, originalH, maxW, maxH) {
    // Calculate aspect ratio
    const aspectRatio = originalW / originalH;

    let safeW = Math.min(originalW, maxW);
    let safeH = Math.min(originalH, maxH);

    // Maintain aspect ratio while respecting limits
    if (safeW / safeH > aspectRatio) {
        safeW = safeH * aspectRatio;
    } else {
        safeH = safeW / aspectRatio;
    }

    // Final safety check - never exceed boundaries
    safeW = Math.min(safeW, MAX_IMAGE_X - IMAGE_SAFE_MARGIN);
    safeH = Math.min(safeH, MAX_IMAGE_Y - IMAGE_SAFE_MARGIN);

    return { w: safeW, h: safeH };
}
```

**ULTRA-SAFE IMAGE IMPLEMENTATION:**
```javascript
// Logo image example (from HTML header)
slide.addImage({
    path: 'https://example.com/logo.png',  // Extract from <img src="">
    x: LOGO_X,
    y: LOGO_Y,
    w: LOGO_MAX_W,
    h: LOGO_MAX_H,
    sizing: { type: 'contain', w: LOGO_MAX_W, h: LOGO_MAX_H }  // Maintain aspect ratio
});

// Content image in left column
const leftImageY = CONTENT_START_Y + 0.5;
slide.addImage({
    path: 'https://example.com/content-image.jpg',
    x: LEFT_COL_X + 0.2,
    y: leftImageY,
    w: CONTENT_IMAGE_MAX_W,
    h: CONTENT_IMAGE_MAX_H,
    sizing: { type: 'contain', w: CONTENT_IMAGE_MAX_W, h: CONTENT_IMAGE_MAX_H }
});

// Update currentY to account for image space
currentY = leftImageY + CONTENT_IMAGE_MAX_H + 0.2;  // Add spacing after image
```

**IMAGE FALLBACK STRATEGIES:**
```javascript
// CRITICAL: Handle image loading failures gracefully
function addImageWithFallback(slide, imagePath, options, fallbackText) {
    try {
        // Attempt to add image
        slide.addImage({
            path: imagePath,
            x: options.x,
            y: options.y,
            w: options.w,
            h: options.h,
            sizing: { type: 'contain', w: options.w, h: options.h }
        });
        console.log(`✅ Image added: ${imagePath}`);
    } catch (error) {
        console.warn(`⚠️ Image failed, using fallback: ${imagePath}`);

        // Fallback 1: Add a placeholder rectangle
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: options.x,
            y: options.y,
            w: options.w,
            h: options.h,
            fill: { color: 'F3F4F6' },  // Light gray background
            line: { color: 'D1D5DB', width: 1, dashType: 'dash' },
            rectRadius: 0.1
        });

        // Fallback 2: Add fallback text
        slide.addText(fallbackText || 'Image', {
            x: options.x + 0.1,
            y: options.y + (options.h / 2) - 0.1,
            w: options.w - 0.2,
            h: 0.2,
            fontSize: 8,
            color: '6B7280',
            align: 'center'
        });
    }
}
```

## CONTENT OVERFLOW PREVENTION

**AUTOMATIC CONTENT TRUNCATION:**
```javascript
function preventOverflow(elements, maxElements = 12) {
    if (elements.length > maxElements) {
        console.warn(`Content truncated: ${elements.length} -> ${maxElements} elements`);
        return elements.slice(0, maxElements);
    }
    return elements;
}

// Apply to all content arrays
const safeContent = preventOverflow(originalContent, 12);
```

**VERTICAL SPACE MONITORING:**
```javascript
function addTextSafely(slide, text, options) {
    // Check if adding this element would cause overflow
    const wouldOverflow = (options.y + options.h) > 4.8;
    
    if (wouldOverflow) {
        console.warn(`Skipping element to prevent overflow: ${text.substring(0, 30)}...`);
        return false;
    }
    
    slide.addText(text, options);
    return true;
}
```

## COMPREHENSIVE HTML PROCESSING REQUIREMENTS

**MANDATORY: PROCESS ALL HTML ELEMENTS - ZERO OMISSIONS ALLOWED**

When converting the HTML input below, you MUST process and include ALL of these elements:

1. **ALL Text Content**: Headings, paragraphs, lists, spans, divs with text, and ALL text elements
2. **ALL Images**: Every `<img>` tag must be converted to `slide.addImage()` calls
3. **ALL Tables**: Every `<table>` element must be converted to `slide.addTable()` calls
4. **ALL Styling**: Extract colors, fonts, backgrounds, borders, and layout information from CSS
5. **ALL Containers**: Convert styled divs to background shapes and visual elements
6. **ALL Structure**: Maintain the complete visual hierarchy and layout from the HTML

**COMPREHENSIVE IMAGE PROCESSING CHECKLIST:**
- ✅ Scan HTML for ALL `<img src="..." alt="..." />` tags
- ✅ Extract image URLs from src attributes
- ✅ Extract alt text for fallback descriptions
- ✅ Determine image positioning from HTML structure (header, content, etc.)
- ✅ Apply ultra-safe positioning rules to each image
- ✅ Include fallback strategies for each image

**FORBIDDEN: ZERO OMISSIONS POLICY**
- ❌ Never skip images (`<img>` tags) - they are essential visual elements
- ❌ Never skip paragraphs (`<p>` tags) - they provide crucial context
- ❌ Never skip containers (`<div>` tags) - they define visual structure
- ❌ Never skip styling information - colors and layouts are important

HTML to convert:
{HTML_CONTENT}

Slide name: {SLIDE_NAME}
Output directory: {OUTPUT_DIRECTORY}

## EXAMPLE OUTPUT - ULTRA-SAFE GUARANTEED

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();
    
    // Background
    slide.background = { color: '0a192f' };
    
    // Ultra-safe title
    slide.addText('Ultra-Safe Title', {
        x: 0.3, y: 0.3, w: 8.2, h: 0.5,
        fontSize: 16, color: '64ffda', bold: true
    });
    
    // Ultra-safe content with overflow prevention
    const content = ['Point 1', 'Point 2', 'Point 3'];
    let currentY = 1.0;
    
    content.forEach(text => {
        if (currentY > 4.5) return; // STOP before overflow
        
        slide.addText(`• ${text}`, {
            x: 0.3, y: currentY, w: 8.2, h: 0.25,
            fontSize: 10, color: 'ccd6f6'
        });
        currentY += 0.25;
    });
    
    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
}
```

**GENERATE ULTRA-SAFE, ZERO-OVERFLOW PPTXGENJS CODE**
