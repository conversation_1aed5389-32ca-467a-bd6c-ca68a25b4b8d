<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Building a Secure Foundation with Cloud Technologies</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f2f5;
            font-family: 'Roboto', sans-serif;
        }

        .slide-container {
            width: 1280px;
            height: 720px;
            background-color: #0a192f;
            background-image:
                linear-gradient(rgba(10, 25, 47, 0.85), rgba(10, 25, 47, 0.85)),
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a3a6e' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            color: #e6f1ff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 40px 60px;
            box-sizing: border-box;
        }

        .slide-content {
            flex-grow: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            width: 100%;
            padding-top: 0;
        }

        .slide-content h1 {
            font-size: 2.8em;
            color: #64ffda;
            font-weight: 700;
            margin: 0 0 10px 0;
            line-height: 1.2;
        }
        
        .title-divider {
            width: 120px;
            height: 3px;
            background-color: #64ffda;
            margin-bottom: 30px;
        }

        .main-layout {
            display: flex;
            width: 100%;
            height: calc(100% - 100px); /* Adjust height based on title area */
            gap: 40px;
        }

        .diagram-column {
            flex: 0 0 45%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .text-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 18px;
        }

        .architecture-layer {
            background-color: rgba(42, 67, 101, 0.5);
            border: 1px solid #1d3b66;
            border-radius: 8px;
            padding: 8px 15px;
            margin-bottom: 8px;
            width: 90%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .architecture-layer:last-child {
            margin-bottom: 0;
        }

        .layer-text {
            color: #ccd6f6;
            font-size: 0.9em;
            font-weight: 400;
        }

        .layer-text strong {
            color: #a8b2d1;
            font-weight: 700;
        }

        .layer-icons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .layer-icons img, .tech-logo {
            height: 24px;
            width: 24px;
            object-fit: contain;
        }
        
        .lock-icon {
             filter: invert(84%) sepia(29%) saturate(399%) hue-rotate(99deg) brightness(104%) contrast(101%); /* #64ffda */
        }

        .tech-item {
            display: flex;
            flex-direction: column;
        }

        .tech-item h3 {
            font-size: 1.1em;
            color: #64ffda;
            margin: 0 0 5px 0;
            font-weight: 700;
        }

        .tech-details {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #a8b2d1;
            font-size: 0.9em;
            flex-wrap: wrap;
        }
        
        .tech-details .tech-logo {
            height: 20px;
            width: 20px;
            border-radius: 3px;
        }

    </style>
</head>
<body>

    <div class="slide-container">
        <div class="slide-content">
            <h1>Building a Secure Foundation with Cloud Technologies</h1>
            <div class="title-divider"></div>
            <div class="main-layout">
                <div class="diagram-column">
                    <!-- Layers from top to bottom in code, but visually stacked -->
                    <div class="architecture-layer">
                        <span class="layer-text"><strong>Identity:</strong> Azure AD Conditional Access</span>
                        <div class="layer-icons">
                            <img src="https://www.svgrepo.com/show/474241/lock-circle.svg" alt="Lock" class="lock-icon">
                        </div>
                    </div>
                    <div class="architecture-layer">
                        <span class="layer-text"><strong>Application:</strong> Web Apps / Microservices</span>
                        <div class="layer-icons">
                            <img src="https://www.svgrepo.com/show/474241/lock-circle.svg" alt="Lock" class="lock-icon">
                        </div>
                    </div>
                    <div class="architecture-layer">
                        <span class="layer-text"><strong>Data:</strong> S3 Buckets / Azure Blob Storage</span>
                        <div class="layer-icons">
                            <img src="https://www.svgrepo.com/show/474241/lock-circle.svg" alt="Lock" class="lock-icon">
                        </div>
                    </div>
                    <div class="architecture-layer">
                        <span class="layer-text"><strong>Compute:</strong> EC2 Instances / Azure VMs</span>
                        <div class="layer-icons">
                            <img src="https://www.svgrepo.com/show/474241/lock-circle.svg" alt="Lock" class="lock-icon">
                        </div>
                    </div>
                    <div class="architecture-layer">
                        <span class="layer-text"><strong>Network:</strong> Azure Virtual Network / NSGs</span>
                        <div class="layer-icons">
                            <img src="https://www.svgrepo.com/show/474241/lock-circle.svg" alt="Lock" class="lock-icon">
                        </div>
                    </div>
                    <div class="architecture-layer">
                        <span class="layer-text"><strong>Infrastructure:</strong> Cloud Provider</span>
                        <div class="layer-icons">
                            <img src="https://www.svgrepo.com/show/376356/aws.svg" alt="AWS">
                            <img src="https://www.svgrepo.com/show/331732/microsoft-azure.svg" alt="Azure">
                            <img src="https://www.svgrepo.com/show/444194/brand-google-cloud.svg" alt="GCP">
                            <img src="https://www.svgrepo.com/show/474241/lock-circle.svg" alt="Lock" class="lock-icon">
                        </div>
                    </div>
                </div>
                <div class="text-column">
                    <div class="tech-item">
                        <h3>Identity & Access Management (IAM)</h3>
                        <div class="tech-details">
                            <img src="https://svgl.app/library/azure.svg" alt="Azure" class="tech-logo"> Azure AD Conditional Access + Passwordless Auth + Behavioral Biometrics
                        </div>
                    </div>
                    <div class="tech-item">
                        <h3>Device Security & Endpoint Management</h3>
                        <div class="tech-details">
                            <img src="https://www.svgrepo.com/show/452062/microsoft.svg" alt="Microsoft" class="tech-logo"> Microsoft Endpoint Manager (Intune) + DPA + 
                            <img src="https://www.svgrepo.com/show/349320/cloudflare.svg" alt="Cloudflare" class="tech-logo"> Remote Browser Isolation
                        </div>
                    </div>
                    <div class="tech-item">
                        <h3>Network Security & Microsegmentation</h3>
                        <div class="tech-details">
                             <img src="https://svgl.app/library/azure.svg" alt="Azure" class="tech-logo"> Azure VNets/NSGs + 
                             <img src="https://www.svgrepo.com/show/349320/cloudflare.svg" alt="Cloudflare" class="tech-logo"> Cloudflare ZTNA + AI Anomaly Detection
                        </div>
                    </div>
                    <div class="tech-item">
                        <h3>Data Security & Protection</h3>
                        <div class="tech-details">
                            <img src="https://www.svgrepo.com/show/452062/microsoft.svg" alt="Microsoft" class="tech-logo"> Microsoft Purview + Cloud-Native Key Management (KMS)
                        </div>
                    </div>
                    <div class="tech-item">
                        <h3>Automation & Orchestration</h3>
                        <div class="tech-details">
                            <img src="https://svgl.app/library/azure.svg" alt="Azure" class="tech-logo"> Azure Logic Apps +
                            <img src="https://www.svgrepo.com/show/353399/ansible.svg" alt="Ansible" class="tech-logo"> Ansible /
                            <img src="https://www.svgrepo.com/show/376353/terraform.svg" alt="Terraform" class="tech-logo"> Terraform (IaC)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>