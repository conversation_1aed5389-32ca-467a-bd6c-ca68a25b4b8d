const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ==========================================================================
    // ULTRA-SAFE POSITIONING & STYLING CONSTANTS
    // ==========================================================================

    // Overall Slide & Content Area Boundaries
    const SAFE_MARGIN = 0.3;
    const SLIDE_WIDTH = 10.0;
    const SLIDE_HEIGHT = 5.625;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4
    const MAX_CONTENT_Y = 4.8;

    // Title Area
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;

    // Main Content Area
    const CONTENT_START_Y = TITLE_Y + TITLE_H + 0.2; // 1.0

    // Two-Column Layout (approximating 3/5 and 2/5 split)
    const LEFT_COL_X = SAFE_MARGIN; // 0.3
    const LEFT_COL_W = 5.2;
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.2; // 5.7
    const RIGHT_COL_W = 3.7;
    const CONTENT_AREA_H = MAX_CONTENT_Y - CONTENT_START_Y; // 3.8

    // Color Palette (extracted from Tailwind CSS)
    const COLORS = {
        GRAY_800: '1F2937',
        GRAY_600: '4B5563',
        GRAY_500: '6B7280',
        GRAY_200: 'E5E7EB',
        TEAL_800: '0D9488', // Adjusted from text-teal-800
        TEAL_700: '0F766E',
        TEAL_200: '99F6E4',
        GREEN_50: 'F0FDF4',
        GREEN_100: 'DCFCE7',
        GREEN_200: 'BBF7D0',
        GREEN_300: '86EFAC',
        GREEN_500: '22C55E',
        GREEN_700: '15803D',
        WHITE: 'FFFFFF',
        BLACK_ALPHA_60: '99000000' // Black with 60% alpha
    };

    // ==========================================================================
    // ULTRA-SAFE HELPER FUNCTIONS
    // ==========================================================================

    /**
     * Adds an image with a graceful fallback to a placeholder if the image fails.
     * @param {PptxGenJS.Slide} slide - The slide object.
     * @param {string} imagePath - The URL or base64 data of the image.
     * @param {object} options - { x, y, w, h }.
     * @param {string} fallbackText - Text to display in the placeholder.
     */
    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'cover', w: options.w, h: options.h }
            });
        } catch (error) {
            console.warn(`⚠️ Image failed to load, using fallback: ${imagePath}`);
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' },
                line: { color: 'D1D5DB', width: 1, dashType: 'dash' },
                rectRadius: 0.1
            });
            slide.addText(fallbackText, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fontSize: 8, color: '6B7280', align: 'center', valign: 'middle'
            });
        }
    }

    // ==========================================================================
    // SLIDE CONTENT CONSTRUCTION
    // ==========================================================================

    // 1. Slide Title (from <h1>)
    slide.addText('Regional Market Dynamics: A Global Perspective', {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: CONTENT_WIDTH,
        h: TITLE_H,
        fontSize: 16,
        color: COLORS.GRAY_800,
        bold: true,
        valign: 'middle'
    });

    // 2. Left Column: World Map & Legend
    const mapContainerH = CONTENT_AREA_H - 0.6; // Reserve space for legend
    const mapImageURL = 'https://img.freepik.com/premium-photo/world-map-visualization-showing-global-data-trends-with-colorcoded-regions_1314467-175172.jpg?w=740';

    // Add the map image as a background shape
    addImageWithFallback(slide, mapImageURL, {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        h: mapContainerH
    }, 'World Map Image');

    // Add region labels over the map (approximating positions)
    const regionLabels = [
        { text: 'Asia', x: LEFT_COL_X + 3.4, y: CONTENT_START_Y + 0.8 },
        { text: 'Europe', x: LEFT_COL_X + 2.3, y: CONTENT_START_Y + 1.0 },
        { text: 'North America', x: LEFT_COL_X + 0.8, y: CONTENT_START_Y + 1.2 },
        { text: 'South America', x: LEFT_COL_X + 1.3, y: CONTENT_START_Y + 2.0 },
        { text: 'Africa', x: LEFT_COL_X + 2.6, y: CONTENT_START_Y + 2.2 }
    ];

    regionLabels.forEach(label => {
        // Background for the label
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: label.x, y: label.y, w: 1.0, h: 0.25,
            fill: { color: COLORS.BLACK_ALPHA_60 },
            rectRadius: 0.05
        });
        // Text for the label
        slide.addText(label.text, {
            x: label.x, y: label.y, w: 1.0, h: 0.25,
            color: COLORS.WHITE, fontSize: 8, bold: true, align: 'center', valign: 'middle'
        });
    });

    // Add the capacity legend below the map
    let legendCurrentX = LEFT_COL_X;
    const legendY = CONTENT_START_Y + mapContainerH + 0.15;
    const legendItemH = 0.2;

    slide.addText('Capacity:', {
        x: legendCurrentX, y: legendY, w: 0.6, h: legendItemH,
        fontSize: 9, color: COLORS.GRAY_600, bold: true, valign: 'middle'
    });
    legendCurrentX += 0.65;

    slide.addText('Low', {
        x: legendCurrentX + 0.7, y: legendY, w: 0.4, h: legendItemH,
        fontSize: 8, color: COLORS.GRAY_500, valign: 'middle'
    });
    legendCurrentX += 1.1;

    const legendColors = [COLORS.GREEN_100, COLORS.GREEN_300, COLORS.GREEN_500, COLORS.GREEN_700];
    legendColors.forEach(color => {
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: legendCurrentX, y: legendY, w: 0.6, h: legendItemH,
            fill: { color: color },
            line: { color: COLORS.GRAY_200, width: 1 },
            rectRadius: 0.05
        });
        legendCurrentX += 0.65;
    });

    slide.addText('High', {
        x: legendCurrentX, y: legendY, w: 0.4, h: legendItemH,
        fontSize: 8, color: COLORS.GRAY_500, valign: 'middle'
    });

    // 3. Right Column: Regional Highlights
    let currentY = CONTENT_START_Y;

    // Column Title (from <h2>)
    slide.addText('Regional Highlights', {
        x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: 0.3,
        fontSize: 12, color: COLORS.TEAL_700, bold: true
    });
    // Bottom border for the title
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X, y: currentY + 0.3, w: RIGHT_COL_W, h: 0,
        line: { color: COLORS.TEAL_200, width: 2 }
    });
    currentY += 0.4;

    // Highlight items (from styled <div>s)
    const highlights = [
        { title: 'Asia (Leading)', text: 'Led by China and India, the region dominates global capacity, particularly in solar and wind installations.', borderColor: COLORS.GREEN_700 },
        { title: 'Europe & North America (Strong Growth)', text: 'Driven by strong policy (EU targets, US IRA), both regions show significant investment and deployment across multiple technologies.', borderColor: COLORS.GREEN_500 },
        { title: 'South America (Hydro Hub)', text: 'Brazil\'s leadership in hydropower and bioenergy anchors the continent\'s renewable profile.', borderColor: COLORS.GREEN_300 },
        { title: 'Africa (High Potential)', text: 'Possesses vast solar and wind potential but requires investment in infrastructure and financing to unlock it.', borderColor: COLORS.GREEN_200 }
    ];

    const itemSpacing = 0.15;
    const itemHeight = (CONTENT_AREA_H - (currentY - CONTENT_START_Y) - (highlights.length - 1) * itemSpacing - 0.3) / highlights.length;

    highlights.forEach(item => {
        // Container shape with background and left border
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: RIGHT_COL_X, y: currentY, w: RIGHT_COL_W, h: itemHeight,
            fill: { color: COLORS.GREEN_50 },
            rectRadius: 0.1
        });
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: RIGHT_COL_X, y: currentY, w: 0.05, h: itemHeight,
            fill: { color: item.borderColor }
        });

        // Content inside the container
        const textX = RIGHT_COL_X + 0.15;
        const textW = RIGHT_COL_W - 0.2;
        let textY = currentY + 0.05;

        // Title (from <h3>)
        slide.addText(item.title, {
            x: textX, y: textY, w: textW, h: 0.2,
            fontSize: 9, color: COLORS.TEAL_800, bold: true
        });
        textY += 0.2;

        // Description (from <p>)
        slide.addText(item.text, {
            x: textX, y: textY, w: textW, h: itemHeight - 0.3,
            fontSize: 8, color: COLORS.GRAY_600, wrap: true
        });

        currentY += itemHeight + itemSpacing;
    });

    // 4. Footer/Source Text
    slide.addText('Source: IEA Renewables 2024 Report', {
        x: RIGHT_COL_X,
        y: MAX_CONTENT_Y,
        w: RIGHT_COL_W,
        h: 0.2,
        fontSize: 8,
        color: COLORS.GRAY_500,
        align: 'right'
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide5_general_region_market.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
