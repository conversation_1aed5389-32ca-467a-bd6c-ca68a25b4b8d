const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ------------------- ULTRA-SAFE POSITIONING & STYLING CONSTANTS -------------------

    // Overall Slide Layout
    const SAFE_MARGIN = 0.5;
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.0
    const MAX_CONTENT_Y = 5.0;

    // Header Area
    const HEADER_Y = 0.3;
    const HEADER_H = 0.75;
    const LOGO_X = SAFE_MARGIN;
    const LOGO_W = 1.5;
    const LOGO_H = 0.4;
    const TITLE_X = LOGO_X + LOGO_W + 0.2;
    const TITLE_W = CONTENT_WIDTH - LOGO_W - 0.2;

    // Content Area
    const CONTENT_START_Y = HEADER_Y + HEADER_H + 0.2; // ~1.25
    const GAP = 0.6;
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = (CONTENT_WIDTH - GAP) / 2; // ~4.2
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + GAP; // ~5.3
    const RIGHT_COL_W = LEFT_COL_W; // ~4.2
    const CONTENT_H = MAX_CONTENT_Y - CONTENT_START_Y; // ~3.75

    // Footer Area
    const FOOTER_Y = 5.15;
    const FOOTER_H = 0.3;

    // ------------------- ULTRA-SAFE HELPER FUNCTIONS -------------------

    /**
     * Adds an image with a robust fallback to prevent errors and maintain layout.
     * @param {object} slide - The PptxGenJS slide object.
     * @param {string} imagePath - The URL or path of the image.
     * @param {object} options - { x, y, w, h } for positioning.
     * @param {string} fallbackText - Text to display if the image fails.
     */
    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({
                path: imagePath,
                x: options.x, y: options.y, w: options.w, h: options.h,
                sizing: { type: 'contain', w: options.w, h: options.h }
            });
        } catch (error) {
            console.warn(`⚠️ Image failed, using fallback: ${imagePath}`);
            slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fill: { color: 'F3F4F6' },
                line: { color: 'D1D5DB', width: 1 },
                rectRadius: 0.1
            });
            slide.addText(fallbackText, {
                x: options.x, y: options.y, w: options.w, h: options.h,
                fontSize: 8, color: '6B7280', align: 'center', valign: 'middle'
            });
        }
    }

    // ------------------- SLIDE CONSTRUCTION -------------------

    // 1. Footer Background (drawn first to be in the back)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0, y: FOOTER_Y, w: SLIDE_WIDTH, h: SLIDE_HEIGHT - FOOTER_Y,
        fill: { color: 'F3F4F6' } // bg-gray-100
    });

    // 2. Header Elements
    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_MARGIN, y: HEADER_Y + HEADER_H, w: CONTENT_WIDTH, h: 0,
        line: { color: 'E5E7EB', width: 1 } // border-b border-gray-200
    });

    addImageWithFallback(slide, 'https://download.logo.wine/logo/Google_Cloud_Platform/Google_Cloud_Platform-Logo.wine.png',
        { x: LOGO_X, y: HEADER_Y + 0.1, w: LOGO_W, h: LOGO_H }, 'GCP Logo');

    slide.addText('Conclusion: Unlock Your Potential with GCP', {
        x: TITLE_X, y: HEADER_Y, w: TITLE_W, h: HEADER_H,
        fontSize: 16, color: '374151', bold: true, valign: 'middle'
    });

    // 3. Left Column Content
    let currentY = CONTENT_START_Y;

    // Summary Section
    slide.addText('Summary: Your Strategic Advantage', {
        x: LEFT_COL_X, y: currentY, w: LEFT_COL_W, h: 0.3,
        fontSize: 12, color: '14213d', bold: true
    });
    slide.addShape(pptx.shapes.LINE, {
        x: LEFT_COL_X, y: currentY + 0.3, w: LEFT_COL_W * 0.6, h: 0,
        line: { color: 'fca311', width: 2 }
    });
    currentY += 0.5;

    const summaryPoints = [
        "GCP provides a powerful, secure, and versatile cloud platform ready to support businesses of all sizes and complexities.",
        "By leveraging GCP, you can significantly reduce infrastructure costs, dramatically improve operational agility, and drive continuous innovation."
    ];

    summaryPoints.forEach(point => {
        slide.addText('✓', { x: LEFT_COL_X, y: currentY, w: 0.25, h: 0.3, fontSize: 12, color: '4285F4', bold: true });
        slide.addText(point, { x: LEFT_COL_X + 0.3, y: currentY, w: LEFT_COL_W - 0.3, h: 0.6, fontSize: 9, color: '334155' });
        currentY += 0.8;
    });
    currentY += 0.2; // Extra space between sections

    // Next Steps Section
    slide.addText('Next Steps: Begin Your Transformation', {
        x: LEFT_COL_X, y: currentY, w: LEFT_COL_W, h: 0.3,
        fontSize: 12, color: '14213d', bold: true
    });
    slide.addShape(pptx.shapes.LINE, {
        x: LEFT_COL_X, y: currentY + 0.3, w: LEFT_COL_W * 0.6, h: 0,
        line: { color: 'fca311', width: 2 }
    });
    currentY += 0.5;

    const nextSteps = [
        "Schedule a consultation to discuss your specific needs.",
        "Request a tailored demo of key GCP services.",
        "Explore our portfolio of client case studies."
    ];

    nextSteps.forEach(step => {
        slide.addText('►', { x: LEFT_COL_X, y: currentY, w: 0.25, h: 0.3, fontSize: 12, color: '2563EB', bold: true });
        slide.addText(step, { x: LEFT_COL_X + 0.3, y: currentY, w: LEFT_COL_W - 0.3, h: 0.3, fontSize: 9, color: '374151' });
        currentY += 0.4;
    });

    // 4. Right Column Content (Call to Action)
    // Background container
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: RIGHT_COL_X, y: CONTENT_START_Y, w: RIGHT_COL_W, h: CONTENT_H,
        fill: { color: 'F9FAFB' }, // bg-gray-50
        line: { color: 'E5E7EB', width: 1 }, // border-gray-200
        rectRadius: 0.15 // rounded-lg
    });

    let rightY = CONTENT_START_Y + 0.2;
    const rightInnerX = RIGHT_COL_X + 0.3;
    const rightInnerW = RIGHT_COL_W - 0.6;

    addImageWithFallback(slide, 'https://www.svgrepo.com/show/494583/consulting-message.svg',
        { x: RIGHT_COL_X + (RIGHT_COL_W / 2) - 0.5, y: rightY, w: 1.0, h: 1.0 }, 'Consulting Icon');
    rightY += 1.2;

    slide.addText('Ready to Build the Future?', {
        x: rightInnerX, y: rightY, w: rightInnerW, h: 0.4,
        fontSize: 14, color: '1d3557', bold: true, align: 'center'
    });
    rightY += 0.4;

    slide.addText('Contact us today to learn more and start your journey to the cloud with a trusted partner.', {
        x: rightInnerX, y: rightY, w: rightInnerW, h: 0.5,
        fontSize: 9, color: '4B5563', align: 'center'
    });
    rightY += 0.7;

    // CTA Button (as a shape with text)
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: rightInnerX, y: rightY, w: rightInnerW, h: 0.5,
        fill: { color: '4285F4' },
        rectRadius: 0.1
    });
    slide.addText('Contact Us Today', {
        x: rightInnerX, y: rightY, w: rightInnerW, h: 0.5,
        fontSize: 11, color: 'FFFFFF', bold: true, align: 'center', valign: 'middle'
    });
    rightY += 0.7;

    // Contact Info Section
    slide.addShape(pptx.shapes.LINE, {
        x: rightInnerX, y: rightY, w: rightInnerW, h: 0,
        line: { color: 'D1D5DB', width: 1 } // border-t border-gray-300
    });
    rightY += 0.2;

    addImageWithFallback(slide, 'https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/google-cloud.svg',
        { x: RIGHT_COL_X + (RIGHT_COL_W / 2) - 0.4, y: rightY, w: 0.8, h: 0.4 }, 'Company Logo');
    rightY += 0.4;

    slide.addText('[Your Company Name]', { x: rightInnerX, y: rightY, w: rightInnerW, h: 0.2, fontSize: 9, color: '4B5563', bold: true, align: 'center' });
    rightY += 0.2;
    slide.addText('[Your Phone Number] | [Your Email Address]', { x: rightInnerX, y: rightY, w: rightInnerW, h: 0.2, fontSize: 8, color: '6B7280', align: 'center' });
    rightY += 0.2;
    slide.addText('[Your Website]', { x: rightInnerX, y: rightY, w: rightInnerW, h: 0.2, fontSize: 8, color: '6B7280', align: 'center' });

    // 5. Footer Text
    slide.addText('Unlocking Innovation with Google Cloud', {
        x: SAFE_MARGIN, y: FOOTER_Y, w: 4.0, h: FOOTER_H,
        fontSize: 8, color: '4B5563', bold: true, valign: 'middle'
    });
    slide.addText('Slide 7', {
        x: SLIDE_WIDTH - SAFE_MARGIN - 4.0, y: FOOTER_Y, w: 4.0, h: FOOTER_H,
        fontSize: 8, color: '4B5563', bold: true, valign: 'middle', align: 'right'
    });

    // ------------------- FILE GENERATION -------------------

    return pptx.writeFile({ fileName: 'generated/presentations/slide7_general_conclusion.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
