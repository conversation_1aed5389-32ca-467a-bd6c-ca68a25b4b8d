import textwrap


#######Daniel's Prompt#######
generationpy_slide_review_prompt_daniel_v1 = textwrap.dedent(
    """
        You are a senior front-end software engineer reviewing a junior engineer's work.
        He has written some HTML which is supposed to show one slide of a powerpoint.

        You have been provided with the HTML code and also a rendering of the code as an image.

        Look at the image then validate the following criteria.
        1. Make sure that text, visual elements and content blocks are completely contained within the slide and not cut off at the bottom of the slide. 
        If this criteria is not met, reduce the vertical padding/spacing between visual elements, titles, subtitles and content blocks OR reduce the font size of the text component to meet the criteria.
        You can reduce the padding by changing the padding or gap parameters, or the margin-bottom parameter of any titles.

        2. Make sure that visual elements do NOT overlap with each other e.g. the company logo overlaps with slide content.
        If anything is overlapping, MAKE SURE to reposition or adjust the size of the frontmost element.

        Do NOT make changes to the code if the above criteria is met.
        If code changes need to be made, only output the improved HTML code, do not output any other text.
        If the code meets all of the criteria, simply output <OK>.

        The HTML code is provided below:
        {code}
        """)


#######<PERSON><PERSON>'s Prompt#######


#######Hai's Prompt#######
