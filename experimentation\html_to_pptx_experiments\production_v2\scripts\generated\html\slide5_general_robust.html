<!DOCTYPE html>
<html>
<head>
<title>Robust and Scalable Technology Platform</title>
<script src="https://cdn.tailwindcss.com"></script>
<style>
    body, html {
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f0f0f0;
    }
    .slide-container {
        width: 1280px;
        height: 720px;
        background-color: #ffffff;
        margin: 20px auto;
        box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
        display: flex;
        flex-direction: column;
        position: relative;
    }
    .slide-content {
        flex-grow: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        text-align: left;
        width: 100%;
        padding: 40px 60px;
        box-sizing: border-box;
    }
    .title {
        font-size: 2.8em;
        color: #003366; /* Navy Blue */
        margin-bottom: 2rem;
        font-weight: 600;
        width: 100%;
    }
    .arrow {
        position: relative;
        width: 100%;
        height: 2px;
        background-color: #90a4ae;
        margin: 20px 0;
    }
    .arrow::after {
        content: '';
        position: absolute;
        right: -1px;
        top: -4px;
        border: solid #90a4ae;
        border-width: 0 2px 2px 0;
        display: inline-block;
        padding: 4px;
        transform: rotate(-45deg);
    }
</style>
</head>
<body>

<div class="slide-container">
    <div class="slide-content">
        <h1 class="title">Robust and Scalable Technology Platform</h1>
        <div class="flex w-full h-full">
            <div class="w-2/5 pr-8">
                <div>
                    <h2 class="text-2xl font-semibold text-[#005a9e] mb-2">Cloud Platform: Amazon Web Services (AWS)</h2>
                    <p class="text-lg text-gray-700 mb-6">Chosen for its comprehensive suite of services, scalability, and proven track record in handling large-scale data analytics and machine learning workloads.</p>
                </div>
                <div>
                    <h2 class="text-2xl font-semibold text-[#005a9e] mb-2">Key Components:</h2>
                    <ul class="list-none text-lg text-gray-700 space-y-3">
                        <li class="flex items-center"><img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws-s3.svg" class="h-6 w-6 mr-2"><span class="font-semibold">Data Storage:</span>&nbsp;AWS S3</li>
                        <li class="flex items-center"><img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws-ec2.svg" class="h-6 w-6 mr-2"><span class="font-semibold">Compute:</span>&nbsp;AWS EC2</li>
                        <li class="flex items-center"><img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Machine-Learning/AWS-Machine-Learning-SageMaker.svg" class="h-6 w-6 mr-2"><span class="font-semibold">Machine Learning:</span>&nbsp;AWS SageMaker</li>
                        <li class="flex items-center"><img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/tableau.svg" class="h-6 w-6 mr-2"><span class="font-semibold">Data Visualization:</span>&nbsp;Tableau</li>
                        <li class="flex items-center"><img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/copyleft-pirate.svg" class="h-6 w-6 mr-2"><span class="font-semibold">Data Scraping:</span>&nbsp;Scrapy Cloud</li>
                        <li class="flex items-center"><img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Machine-Learning/AWS-Machine-Learning-Comprehend.svg" class="h-6 w-6 mr-2"><span class="font-semibold">NLP:</span>&nbsp;AWS Comprehend</li>
                    </ul>
                </div>
            </div>
            <div class="w-3/5 flex items-center justify-center">
                <div class="w-full">
                    <div class="flex justify-around items-center mb-8">
                        <div class="text-center w-32">
                            <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/data-modelling.svg" class="h-12 mx-auto">
                            <p class="mt-2 text-sm font-semibold">Industry Reports</p>
                        </div>
                        <div class="text-center w-32">
                            <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/relational-databases.svg" class="h-12 mx-auto">
                            <p class="mt-2 text-sm font-semibold">Government Databases</p>
                        </div>
                        <div class="text-center w-32">
                            <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/discoverable-content.svg" class="h-12 mx-auto">
                            <p class="mt-2 text-sm font-semibold">News Feeds</p>
                        </div>
                        <div class="text-center w-32">
                            <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/e90839d55bdbdac9e5e79cdda870a356008c439a/svgs/web-skills-flat-vectors/twitter.29b6fbd9.svg" class="h-12 mx-auto">
                            <p class="mt-2 text-sm font-semibold">Social Media</p>
                        </div>
                    </div>
                    <div class="flex justify-center items-center">
                        <div class="arrow w-3/4"></div>
                    </div>
                    <div class="flex justify-center items-center my-4">
                        <div class="bg-gray-100 border border-gray-300 rounded-lg p-4 text-center shadow-md">
                            <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws-s3.svg" class="h-16 mx-auto mb-2">
                            <h3 class="text-md font-bold text-gray-800">AWS S3 (Data Lake)</h3>
                        </div>
                    </div>
                    <div class="flex justify-around items-center mt-8">
                        <div class="text-center w-48 p-4 bg-gray-100 border border-gray-300 rounded-lg shadow-md">
                            <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws-ec2.svg" class="h-12 mx-auto mb-2">
                            <h3 class="text-md font-bold text-gray-800">AWS EC2</h3>
                            <p class="text-sm text-gray-600">(Scrapy Cloud for Web Scraping)</p>
                            <div class="arrow w-full mt-4"></div>
                             <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Machine-Learning/AWS-Machine-Learning-Comprehend.svg" class="h-12 mx-auto mt-4">
                            <p class="text-sm font-semibold text-gray-800">AWS Comprehend (NLP)</p>
                        </div>
                        <div class="text-center w-48 p-4 bg-gray-100 border border-gray-300 rounded-lg shadow-md">
                            <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/aws_svg/Machine-Learning/AWS-Machine-Learning-SageMaker.svg" class="h-12 mx-auto mb-2">
                            <h3 class="text-md font-bold text-gray-800">AWS SageMaker</h3>
                             <p class="text-sm text-gray-600">(Machine Learning)</p>
                            <div class="arrow w-full mt-4"></div>
                            <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/tableau.svg" class="h-12 mx-auto mt-4">
                            <p class="text-sm font-semibold text-gray-800">Tableau (Interactive Dashboards)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>