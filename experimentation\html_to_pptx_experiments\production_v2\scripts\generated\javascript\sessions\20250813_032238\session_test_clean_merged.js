
// Consolidated Multi-Slide PowerPoint Generator
// Generated by JavaScript-based merging approach (production_v2)

const PptxGenJS = require('pptxgenjs');


function generateSlide2AgendaUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_2_agenda_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE CANVAS BOUNDARIES & GENERIC CONSTANTS
    const SAFE_MARGIN = 0.3;
    const SLIDE_WIDTH = 10;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use 8.2 for safety
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const ICON_TEXT_GAP = 0.3;
    const ICON_SIZE = 0.2;

    // ULTRA-SAFE COLORS
    const BG_COLOR = '0a192f';
    const PRIMARY_ACCENT_COLOR = '64ffda';
    const PRIMARY_TEXT_COLOR = 'ccd6f6';
    const BOLD_TEXT_COLOR = 'a8b2d1';

    // Set slide background color
    slide.background = { color: BG_COLOR };

    // Add Title
    slide.addText("Fortifying Our Defenses: A Zero Trust Approach", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16, // Ultra-safe title size
        color: PRIMARY_ACCENT_COLOR,
        bold: true,
        valign: 'top'
    });

    // Add Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.2, // 120px equivalent
        h: 0.03, // 3px equivalent
        fill: { color: PRIMARY_ACCENT_COLOR }
    });

    // Agenda Content - structured for rich text
    const agendaItems = [
        { boldText: "The Challenge:", regularText: "Current security relies on outdated \"trust but verify\" models, leaving us vulnerable to sophisticated attacks and insider threats." },
        { boldText: "The Solution:", regularText: "Implement a comprehensive Zero Trust security framework, assuming no user or device is inherently trustworthy." },
        { boldText: "Key Components:", regularText: "Strong Identity & Access Management, Device Security, Network Microsegmentation, and Data Protection." },
        { boldText: "Cloud-First Strategy:", regularText: "Leverage cloud services for scalability, cost-effectiveness, and advanced security features." },
        { boldText: "Benefits:", regularText: "Reduced risk of breaches, improved compliance, enhanced data protection, and increased operational efficiency." },
        { boldText: "Call to Action:", regularText: "Invest in a phased Zero Trust implementation to secure our sensitive data and prevent cyberattacks." }
    ];

    // Vertical position tracker
    let currentY = CONTENT_START_Y;
    const ITEM_VERTICAL_SPACING = 0.6; // Provides ample space between items

    // Function to add text safely and prevent overflow
    function addTextSafely(slide, text, options) {
        const wouldOverflow = (options.y + options.h) > MAX_CONTENT_Y;
        if (wouldOverflow) {
            console.warn(`Skipping element to prevent vertical overflow: ${text.substring(0, 30)}...`);
            return false;
        }
        slide.addText(text, options);
        return true;
    }

    // Loop through agenda items and add them to the slide
    agendaItems.forEach(item => {
        // Check for vertical overflow before adding the icon and text
        if ((currentY + ITEM_VERTICAL_SPACING) > MAX_CONTENT_Y) {
            console.warn("Stopping content addition to prevent overflow.");
            return;
        }

        // Add ultra-safe text-based icon (✓)
        addTextSafely(slide, "✓", {
            x: SAFE_MARGIN,
            y: currentY,
            w: ICON_SIZE,
            h: ICON_SIZE,
            fontSize: 12,
            color: PRIMARY_ACCENT_COLOR,
            bold: true,
            valign: 'top'
        });

        // Add rich text content (bold + regular)
        const textX = SAFE_MARGIN + ICON_TEXT_GAP;
        const textW = 8.2 - ICON_TEXT_GAP; // Ensure it stays within the safe zone
        const textH = ITEM_VERTICAL_SPACING - 0.1; // Height for the text block

        addTextSafely(slide,
            [
                { text: item.boldText + " ", options: { color: BOLD_TEXT_COLOR, bold: true } },
                { text: item.regularText, options: { color: PRIMARY_TEXT_COLOR } }
            ],
            {
                x: textX,
                y: currentY,
                w: textW,
                h: textH,
                fontSize: 10, // Ultra-safe content size
                lineSpacing: 14, // Tight but readable line spacing
                valign: 'top'
            }
        );

        // Increment vertical position for the next item
        currentY += ITEM_VERTICAL_SPACING;
    });

    return slide;
}

function generateSlide3GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_3_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & STYLE CONSTANTS (GENERIC)
    const SAFE_MARGIN = 0.3;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const LINE_SPACING = 0.25; // Ultra-tight spacing

    // Two-column layout dimensions
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 3.8;
    const RIGHT_COL_X = 4.3;
    const RIGHT_COL_W = 4.2; // Adjusted for safe fit

    // Color Palette
    const BG_COLOR = '0a192f';
    const PRIMARY_COLOR = '64ffda';
    const TEXT_COLOR = 'ccd6f6';
    const TEXT_HIGHLIGHT_COLOR = 'a8b2d1';

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Add Title
    slide.addText("The Weaknesses of Our Current Security Posture", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16, // Ultra-safe max title size
        color: PRIMARY_COLOR,
        bold: true,
        align: 'left',
        valign: 'top'
    });

    // Add Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_MARGIN,
        y: 0.9,
        w: 1.5,
        h: 0,
        line: { color: PRIMARY_COLOR, width: 2 }
    });

    // --- LEFT COLUMN (VISUALS) ---
    const visualCenterX = LEFT_COL_X + (LEFT_COL_W / 2);
    const visualCenterY = CONTENT_START_Y + ((MAX_CONTENT_Y - CONTENT_START_Y) / 2);

    // Add Castle Icon (using image path for reliability)
    slide.addImage({
        path: "https://www.svgrepo.com/show/514338/castle.svg",
        x: visualCenterX - 1.5, // Center the 3.0" wide image
        y: visualCenterY - 1.7, // Position vertically
        w: 3.0,
        h: 3.0,
        // SVG colorization is applied via PptxGenJS options
        color: PRIMARY_COLOR
    });

    // Add Unlocked Icon (using image path)
    slide.addImage({
        path: "https://www.svgrepo.com/show/315900/unlocked-padlock.svg",
        x: visualCenterX - 0.4, // Center the 0.8" wide icon
        y: visualCenterY - 1.0, // Position inside castle
        w: 0.8,
        h: 0.8,
        color: TEXT_HIGHLIGHT_COLOR
    });

    // Add Visual Label with a shape as a border
    slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        x: visualCenterX - 1.0, // Center the 2.0" wide shape
        y: visualCenterY + 1.6,
        w: 2.0,
        h: 0.4,
        rectRadius: 0.05,
        fill: { color: BG_COLOR },
        line: { color: PRIMARY_COLOR, width: 1.5 }
    });
    slide.addText("Perimeter Security", {
        x: visualCenterX - 1.0,
        y: visualCenterY + 1.6,
        w: 2.0,
        h: 0.4,
        fontSize: 10,
        color: PRIMARY_COLOR,
        bold: true,
        align: 'center',
        valign: 'middle'
    });

    // --- RIGHT COLUMN (TEXT CONTENT) ---
    const weaknesses = [
        { strong: "Increased Sophistication of Cyberattacks:", text: "Attackers are bypassing traditional perimeter defenses." },
        { strong: "Insider Threats:", text: "Malicious or negligent employees can compromise sensitive data." },
        { strong: "Complex IT Environment:", text: "Cloud adoption, remote work, and BYOD create new attack vectors." },
        { strong: "Compliance Requirements:", text: "Regulations like GDPR and CCPA demand stronger data protection measures." },
        { strong: "Lack of Visibility:", text: "Difficult to track user activity and identify suspicious behavior across the network." },
        { strong: "The Cost of Inaction:", text: "Breaches result in significant financial, reputational, and legal liabilities." }
    ];

    // Function to get font size based on content density
    function getUltraSafeFontSize(elementCount, baseSize) {
        let size = baseSize;
        if (elementCount > 5) size = 9; // Dense content
        return Math.max(size, 8); // Never below 8px
    }

    const contentFontSize = getUltraSafeFontSize(weaknesses.length, 10);
    const contentLineHeight = 0.6; // Spacing for icon + text
    let currentY = CONTENT_START_Y + 0.2; // Start slightly lower for better alignment

    // Function to add text safely and prevent overflow
    function addTextSafely(slide, text, options) {
        if ((options.y + options.h) > MAX_CONTENT_Y) {
            console.warn(`Skipping element to prevent vertical overflow: ${text.substring(0, 30)}...`);
            return false;
        }
        slide.addText(text, options);
        return true;
    }

    weaknesses.forEach(item => {
        // Check for vertical overflow before adding a new group
        if ((currentY + contentLineHeight) > MAX_CONTENT_Y) {
            console.warn("Stopping content addition to prevent vertical overflow.");
            return;
        }

        // Add Icon (using a safe, text-based character)
        addTextSafely(slide, "⚠️", {
            x: RIGHT_COL_X,
            y: currentY,
            w: 0.3,
            h: contentLineHeight,
            fontSize: 14,
            color: PRIMARY_COLOR,
            valign: 'top'
        });

        // Add Text Content
        const textOptions = {
            x: RIGHT_COL_X + 0.3,
            y: currentY,
            w: RIGHT_COL_W - 0.3,
            h: contentLineHeight,
            fontSize: contentFontSize,
            color: TEXT_COLOR,
            valign: 'top',
            lineSpacing: contentFontSize + 4 // Provides good line spacing within the text box
        };

        // Use rich text for bolding part of the string
        addTextSafely(slide, [{
            text: item.strong + " ",
            options: { bold: true, color: TEXT_HIGHLIGHT_COLOR }
        }, {
            text: item.text,
            options: { bold: false, color: TEXT_COLOR }
        }], textOptions);

        currentY += contentLineHeight;
    });

    return slide;
}

function generateSlide4GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_4_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & SIZING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const MAX_CONTENT_Y = 4.8;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.0;

    // Two-column layout constants
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.5; // 55% of available width, rounded
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.4; // 0.4" gutter
    const RIGHT_COL_W = 3.6; // 45% of available width, rounded

    // Color Palette
    const BG_COLOR = '0a192f';
    const PRIMARY_ACCENT_COLOR = '64ffda';
    const PRIMARY_TEXT_COLOR = 'ccd6f6';
    const SECONDARY_TEXT_COLOR = 'a8b2d1';
    const BORDER_COLOR = '1a3a6e';
    const CARD_FILL_COLOR = '1a2b4a'; // A slightly lighter, safe fill

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Title
    slide.addText("Zero Trust: Never Trust, Always Verify", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: 8.2,
        h: TITLE_H,
        fontSize: 16,
        color: PRIMARY_ACCENT_COLOR,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.5, // 120px is approx 1.5 inches
        h: 0,
        line: { color: PRIMARY_ACCENT_COLOR, width: 2 }
    });

    // --- Left Column Content ---
    const principles = [
        { title: "Identity-Centric Security:", text: "Verify every user and device before granting access." },
        { title: "Microsegmentation:", text: "Isolate applications and data to limit the blast radius of a breach." },
        { title: "Continuous Monitoring:", text: "Constantly monitor user activity, device health, and network traffic for suspicious behavior." },
        { title: "Data-Centric Protection:", text: "Protect sensitive data at rest and in transit with encryption and DLP." },
        { title: "Automation & Orchestration:", text: "Automate security tasks and workflows to improve efficiency and reduce response times." }
    ];

    let currentY = CONTENT_START_Y;
    const itemHeight = 0.55; // Calculated height for icon + text block

    principles.forEach(item => {
        if ((currentY + itemHeight) > MAX_CONTENT_Y) return; // Overflow prevention

        // Icon (using a safe, basic shape)
        slide.addShape(pptx.shapes.OVAL, {
            x: LEFT_COL_X,
            y: currentY + 0.05,
            w: 0.2,
            h: 0.2,
            fill: { color: PRIMARY_ACCENT_COLOR }
        });

        // Text content for the principle
        slide.addText([
            { text: item.title, options: { bold: true, color: SECONDARY_TEXT_COLOR } },
            { text: ` ${item.text}`, options: { color: PRIMARY_TEXT_COLOR } }
        ], {
            x: LEFT_COL_X + 0.3,
            y: currentY,
            w: LEFT_COL_W - 0.3,
            h: itemHeight,
            fontSize: 9,
            lineSpacing: 12
        });

        currentY += itemHeight;
    });

    // Conclusion Text (at the bottom of the left column)
    const conclusionY = 4.2;
    const conclusionHeight = 0.5;
    if ((conclusionY + conclusionHeight) <= MAX_CONTENT_Y) {
        // Decorative line
        slide.addShape(pptx.shapes.LINE, {
            x: LEFT_COL_X,
            y: conclusionY,
            w: 0,
            h: conclusionHeight,
            line: { color: PRIMARY_ACCENT_COLOR, width: 2 }
        });

        slide.addText([
            { text: "Why it Works: ", options: { bold: true, color: PRIMARY_ACCENT_COLOR } },
            { text: "Zero Trust eliminates implicit trust, making it significantly harder for attackers to gain access to sensitive data.", options: { color: SECONDARY_TEXT_COLOR } }
        ], {
            x: LEFT_COL_X + 0.2,
            y: conclusionY,
            w: LEFT_COL_W - 0.2,
            h: conclusionHeight,
            fontSize: 9,
            lineSpacing: 12
        });
    }

    // --- Right Column Content ---
    const diagramCards = [
        { title: "Verify Explicitly", text: "Authenticate and authorize based on all available data points." },
        { title: "Least Privilege Access", text: "Limit user access with just-in-time and just-enough-access (JIT/JEA)." },
        { title: "Assume Breach", text: "Minimize blast radius and segment access. Verify all sessions are encrypted." }
    ];

    currentY = CONTENT_START_Y + 0.2; // Start slightly lower for visual balance
    const cardHeight = 1.0;
    const cardGutter = 0.2;

    diagramCards.forEach(card => {
        if ((currentY + cardHeight) > MAX_CONTENT_Y) return; // Overflow prevention

        // Card background shape
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: RIGHT_COL_X,
            y: currentY,
            w: RIGHT_COL_W,
            h: cardHeight,
            fill: { color: CARD_FILL_COLOR },
            line: { color: BORDER_COLOR, width: 1 },
            rectRadius: 0.1
        });

        // Icon (using a safe, text-based symbol)
        slide.addText("✓", {
            x: RIGHT_COL_X + 0.2,
            y: currentY + 0.3,
            w: 0.5,
            h: 0.5,
            fontSize: 24,
            bold: true,
            color: PRIMARY_ACCENT_COLOR,
            align: 'center'
        });

        // Card text
        slide.addText([
            { text: card.title, options: { bold: true, fontSize: 10, color: PRIMARY_TEXT_COLOR, breakLine: true } },
            { text: card.text, options: { fontSize: 9, color: SECONDARY_TEXT_COLOR } }
        ], {
            x: RIGHT_COL_X + 0.8,
            y: currentY + 0.15,
            w: RIGHT_COL_W - 1.0,
            h: cardHeight - 0.3,
            lineSpacing: 12
        });

        currentY += cardHeight + cardGutter;
    });

    return slide;
}

function generateSlide5GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_5_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE DEFAULTS & CONSTANTS
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const TITLE_Y = 0.3;
    const TITLE_H = 0.5;
    const TITLE_DIVIDER_Y = 0.8;

    // Colors from HTML
    const BG_COLOR = '0a192f';
    const PRIMARY_COLOR = '64ffda';
    const TEXT_COLOR_LIGHT = 'ccd6f6';
    const TEXT_COLOR_MEDIUM = 'a8b2d1';
    const SHAPE_FILL_COLOR = '2a4365'; // rgba(42, 67, 101, 0.5) -> solid
    const SHAPE_BORDER_COLOR = '1d3b66';

    // Two-column layout constants
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.2;
    const RIGHT_COL_X = LEFT_COL_X + LEFT_COL_W + 0.4; // 4.9
    const RIGHT_COL_W = SLIDE_WIDTH - RIGHT_COL_X - SAFE_MARGIN - 0.3; // 4.5

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Slide Title
    slide.addText("Building a Secure Foundation with Cloud Technologies", {
        x: SAFE_MARGIN,
        y: TITLE_Y,
        w: SLIDE_WIDTH - (2 * SAFE_MARGIN),
        h: TITLE_H,
        fontSize: 16,
        color: PRIMARY_COLOR,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: TITLE_DIVIDER_Y,
        w: 1.2,
        h: 0.03,
        fill: { color: PRIMARY_COLOR }
    });

    // --- LEFT COLUMN: Architecture Diagram ---
    const architectureLayers = [
        { text: "Identity: Azure AD Conditional Access" },
        { text: "Application: Web Apps / Microservices" },
        { text: "Data: S3 Buckets / Azure Blob Storage" },
        { text: "Compute: EC2 Instances / Azure VMs" },
        { text: "Network: Azure Virtual Network / NSGs" },
        { text: "Infrastructure: Cloud Provider" }
    ];

    let currentY = CONTENT_START_Y + 0.5; // Start lower for visual centering
    const layerHeight = 0.45;
    const layerSpacing = 0.1;
    const iconSize = 0.2;

    architectureLayers.forEach(layer => {
        // Check for vertical overflow before adding a layer
        if ((currentY + layerHeight) > MAX_CONTENT_Y) return;

        // Layer Box (Rounded Rectangle)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: LEFT_COL_X,
            y: currentY,
            w: LEFT_COL_W,
            h: layerHeight,
            fill: { color: SHAPE_FILL_COLOR },
            line: { color: SHAPE_BORDER_COLOR, width: 1 },
            rectRadius: 0.08
        });

        // Layer Text
        slide.addText(layer.text, {
            x: LEFT_COL_X + 0.15,
            y: currentY,
            w: LEFT_COL_W - 0.6, // Leave space for icon
            h: layerHeight,
            fontSize: 9,
            color: TEXT_COLOR_LIGHT,
            valign: 'middle'
        });

        // Lock Icon (using a safe custom shape)
        // A simple "lock" shape: a square base with an arc on top.
        const iconX = LEFT_COL_X + LEFT_COL_W - 0.4;
        const iconY = currentY + (layerHeight - iconSize) / 2;
        // Arc for the lock shackle
        slide.addShape(pptx.shapes.ARC, {
            x: iconX,
            y: iconY,
            w: iconSize,
            h: iconSize,
            line: { color: PRIMARY_COLOR, width: 1.5 },
            angleRange: [180, 360]
        });
        // Rectangle for the lock body
        slide.addShape(pptx.shapes.RECTANGLE, {
            x: iconX,
            y: iconY + (iconSize / 2),
            w: iconSize,
            h: iconSize / 2,
            fill: { color: PRIMARY_COLOR }
        });

        currentY += layerHeight + layerSpacing;
    });

    // --- RIGHT COLUMN: Technology Details ---
    const techItems = [
        { title: "Identity & Access Management (IAM)", details: "Azure AD Conditional Access + Passwordless Auth + Behavioral Biometrics" },
        { title: "Device Security & Endpoint Management", details: "Microsoft Endpoint Manager (Intune) + DPA + Remote Browser Isolation" },
        { title: "Network Security & Microsegmentation", details: "Azure VNets/NSGs + Cloudflare ZTNA + AI Anomaly Detection" },
        { title: "Data Security & Protection", details: "Microsoft Purview + Cloud-Native Key Management (KMS)" },
        { title: "Automation & Orchestration", details: "Azure Logic Apps + Ansible / Terraform (IaC)" }
    ];

    currentY = CONTENT_START_Y + 0.2; // Reset Y for the right column
    const itemHeight = 0.6;
    const itemSpacing = 0.15;

    techItems.forEach(item => {
        // Check for vertical overflow before adding an item
        if ((currentY + itemHeight) > MAX_CONTENT_Y) return;

        // Tech Item Title
        slide.addText(item.title, {
            x: RIGHT_COL_X,
            y: currentY,
            w: RIGHT_COL_W,
            h: 0.2,
            fontSize: 10,
            color: PRIMARY_COLOR,
            bold: true
        });

        // Tech Item Details
        slide.addText(item.details, {
            x: RIGHT_COL_X,
            y: currentY + 0.2,
            w: RIGHT_COL_W,
            h: 0.4,
            fontSize: 9,
            color: TEXT_COLOR_MEDIUM,
            lineSpacing: 12 // Ultra-safe line spacing in points
        });

        currentY += itemHeight + itemSpacing;
    });

    return slide;
}

function generateSlide6GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_6_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// Set the slide layout to 16:9 to match the HTML's aspect ratio.
// ULTRA-SAFE CONSTANTS - NO SLIDE-SPECIFIC NAMING
    const SAFE_MARGIN = 0.3;
    const SLIDE_WIDTH = 10.0;
    const SLIDE_HEIGHT = 5.625;

    // Content Area Boundaries
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer column widths

    // Two-Column Layout Dimensions
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.8; // Increased width for text content
    const RIGHT_COL_X = 5.3;
    const RIGHT_COL_W = 3.2; // Reserved for the large icon shape

    // Colors from HTML
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY = '64ffda';
    const COLOR_TEXT_HEADING = 'a8b2d1';
    const COLOR_TEXT_BODY = 'ccd6f6';
    const COLOR_TEXT_SUBTLE = '8892b0';

    // Set slide background color
    slide.background = { color: COLOR_BACKGROUND };

    // 1. Slide Title
    slide.addText("Assembling the Right Team for Success", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16, // Ultra-safe title size
        color: COLOR_PRIMARY,
        bold: true,
        align: 'left'
    });

    // 2. Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2,
        h: 0.04,
        fill: { color: COLOR_PRIMARY }
    });

    // 3. Content Data
    const teamItems = [
        {
            title: "Dedicated Security Team",
            desc: "Requires experienced security engineers, architects, and analysts to lead the implementation."
        },
        {
            title: "Cloud Expertise",
            desc: "Personnel with deep knowledge of chosen cloud platforms (Azure, AWS, or Google Cloud)."
        },
        {
            title: "IAM Specialists",
            desc: "Experts in identity and access management solutions are crucial for the core of Zero Trust."
        },
        {
            title: "Training & Awareness Programs",
            subItems: [
                "Educate all employees on Zero Trust principles and secure work practices.",
                "Provide in-depth technical training for IT staff on new technologies."
            ]
        },
        {
            title: "Change Management",
            desc: "A dedicated team to manage the cultural and operational transition, ensuring user adoption."
        }
    ];

    // 4. Vertical Position Tracking
    let currentY = CONTENT_START_Y;
    const ITEM_SPACING = 0.15; // Space between major list items
    const LINE_HEIGHT_TITLE = 0.2;
    const LINE_HEIGHT_DESC = 0.18;
    const LINE_HEIGHT_SUB = 0.18;
    const ICON_SIZE = 0.25;
    const ICON_TEXT_GAP = 0.1;

    // 5. Left Column: Team List
    teamItems.forEach(item => {
        const itemHeightEstimate = 0.6; // A safe estimate for each block
        if (currentY + itemHeightEstimate > MAX_CONTENT_Y) {
            console.warn(`Skipping item "${item.title}" to prevent vertical overflow.`);
            return; // Skip this item if it risks overflow
        }

        // Icon (using a safe, basic shape as a placeholder)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: LEFT_COL_X,
            y: currentY + 0.05, // Vertically center icon slightly
            w: ICON_SIZE,
            h: ICON_SIZE,
            fill: { color: COLOR_PRIMARY },
            rectRadius: 0.05
        });

        const textX = LEFT_COL_X + ICON_SIZE + ICON_TEXT_GAP;
        const textW = LEFT_COL_W - (ICON_SIZE + ICON_TEXT_GAP);

        // Item Title
        slide.addText(item.title, {
            x: textX,
            y: currentY,
            w: textW,
            h: LINE_HEIGHT_TITLE,
            fontSize: 10, // Ultra-safe content size
            color: COLOR_TEXT_HEADING,
            bold: true
        });
        currentY += LINE_HEIGHT_TITLE;

        // Item Description or Sub-list
        if (item.desc) {
            slide.addText(item.desc, {
                x: textX,
                y: currentY,
                w: textW,
                h: LINE_HEIGHT_DESC * 2, // Allow for two lines
                fontSize: 9, // Ultra-safe data size
                color: COLOR_TEXT_BODY,
                lineSpacing: 12 // Tighter line spacing
            });
            currentY += (LINE_HEIGHT_DESC * 2) + ITEM_SPACING;
        } else if (item.subItems) {
            item.subItems.forEach(subItem => {
                if (currentY + LINE_HEIGHT_SUB > MAX_CONTENT_Y) return;
                // Use a text-based bullet for maximum safety
                slide.addText("• " + subItem, {
                    x: textX + 0.2, // Indent sub-item
                    y: currentY,
                    w: textW - 0.2,
                    h: LINE_HEIGHT_SUB * 2, // Allow for two lines
                    fontSize: 9,
                    color: COLOR_TEXT_SUBTLE,
                    lineSpacing: 12
                });
                currentY += LINE_HEIGHT_SUB;
            });
            currentY += ITEM_SPACING;
        }
    });

    // 6. Right Column: Large Icon Placeholder
    // Using CUSTOM_GEOMETRY to create a simple "team" icon representation.
    // This is a safe, valid shape that avoids external images.
    const iconPoints = [
        // Head 1
        { x: 0.35, y: 0.20 }, { x: 0.65, y: 0.20 }, { x: 0.65, y: 0.35 }, { x: 0.35, y: 0.35 },
        // Body 1
        { x: 0.25, y: 0.40 }, { x: 0.75, y: 0.40 }, { x: 0.75, y: 0.80 }, { x: 0.25, y: 0.80 },
    ];

    slide.addShape(pptx.shapes.CUSTOM_GEOMETRY, {
        x: RIGHT_COL_X + 0.8, // Center the shape within the right column
        y: 1.8,
        w: 1.5,
        h: 1.5,
        points: iconPoints,
        fill: { color: COLOR_PRIMARY, transparency: 80 } // High transparency as in HTML
    });

    return slide;
}

function generateSlide7GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_7_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE POSITIONING & STYLING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use smaller values for safety

    // Colors from HTML
    const BG_COLOR = '0a192f';
    const PRIMARY_COLOR = '64ffda';
    const TEXT_COLOR_LIGHT = 'ccd6f6';
    const TEXT_COLOR_MEDIUM = 'a8b2d1';
    const TEXT_COLOR_DARK = '0a192f';

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Title
    slide.addText("A Strategic Roadmap to Zero Trust", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16,
        color: PRIMARY_COLOR,
        bold: true,
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.2, // 120px is ~1.2 inches
        h: 0.03,
        fill: { color: PRIMARY_COLOR },
    });

    // --- Gantt Chart Area ---
    const GANTT_START_Y = 1.2;
    const PHASE_COL_X = SAFE_MARGIN;
    const PHASE_COL_W = 2.0;
    const TIMELINE_COL_X = PHASE_COL_X + PHASE_COL_W; // 2.3
    const TIMELINE_COL_W = 6.2; // 2.3 + 6.2 = 8.5 (SAFE)
    const QUARTER_W = TIMELINE_COL_W / 4; // 1.55

    // Timeline Header Text
    const timelineHeaders = ["3 Months", "6 Months", "9 Months", "12 Months"];
    timelineHeaders.forEach((header, index) => {
        slide.addText(header, {
            x: TIMELINE_COL_X + (index * QUARTER_W),
            y: GANTT_START_Y,
            w: QUARTER_W,
            h: 0.2,
            align: 'center',
            fontSize: 9,
            color: TEXT_COLOR_MEDIUM,
        });
    });

    // Timeline Header Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: TIMELINE_COL_X,
        y: GANTT_START_Y + 0.25,
        w: TIMELINE_COL_W,
        h: 0,
        line: { color: '1a3a6e', width: 1 },
    });

    // --- Gantt Chart Phases ---
    const phases = [
        {
            title: "Phase 1: Assessment & Planning",
            desc: "Conduct security assessment, define policies, select technologies.",
            barWidth: QUARTER_W,
            barText: "3 Months",
            opacity: "CC" // 80%
        },
        {
            title: "Phase 2: Identity & Access",
            desc: "Implement passwordless auth, conditional access, behavioral biometrics.",
            barWidth: QUARTER_W * 2,
            barText: "6 Months",
            opacity: "B3" // 70%
        },
        {
            title: "Phase 3: Device Security",
            desc: "Deploy endpoint management, enforce device posture, implement RBI.",
            barWidth: QUARTER_W * 3,
            barText: "9 Months",
            opacity: "99" // 60%
        },
        {
            title: "Phase 4: Network Microsegmentation",
            desc: "Implement SDP, configure security groups, deploy AI anomaly detection.",
            barWidth: QUARTER_W * 4,
            barText: "12 Months",
            opacity: "80" // 50%
        },
        {
            title: "Phase 5: Data Security & Monitoring",
            desc: "Implement DLP, encrypt data, and continuously monitor environment.",
            barWidth: QUARTER_W * 4,
            barText: "Ongoing",
            opacity: "66" // 40%
        }
    ];

    let currentY = GANTT_START_Y + 0.4;
    const rowHeight = 0.8;
    const barHeight = 0.4;

    phases.forEach(phase => {
        // Stop if the next element will overflow the safe area
        if ((currentY + rowHeight) > MAX_CONTENT_Y) {
            console.warn(`Skipping phase "${phase.title}" to prevent vertical overflow.`);
            return;
        }

        // Phase Title and Description (Left Column)
        slide.addText(phase.title, {
            x: PHASE_COL_X,
            y: currentY,
            w: PHASE_COL_W,
            h: 0.25,
            fontSize: 10,
            bold: true,
            color: TEXT_COLOR_MEDIUM,
        });
        slide.addText(phase.desc, {
            x: PHASE_COL_X,
            y: currentY + 0.2,
            w: PHASE_COL_W,
            h: 0.4,
            fontSize: 8,
            color: TEXT_COLOR_LIGHT,
        });

        // Timeline Bar (Right Column)
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: TIMELINE_COL_X,
            y: currentY,
            w: phase.barWidth,
            h: barHeight,
            fill: { color: PRIMARY_COLOR, transparency: 100 - parseInt(phase.opacity, 16) * 100 / 255 },
            rectRadius: 0.1
        });

        // Text on Timeline Bar
        slide.addText(phase.barText, {
            x: TIMELINE_COL_X,
            y: currentY,
            w: phase.barWidth,
            h: barHeight,
            align: 'center',
            valign: 'middle',
            fontSize: 9,
            bold: true,
            color: TEXT_COLOR_DARK,
        });

        currentY += rowHeight;
    });

    return slide;
}

function generateSlide8GeneralUltraSafeAutoConvert(pptx) {
    // Function-scoped variables for slide_8_general_ultra_safe_auto_convert
    const slide = pptx.addSlide();

    // Slide content
// ULTRA-SAFE DEFAULTS & HELPERS
    const MAX_CONTENT_Y = 4.8;

    /**
     * Adds text to the slide only if it fits within the vertical safe area.
     * @param {object} slide - The PptxGenJS slide object.
     * @param {string} text - The text content to add.
     * @param {object} options - The PptxGenJS options for addText.
     * @returns {boolean} - True if the text was added, false otherwise.
     */
    function addTextSafely(slide, text, options) {
        const wouldOverflow = (options.y + options.h) > MAX_CONTENT_Y;
        if (wouldOverflow) {
            console.warn(`Skipping element to prevent overflow: ${text.substring(0, 30)}...`);
            return false;
        }
        slide.addText(text, options);
        return true;
    }

    // Set slide background color
    slide.background = { color: '0a192f' };

    // Add ultra-safe title
    slide.addText("Investing in a Secure Future", {
        x: 0.3,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16,
        color: '64ffda',
        bold: true,
        align: 'left'
    });

    // Add title divider shape (ultra-safe)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: 0.3,
        y: 0.8, // Positioned safely below the title
        w: 1.2,
        h: 0.03,
        fill: { color: '64ffda' }
    });

    // Define content start position
    const CONTENT_START_Y = 1.2;

    // --- LEFT COLUMN: COST TABLE ---
    const LEFT_COL_X = 0.3;
    const LEFT_COL_W = 4.8;

    const tableRows = [
        // Header row with ultra-safe font sizes and colors
        [
            { text: "Category", options: { bold: true, fontSize: 9, color: 'a8b2d1', fill: '142642' } },
            { text: "Estimated Cost", options: { bold: true, fontSize: 9, color: 'a8b2d1', fill: '142642' } },
            { text: "Notes", options: { bold: true, fontSize: 9, color: 'a8b2d1', fill: '142642' } }
        ],
        // Data rows with ultra-safe font sizes
        [
            { text: "Software & Cloud Services", options: { fontSize: 8, color: 'ccd6f6' } },
            { text: "$50,000/year", options: { fontSize: 8, color: '64ffda', bold: true } },
            { text: "Azure AD, Intune, Cloudflare ZTNA, Microsoft Purview", options: { fontSize: 8, color: 'ccd6f6' } }
        ],
        [
            { text: "Hardware", options: { fontSize: 8, color: 'ccd6f6' } },
            { text: "$5,000", options: { fontSize: 8, color: '64ffda', bold: true } },
            { text: "Minimal, depends on existing infrastructure", options: { fontSize: 8, color: 'ccd6f6' } }
        ],
        [
            { text: "Personnel Costs", options: { fontSize: 8, color: 'ccd6f6' } },
            { text: "$150,000/year", options: { fontSize: 8, color: '64ffda', bold: true } },
            { text: "Security team, cloud experts, and training staff", options: { fontSize: 8, color: 'ccd6f6' } }
        ],
        [
            { text: "Training Costs", options: { fontSize: 8, color: 'ccd6f6' } },
            { text: "$10,000", options: { fontSize: 8, color: '64ffda', bold: true } },
            { text: "Employee and technical training programs", options: { fontSize: 8, color: 'ccd6f6' } }
        ],
        [
            { text: "Consulting Fees", options: { fontSize: 8, color: 'ccd6f6' } },
            { text: "$20,000", options: { fontSize: 8, color: '64ffda', bold: true } },
            { text: "If using external implementation consultants", options: { fontSize: 8, color: 'ccd6f6' } }
        ],
        // Footer row (styled as data)
        [
            { text: "Total Estimated Cost", options: { fontSize: 9, color: '64ffda', bold: true } },
            { text: "$235,000", options: { fontSize: 9, color: '64ffda', bold: true } },
            { text: "", options: { fontSize: 8 } }
        ]
    ];

    slide.addTable(tableRows, {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        colW: [1.4, 1.0, 2.4], // Total = 4.8 (SAFE!)
        border: { type: 'solid', pt: 1, color: '1a3a6e' },
        autoPage: false, // Critical for preventing overflow
        rowH: 0.35, // Generous row height to prevent text clipping
    });

    // --- RIGHT COLUMN: ROI LIST ---
    const RIGHT_COL_X = 5.4;
    const RIGHT_COL_W = 3.1;

    // Add right column title
    slide.addText("Return on Investment (ROI)", {
        x: RIGHT_COL_X,
        y: CONTENT_START_Y,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: 12,
        color: 'a8b2d1',
        bold: true
    });

    // Add divider line for the right column
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X - 0.2, // Positioned to the left of the content
        y: CONTENT_START_Y,
        w: 0,
        h: 3.0, // Safe height
        line: { color: '1a3a6e', width: 2 }
    });

    const roiItems = [
        { boldText: "Reduced Risk of Data Breaches:", normalText: "Quantify savings from preventing costly breaches." },
        { boldText: "Improved Compliance:", normalText: "Avoid fines and penalties from non-compliance." },
        { boldText: "Increased Productivity:", normalText: "Streamlined access and reduced security-related downtime." },
        { boldText: "Enhanced Reputation:", normalText: "Maintain customer trust and protect brand value." }
    ];

    let currentY = CONTENT_START_Y + 0.5; // Start below the ROI title
    const ICON_TEXT = "✓"; // Safest icon is a text character
    const ICON_SIZE = 0.2;
    const TEXT_X_OFFSET = 0.3;
    const ITEM_SPACING = 0.6; // Vertical space between items

    roiItems.forEach(item => {
        // Add text-based icon (ultra-safe)
        addTextSafely(slide, ICON_TEXT, {
            x: RIGHT_COL_X,
            y: currentY,
            w: ICON_SIZE,
            h: ICON_SIZE,
            fontSize: 12,
            color: '64ffda',
            bold: true
        });

        // Add formatted text content
        addTextSafely(slide, [
            { text: item.boldText, options: { fontSize: 9, color: 'a8b2d1', bold: true, breakLine: true } },
            { text: item.normalText, options: { fontSize: 9, color: 'ccd6f6' } }
        ], {
            x: RIGHT_COL_X + TEXT_X_OFFSET,
            y: currentY,
            w: RIGHT_COL_W - TEXT_X_OFFSET,
            h: 0.5, // Safe height for two lines
            lineSpacing: 12
        });

        currentY += ITEM_SPACING; // Increment Y for the next item
    });

    return slide;
}

// Main presentation creation function
function createPresentation() {
    const pptx = new PptxGenJS();

    console.log('Creating multi-slide presentation with 7 slides...');

    generateSlide2AgendaUltraSafeAutoConvert(pptx);
    generateSlide3GeneralUltraSafeAutoConvert(pptx);
    generateSlide4GeneralUltraSafeAutoConvert(pptx);
    generateSlide5GeneralUltraSafeAutoConvert(pptx);
    generateSlide6GeneralUltraSafeAutoConvert(pptx);
    generateSlide7GeneralUltraSafeAutoConvert(pptx);
    generateSlide8GeneralUltraSafeAutoConvert(pptx);

    console.log(`✅ Created presentation with ${pptx.slides.length} slides`);

    return pptx.writeFile({ fileName: 'session_test_clean.pptx' });
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('✅ Multi-slide PowerPoint generated successfully!');
        console.log('📄 File saved as: session_test_clean.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('❌ Error generating presentation:', error);
        console.error(error.stack);
        process.exit(1);
    });
