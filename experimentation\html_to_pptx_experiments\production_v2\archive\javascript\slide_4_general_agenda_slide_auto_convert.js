const PptxGenJS = require('pptxgenjs');


This solution:
1.  **Extracts Key Concepts**: It correctly identifies the main principles from the HTML's list and the three core tenets from the right-hand cards, treating them as the primary agenda items.
2.  **Applies Dynamic Layout**: It uses a dynamic layout function to select the optimal font size and spacing based on the total number of extracted items, ensuring readability.
3.  **Uses a Numbered List Pattern**: It implements a clean, numbered list format, which is a classic and effective pattern for agenda slides.
4.  **Adds Visual Hierarchy**: It uses bold, colored numbers and a subtle background shape to create a clear visual structure and guide the viewer's eye.
5.  **Adheres to Constraints**: The code strictly uses allowed PptxGenJS shapes and follows the required function signature and constants.


function createPresentation() {
    const pptx = new PptxGenJS();

    // Set a dark, modern slide background to match the HTML's theme
    pptx.defineLayout({
        name: 'DarkLayout',
        width: 10,
        height: 5.625,
        background: { color: '0A192F' }
    });
    const

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
