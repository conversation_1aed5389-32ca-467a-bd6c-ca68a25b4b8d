const PptxGenJS = require('pptxgenjs');


function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION
    // =======================================================================

    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0; // Higher start for dense content
    const MAX_CONTENT_Y = 4.8;

    const COLORS = {
        darkBlue: '1A237E',
        mediumGray: '5F6368',
        strongerBlue: '1A73E8',
        lighterBlue: 'E8F0FE',
        indigo900: '4338CA',
        blue500: '3B82F6',
        gray50: 'F9FAFB',
        gray700: '374151',
        blue600: '2563EB',
        indigo50: 'EEF2FF'
    };

    const FONT_SIZES = {
        title: 16,
        subtitle: 10,
        heading: 12,
        body: 9,
        takeaway: 10,
        metricTitle: 8,
        metricValue: 11
    };

    const COLUMN_WIDTH = (SLIDE_WIDTH - (2 * SAFE_MARGIN) - 0.6) / 2; // Two columns with gap

    // =======================================================================
    // 2. HELPER FUNCTIONS
    // =======================================================================

    function addTextWithDefaults(slide, text, options) {
        slide.addText(text, {
            x: options.x,
            y: options.y,
            w: options.w,
            h: options.h || 0.25,
            fontSize: options.fontSize || FONT_SIZES.body,
            color: options.color || COLORS.darkBlue,
            bold: options.bold || false,
            valign: 'top' // Default vertical alignment
        });
    }


    // =======================================================================
    // 3. SLIDE CONTENT
    // =======================================================================

    slide.background = { color: 'FFFFFF' };

    let currentY = SAFE_MARGIN;

    // Title
    addTextWithDefaults(slide, 'Phased Modernization Pyramid', {
        x: SAFE_MARGIN,
        y: currentY,
        w: SLIDE_WIDTH - (2 * SAFE_MARGIN),
        fontSize: FONT_SIZES.title,
        color: COLORS.darkBlue,
        bold: true
    });
    currentY += 0.5;

    // Subtitle
    addTextWithDefaults(slide, 'From COBOL Monolith to Cloud-Native at Controlled Risk. Width = Scope/Effort; Vertical = Maturity/Time.', {
        x: SAFE_MARGIN,
        y: currentY,
        w: SLIDE_WIDTH - (2 * SAFE_MARGIN),
        fontSize: FONT_SIZES.subtitle,
        color: COLORS.mediumGray
    });
    currentY += 0.4;


    // =======================================================================
    // 4. CHART (Funnel represented as table)
    // =======================================================================

    const pyramidData = [
        { name: '5. Foundation & Assessment', value: 100, description: 'Establish AWS Landing Zone (Control Tower, VPC, IAM), connectivity, observability (CloudWatch), and CI/CD. Create golden regression test suite; define SLOs/RTO/RPO. Plan data migration via DMS.' },
        { name: '4. Replatform & Stabilize', value: 80, description: 'Lift COBOL runtime to EC2 Auto Scaling. Migrate databases to RDS/Aurora (Multi-AZ) via DMS+CDC. Move files to EFS. Implement blue/green environments for immediate reliability wins.' },
        { name: '3. Strangler Refactor', value: 60, description: 'Front legacy with API Gateway. Carve out business capabilities into microservices (ECS/Lambda) and data domains (Aurora/DynamoDB). Use SQS/EventBridge for decoupling.' },
        { name: '2. Re-architect Cloud-Native', value: 40, description: 'Retire COBOL modules. Adopt serverless-first, SAGA patterns, and pervasive IaC (CDK/Terraform). Enforce SLIs/SLOs and automated chaos/resiliency testing.' },
        { name: '1. Optimize & Govern', value: 20, description: 'Performance/cost tuning (Graviton, Savings Plans). Implement Multi-Region DR. Use FinOps (Cost Explorer) and security (Security Hub) dashboards for continuous verification.' }
    ];

    const pyramidRows = pyramidData.map(item => ([{ text: item.name, options: { fontSize: 9, color: 'FFFFFF', fill: { color: COLORS.darkBlue }, bold: true } }]));

    slide.addTable(pyramidRows, {
        x: SAFE_MARGIN,
        y: currentY,
        w: COLUMN_WIDTH,
        h: 2.5,
        colW: [COLUMN_WIDTH],
        rowH: 0.5,
        fill: { color: COLORS.lighterBlue },
        border: { pt: 1, color: COLORS.strongerBlue }
    });

    // =======================================================================
    // 5. RIGHT COLUMN (Key Insights and Metrics)
    // =======================================================================

    const rightColumnX = SAFE_MARGIN + COLUMN_WIDTH + 0.6;

    addTextWithDefaults(slide, 'Key Insights', {
        x: rightColumnX,
        y: currentY,
        w: COLUMN_WIDTH,
        fontSize: FONT_SIZES.heading,
        color: COLORS.indigo900,
        bold: true
    });
    currentY += 0.3;


    const insights = [
        "Strangler pattern ensures zero/near-zero downtime by isolating and replacing capabilities incrementally.",
        "Data-first discipline (CDC, contract tests) preserves integrity while services peel away.",
        "Blue/green & canary releases de-risk cutovers; synthetic monitoring validates critical paths.",
        "Reuse what works (EC2/RDS) before reimagining; focus re-architecture on high-ROI domains.",
        "Measurable KPIs (error budgets, lead time, cost/transaction) guide pace and scope."
    ];

    insights.forEach(insight => {
        addTextWithDefaults(slide, `• ${insight}`, {
            x: rightColumnX,
            y: currentY,
            w: COLUMN_WIDTH,
            fontSize: FONT_SIZES.body,
            color: COLORS.gray700
        });
        currentY += 0.25;
    });

    // Reset currentY for the right column to place metrics below the insights
    currentY = CONTENT_START_Y + 3.0;


    const metrics = [
        { title: 'Time-to-First-Value', value: '6-12 wks' },
        { title: 'Availability Target', value: '≥99.95%' },
        { title: 'TCO Reduction', value: '30-50%' },
        { title: 'Test Coverage', value: '≥80%' },
        { title: 'Rollback Time', value: '<15 min' },
        { title: 'Cutover Strategy', value: 'Blue/Green' }
    ];

    metrics.forEach((metric, index) => {
        const metricX = rightColumnX + (index % 3) * (COLUMN_WIDTH / 3);
        const metricY = currentY + Math.floor(index / 3) * 0.7;

        addTextWithDefaults(slide, metric.title, {
            x: metricX,
            y: metricY,
            w: COLUMN_WIDTH / 3,
            fontSize: FONT_SIZES.metricTitle,
            color: COLORS.indigo900
        });

        addTextWithDefaults(slide, metric.value, {
            x: metricX,
            y: metricY + 0.2,
            w: COLUMN_WIDTH / 3,
            fontSize: FONT_SIZES.metricValue,
            color: COLORS.indigo900,
            bold: true
        });
    });


    // =======================================================================
    // 6. TAKEAWAY BAR
    // =======================================================================
    currentY = MAX_CONTENT_Y - 0.4;
    addTextWithDefaults(slide, "Takeaway: Stabilize on AWS first, then progressively strangle and re-architect until COBOL is safely retired—achieving cloud-native speed, resilience, and lower TCO without business interruption.", {
        x: SAFE_MARGIN,
        y: currentY,
        w: SLIDE_WIDTH - (2 * SAFE_MARGIN),
        fontSize: FONT_SIZES.takeaway,
        color: COLORS.darkBlue,
        fill: { color: COLORS.lighterBlue },
        border: { type: 'none' }
    });



    return pptx.writeFile({ fileName: 'generated/presentations/slide3_general_pyramid.pptx' });
}


module.exports = createPresentation;

createPresentation().then(fileName => console.log(`Presentation created: ${fileName}`));

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
