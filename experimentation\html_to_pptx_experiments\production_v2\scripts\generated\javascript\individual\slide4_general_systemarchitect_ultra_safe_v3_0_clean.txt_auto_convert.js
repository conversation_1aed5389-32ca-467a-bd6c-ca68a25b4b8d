const PptxGenJS = require('pptxgenjs');


function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // =======================================================================
    // 1. CENTRALIZED CONSTANTS & CONFIGURATION
    // =======================================================================

    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0; // Higher start for dense content
    const MAX_CONTENT_Y = 4.8;

    const COLORS = {
        title: '1A237E',
        rowLabel: '1A237E',
        rowLabelBg: 'E8F0FE',
        boxBg: 'F8F9FA',
        boxBorder: 'DEE2E6',
        boxTitle: '3C4043',
        boxSubtitle: '5F6368',
        interactionsTitle: '1A237E',
        interactionsText: '3C4043',
        takeawayBg: 'E8F0FE',
        takeawayBorder: '1A73E8',
        takeawayTitle: '1A237E',
        takeawayText: '3C4043'
    };

    const FONT_SIZES = {
        title: 16,
        rowLabel: 10,
        boxTitle: 9,
        boxSubtitle: 8,
        interactionsTitle: 12,
        interactionsText: 9,
        takeawayTitle: 12,
        takeawayText: 10
    };


    // =======================================================================
    // 2. HELPER FUNCTIONS
    // =======================================================================

    function addImageWithFallback(slide, imagePath, options, fallbackText) {
        try {
            slide.addImage({ ...options, path: imagePath, sizing: { type: 'contain', ...options } });
        } catch (error) {
            slide.addShape(pptx.shapes.RECTANGLE, { ...options, fill: { color: 'F3F4F6' }, line: { color: 'D1D5DB', width: 1 }, rectRadius: 0.05 });
            slide.addText(fallbackText, { ...options, x: options.x + 0.1, y: options.y + options.h / 2 - 0.1, w: options.w - 0.2, fontSize: 8, color: '6B7280', align: 'center', valign: 'middle' });
        }
    }


    // =======================================================================
    // 3. SLIDE CONTENT
    // =======================================================================

    slide.background = { color: 'FFFFFF' };

    let currentY = SAFE_MARGIN;

    // Title
    slide.addText('Future-State System Architecture on AWS', { x: SAFE_MARGIN, y: currentY, w: SLIDE_WIDTH - 2 * SAFE_MARGIN, h: 0.5, fontSize: FONT_SIZES.title, color: COLORS.title, bold: true });
    currentY += 0.7;

    // Architecture Diagram
    const archRows = [
        { label: 'Edge & Access', boxes: [
            { img: 'https://www.svgrepo.com/show/433909/user-group.svg', title: 'End Users / Partner Systems' },
            { img: 'https://storage.slide-gen.com/aws_icon/Networking-Content-Delivery/AWS-Networking-Content-Delivery-Route-53.svg', title: 'Amazon Route 53', subtitle: 'DNS' },
            { img: 'https://storage.slide-gen.com/aws_icon/Networking-Content-Delivery/AWS-Networking-Content-Delivery-CloudFront.svg', title: 'Amazon CloudFront', subtitle: 'CDN' },
            { img: 'https://storage.slide-gen.com/aws_icon/Security-Identity-Compliance/AWS-Security-Identity-Compliance-WAF.svg', title: 'AWS WAF', subtitle: 'Layer 7 Protection' },
            { img: 'https://storage.slide-gen.com/aws_icon/App-Integration/AWS-App-Integration-API-Gateway.svg', title: 'Amazon API Gateway', subtitle: 'REST/HTTP Endpoints' }
        ]},
        // ... other rows
    ];

    const boxWidth = (SLIDE_WIDTH - 2 * SAFE_MARGIN - 0.4 * 5) / 5; // Adjust for label and gaps
    const rowHeight = 1.2;

    archRows.forEach(row => {
        // Row Label
        slide.addText(row.label, { x: SAFE_MARGIN, y: currentY, w: 0.4, h: rowHeight, fontSize: FONT_SIZES.rowLabel, color: COLORS.rowLabel, bold: true, valign: 'middle', rotate: 270, fill: { color: COLORS.rowLabelBg } });

        let boxX = SAFE_MARGIN + 0.5;
        row.boxes.forEach(box => {
            slide.addShape(pptx.shapes.RECTANGLE, { x: boxX, y: currentY, w: boxWidth, h: rowHeight, fill: { color: COLORS.boxBg }, line: { color: COLORS.boxBorder, width: 1 }, rectRadius: 0.1 });
            let boxContentY = currentY + 0.1;
            addImageWithFallback(slide, box.img, { x: boxX + 0.1, y: boxContentY, w: 0.3, h: 0.3 }, 'Icon');
            boxContentY += 0.4;
            slide.addText(box.title, { x: boxX + 0.1, y: boxContentY, w: boxWidth - 0.2, h: 0.2, fontSize: FONT_SIZES.boxTitle, color: COLORS.boxTitle, bold: true, align: 'center' });
            if (box.subtitle) {
                boxContentY += 0.3;
                slide.addText(box.subtitle, { x: boxX + 0.1, y: boxContentY, w: boxWidth - 0.2, h: 0.2, fontSize: FONT_SIZES.boxSubtitle, color: COLORS.boxSubtitle, align: 'center' });
            }
            boxX += boxWidth + 0.1;
        });

        currentY += rowHeight + 0.2;
    });

    // ... rest of the content (Key Interactions, Takeaway) using similar approach

    return pptx.writeFile({ fileName: 'generated/presentations/slide4_general_systemarchitect.pptx' });
}


module.exports = createPresentation;

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
