import google.genai as genai
from google.genai import types
from typing import List, Union
from PIL import Image
from llm.providers.tavily_image_search import image_tavily
import random
from google.genai.errors import ServerError
import io
import asyncio # Crucial for running blocking API calls in a thread
from loguru import logger

class Gemini_LLM:
    def __init__(self, api_key: str, model: str = 'gemini-pro', temperature: float = 0,
                  max_retries: int = 2, base_backoff_delay: float = 10.0):
        self.model = model # Renamed to avoid confusion with `self.model` instance
        self.temperature = temperature
        # Directly get the GenerativeModel instance
        self.client = genai.Client(api_key=api_key)
        self.max_retries = max_retries
        self.base_backoff_delay = base_backoff_delay # Initial delay in seconds
        self.max_backoff_delay = 45.0 # Maximum delay between retries in seconds

    async def _api_call_with_retries(self, func, *args, **kwargs):
        """
        Helper method to perform an API call with retries and exponential backoff.
        Catches ServiceUnavailable (503) and ResourceExhausted (429) errors.

        Args:
            func: The callable (e.g., self.client.models.generate_content) to execute.
            *args: Positional arguments for func.
            **kwargs: Keyword arguments for func.
        """
        for attempt in range(self.max_retries + 1): # +1 to include the initial attempt
            try:
                # Execute the blocking call in a separate thread
                response = await asyncio.to_thread(func, *args, **kwargs)
                return response
            
            except ServerError as e:
                if e.code in [503, 429]:
                    if attempt < self.max_retries:
                        delay = min(self.base_backoff_delay * (2 ** attempt), self.max_backoff_delay)
                        jitter = random.uniform(0, delay * 0.1) # Add up to 10% jitter to prevent thundering herd problem
                        sleep_time = delay + jitter
                        logger.error(f"[Error] {e} : Retrying in {sleep_time:.2f} seconds (attempt {attempt + 1}/{self.max_retries}).")
                        await asyncio.sleep(sleep_time)
                    else:
                        logger.error(f"[Error] {e} : Retries exceeded.")
                        raise # Re-raise the last exception if all retries are exhausted
                else:
                    logger.error(f"[Error] {e} : Non-retryable API error.")

            except Exception as e:
                logger.debug(f"[Error] : An unexpected error occurred during LLM call: {e}...")
                raise # Re-raise immediately for unhandled exceptions

    # Make this method async
    async def call(self, query: str):
        # Configure generation settings without tools for basic text generation
        config = genai.types.GenerateContentConfig(
            temperature=self.temperature,
            thinking_config=types.ThinkingConfig(thinking_budget=128),
            tools=[image_tavily]
        )

        response = await self._api_call_with_retries(
            self.client.models.generate_content,
            model=self.model,
            contents=query,
            config=config
        )

        # Log if the LLM made any tool calls (including Tavily)
        if hasattr(response, 'candidates') and response.candidates:
            for candidate in response.candidates:
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    for part in candidate.content.parts:
                        if hasattr(part, 'function_call') and part.function_call:
                            function_name = part.function_call.name
                            if function_name == 'image_tavily':
                                args = dict(part.function_call.args) if part.function_call.args else {}
                                search_term = args.get('search_term', 'unknown')
                                logger.info(f"🤖 LLM called Tavily function with search term: '{search_term}'")

        # Extract and return the text.
        # Check if usage_metadata exists before accessing to prevent AttributeError
        input_token_count = response.usage_metadata.prompt_token_count if hasattr(response, 'usage_metadata') and response.usage_metadata else None
        output_token_count = response.usage_metadata.candidates_token_count if hasattr(response, 'usage_metadata') and response.usage_metadata else None

        return {
            'text': response.text,
            'input_token_count': input_token_count,
            'output_token_count': output_token_count
        }


    # Make this method async
    async def call_with_images(self, query: str, images: Union[List[Image.Image], Image.Image]):
        # Ensure images is a list for consistent handling
        if not isinstance(images, list):
            images = [images]

        # Convert pillow images to byte array then wrap in typing
        payload = [types.Part.from_bytes(data=self.image_to_byte_array(img),
                                         mime_type="image/jpeg")
                   for img in images]

        # Combine text and image content for the model
        contents = [query] + payload

        config = types.GenerateContentConfig(  # Use config for temperature
            temperature=self.temperature,
            thinking_config=types.ThinkingConfig(thinking_budget=128)
        )

        response = await self._api_call_with_retries(
            self.client.models.generate_content, # Pass the method reference itself
            model=self.model,                    # Pass the model name
            contents=contents,                   # Pass keyword arguments
            config=config
        )

        return {'text': response.text}


    def image_to_byte_array(self, img: Image.Image) -> bytes:
        img_byte_arr = io.BytesIO()
        if img.mode in ("RGBA", "P"): 
            img = img.convert("RGB")
        img.save(img_byte_arr, format='JPEG')
        img_byte_arr = img_byte_arr.getvalue()

        return img_byte_arr