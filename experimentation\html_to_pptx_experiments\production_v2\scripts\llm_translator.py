#!/usr/bin/env python3
"""
Production LLM Translator
Enhanced HTML-to-JavaScript translation module using comprehensive LLM infrastructure
Leverages existing llm.llmwrapper and pptx_generation.html_to_pptx_translator modules
"""

import sys
import os
from pathlib import Path
from typing import Optional

# Add root directory to path for proper module imports
script_dir = Path(__file__).parent
root_dir = script_dir.parent.parent.parent.parent  # Go up to workspace root
sys.path.insert(0, str(root_dir))

try:
    from dotenv import load_dotenv

    # Load environment variables
    env_paths = ["../../../local.env", "../../../.env", "../../local.env", "../../.env", "../local.env", "../.env", "local.env", ".env"]
    for env_path in env_paths:
        if os.path.exists(env_path):
            load_dotenv(env_path)
            print(f"✅ Environment loaded from: {env_path}", file=sys.stderr)
            break

    # Import comprehensive modules directly
    from llm.llmwrapper import LLM
    from pptx_generation.html_to_pptx_translator import llm_html_to_pptxgenjs_single

except ImportError as e:
    print(f"❌ Import error: {e}", file=sys.stderr)
    sys.exit(1)

class ProductionLLMTranslator:
    """
    Production-ready HTML-to-JavaScript translator
    Uses comprehensive LLM infrastructure and proven translation functions
    """

    def __init__(self, provider_mode: str = "vertex"):
        """
        Initialize the translator with specified provider mode

        Args:
            provider_mode: "vertex" for Vertex AI (more reliable) or "gemini" for direct Gemini API (has thinking budget)
        """
        self.llm = None
        self.provider_mode = provider_mode.lower()
        self.prompt_dir = Path(__file__).parent.parent / "prompts"
        # Lazy initialization - only initialize LLM when needed
        # Optional analysis payload from router (colors/visuals/layout)
        self.last_analysis = None

    def _initialize_llm(self):
        """Initialize LLM with optimal settings for translation"""
        try:
            if self.provider_mode == "vertex":
                # Use Vertex AI HTML translator provider (more reliable than direct Gemini API)
                # Use Gemini 1.5 Pro (available in Vertex AI) for high-quality HTML-to-JavaScript translation
                self.llm = LLM(provider="vertex_html_translator", model="gemini-1.5-pro")
                print(f"✅ LLM initialized: Vertex AI (more reliable)", file=sys.stderr)
            elif self.provider_mode == "gemini":
                # Use direct Gemini API (has thinking budget but less reliable)
                # Use Gemini 1.5 Pro for stable HTML-to-JavaScript translation (2.5-pro has empty response issues)
                self.llm = LLM(provider="gemini_html_translator", model="gemini-1.5-pro")
                print(f"✅ LLM initialized: Direct Gemini API (with thinking budget)", file=sys.stderr)
            else:
                raise ValueError(f"Invalid provider_mode: {self.provider_mode}. Use 'vertex' or 'gemini'")

            print(f"🔧 Provider: {self.llm.provider}", file=sys.stderr)
        except Exception as e:
            print(f"❌ Failed to initialize LLM: {e}", file=sys.stderr)
            sys.exit(1)
    
    def load_prompt(self, prompt_name: str) -> str:
        """
        Load a prompt template from the production prompts directory
        
        Args:
            prompt_name: Name of the prompt file (without extension)
            
        Returns:
            Prompt template content
        """
        prompt_file = self.prompt_dir / f"{prompt_name}.txt"
        
        if not prompt_file.exists():
            # Fallback to ultra_safe if requested prompt doesn't exist
            prompt_file = self.prompt_dir / "ultra_safe.txt"
            print(f"⚠️ Prompt '{prompt_name}' not found, using ultra_safe fallback", file=sys.stderr)
        
        if not prompt_file.exists():
            raise FileNotFoundError(f"No prompt files found in {self.prompt_dir}")
        
        return prompt_file.read_text(encoding='utf-8')
    
    def format_prompt(self, template: str, html_content: str, slide_name: str,
                     output_directory: str = "generated/presentations",
                     analysis: dict | None = None) -> str:
        """
        Format a prompt template with actual values
        Supports both {{variable}} and {VARIABLE} formats

        Args:
            template: Prompt template string
            html_content: HTML content to translate
            slide_name: Name of the slide for context
            output_directory: Directory for output files
            analysis: Optional analyzer JSON to embed for color/visual hints

        Returns:
            Formatted prompt string
        """
        # Support both template formats
        formatted = template.replace("{HTML_CONTENT}", html_content) \
                           .replace("{SLIDE_NAME}", slide_name) \
                           .replace("{OUTPUT_DIRECTORY}", output_directory)

        # Also support {{variable}} format used in some templates
        formatted = formatted.replace("{{html_content}}", html_content) \
                            .replace("{{slide_name}}", slide_name) \
                            .replace("{{output_directory}}", output_directory)

        # Basic analysis injection (no color enhancement complexity)
        if analysis is not None:
            import json
            analysis_str = json.dumps(analysis, ensure_ascii=False)
            formatted = formatted.replace("{{analysis}}", analysis_str)
            formatted = formatted.replace("{ANALYSIS}", analysis_str)

        # Remove any color enhancement placeholders (simplified approach)
        formatted = formatted.replace("{COLOR_ENHANCEMENT_BLOCK}", "")
        formatted = formatted.replace("{{color_enhancement}}", "")

        return formatted

    async def translate_html_to_js(self, html_content: str, slide_name: str,
                                  prompt_name: str = "ultra_safe") -> str:
        """
        Translate HTML content to PptxGenJS JavaScript code using comprehensive translator

        Args:
            html_content: HTML content to translate
            slide_name: Name of the slide for context
            prompt_name: Name of the prompt template to use

        Returns:
            Generated JavaScript code
        """
        try:
            # Initialize LLM only when needed
            if self.llm is None:
                self._initialize_llm()

            print(f"🔄 Starting translation for: {slide_name}", file=sys.stderr)
            print(f"🎯 Using prompt: {prompt_name}", file=sys.stderr)

            # Check if we should use custom prompt or default translator
            if prompt_name != "default":
                # Use custom prompt with direct LLM call
                return await self._translate_with_custom_prompt(html_content, slide_name, prompt_name)
            else:
                # Use the comprehensive translator function
                print(f"🔧 Using comprehensive translator function", file=sys.stderr)
                js_code = await llm_html_to_pptxgenjs_single(
                    html_content=html_content,
                    slide_name=slide_name,
                    llm=self.llm
                )

                print(f"✅ Translation completed: {len(js_code)} characters", file=sys.stderr)
                return js_code

        except Exception as e:
            print(f"❌ Translation failed for {slide_name}: {e}", file=sys.stderr)
            raise e

    async def _translate_with_custom_prompt(self, html_content: str, slide_name: str, prompt_name: str) -> str:
        """
        Translate using a custom prompt template

        Args:
            html_content: HTML content to translate
            slide_name: Name of the slide for context
            prompt_name: Name of the prompt template to use

        Returns:
            Generated JavaScript code
        """
        # Load and format the prompt template
        prompt_template = self.load_prompt(prompt_name)
        formatted_prompt = self.format_prompt(
            template=prompt_template,
            html_content=html_content,
            slide_name=slide_name,
            output_directory="generated/presentations",
            analysis=self.last_analysis,
        )

        # Call LLM with formatted prompt
        response = await self.llm.call(query=formatted_prompt)

        # Extract code from response
        if isinstance(response, dict) and "response" in response:
            js_code = response["response"]
        elif isinstance(response, dict) and "text" in response:
            js_code = response["text"]
        else:
            js_code = str(response)

        # Clean up the response
        js_code = self._clean_javascript_response(js_code)

        # Validate the generated code
        self._validate_javascript_code(js_code, slide_name)

        return js_code
    
    def _clean_javascript_response(self, js_code: str) -> str:
        """
        Clean up LLM response to extract pure JavaScript code
        
        Args:
            js_code: Raw LLM response
            
        Returns:
            Cleaned JavaScript code
        """
        # Remove markdown code blocks
        js_code = js_code.replace('```javascript', '').replace('```js', '').replace('```', '')
        js_code = js_code.strip()
        
        # Clean up any existing PptxGenJS imports/requires to prevent duplicates
        lines = js_code.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Remove any line that imports or requires PptxGenJS
            if ('import' in line and 'PptxGenJS' in line and 'pptxgenjs' in line.lower()) or \
               ('require(' in line and 'PptxGenJS' in line and 'pptxgenjs' in line.lower()) or \
               ('const PptxGenJS' in line) or \
               ('import PptxGenJS' in line):
                print(f"🧹 Removed duplicate import: {line.strip()}", file=sys.stderr)
                continue  # Skip this line
            else:
                cleaned_lines.append(line)
        
        js_code = '\n'.join(cleaned_lines)
        
        # Remove any descriptive text that's not JavaScript code
        # Look for the first function or const declaration and start from there
        lines = js_code.split('\n')
        js_start_index = 0
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if (stripped.startswith('function ') or
                stripped.startswith('const ') or
                stripped.startswith('var ') or
                stripped.startswith('let ') or
                stripped.startswith('//') or
                stripped.startswith('/*') or
                stripped == ''):
                js_start_index = i
                break
        
        if js_start_index > 0:
            print(f"🧹 Removed {js_start_index} lines of non-JavaScript content", file=sys.stderr)
            js_code = '\n'.join(lines[js_start_index:])
        
        return js_code
    
    def _validate_javascript_code(self, js_code: str, slide_name: str) -> None:
        """
        Validate the generated JavaScript code

        Args:
            js_code: Generated JavaScript code
            slide_name: Name of the slide for error context

        Raises:
            ValueError: If code validation fails
        """
        if not js_code or len(js_code.strip()) < 50:
            raise ValueError(f"Generated JavaScript code for {slide_name} is too short or empty")

        if '<coroutine object' in js_code:
            raise ValueError(f"Generated JavaScript for {slide_name} contains unawaited coroutine objects")

        # Check for basic PptxGenJS structure
        if 'addSlide' not in js_code and 'pptx.addSlide' not in js_code:
            print(f"⚠️ Warning: {slide_name} - No slide creation detected in generated code", file=sys.stderr)

        if 'addText' not in js_code:
            print(f"⚠️ Warning: {slide_name} - No text elements detected in generated code", file=sys.stderr)

        print(f"✅ Validation passed for {slide_name}", file=sys.stderr)

    def get_available_prompts(self) -> list:
        """
        Get list of available prompt templates

        Returns:
            List of available prompt names
        """
        if not self.prompt_dir.exists():
            return []

        return [f.stem for f in self.prompt_dir.glob("*.txt")]

    async def translate_with_fallback(self, html_content: str, slide_name: str,
                                    primary_prompt: str = "balanced",
                                    fallback_prompt: str = "ultra_safe") -> str:
        """
        Translate with automatic fallback to safer prompt on failure

        Args:
            html_content: HTML content to translate
            slide_name: Name of the slide for context
            primary_prompt: Primary prompt to try first
            fallback_prompt: Fallback prompt if primary fails

        Returns:
            Generated JavaScript code
        """
        try:
            return await self.translate_html_to_js(html_content, slide_name, primary_prompt)
        except Exception as e:
            print(f"⚠️ Primary prompt {primary_prompt} failed for {slide_name}: {e}", file=sys.stderr)
            print(f"🔄 Trying fallback prompt: {fallback_prompt}", file=sys.stderr)

            try:
                return await self.translate_html_to_js(html_content, slide_name, fallback_prompt)
            except Exception as fallback_error:
                print(f"❌ Fallback prompt {fallback_prompt} also failed: {fallback_error}", file=sys.stderr)

                # Final fallback to comprehensive translator
                print(f"🔄 Using comprehensive translator as final fallback", file=sys.stderr)
                if self.llm is None:
                    self._initialize_llm()

                return await llm_html_to_pptxgenjs_single(
                    html_content=html_content,
                    slide_name=slide_name,
                    llm=self.llm
                )
