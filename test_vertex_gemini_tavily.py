#!/usr/bin/env python3
"""
Test script to verify Vertex AI Gemini provider with Tavily integration
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from llm.providers.vertex_gemini import Vertex_Gemini_LLM

async def test_vertex_gemini_tavily():
    """Test the Vertex AI Gemini provider with Tavily integration."""
    
    print("🧪 Testing Vertex AI Gemini provider with Tavily integration...")
    
    try:
        # Initialize the provider
        llm = Vertex_Gemini_LLM(
            model="gemini-2.5-flash",
            temperature=0.1,
            enable_tavily=True
        )
        
        print(f"✅ Provider initialized successfully")
        print(f"🔧 Tavily enabled: {llm.is_tavily_enabled()}")
        print(f"📍 Model path: {llm._model_path()}")
        
        # Test basic text generation (should work without Tavily)
        print("\n📝 Testing basic text generation...")
        response = await llm.call("What is artificial intelligence?")
        
        print(f"✅ Response received: {len(response['text'])} characters")
        print(f"📊 Input tokens: {response.get('input_token_count', 'N/A')}")
        print(f"📊 Output tokens: {response.get('output_token_count', 'N/A')}")
        print(f"📄 Response preview: {response['text'][:200]}...")
        
        # Test with a query that might trigger Tavily (if available)
        print("\n🔍 Testing query that might use Tavily...")
        response2 = await llm.call("Show me an image of a modern office workspace")
        
        print(f"✅ Response received: {len(response2['text'])} characters")
        print(f"📊 Input tokens: {response2.get('input_token_count', 'N/A')}")
        print(f"📊 Output tokens: {response2.get('output_token_count', 'N/A')}")
        print(f"📄 Response preview: {response2['text'][:200]}...")
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_vertex_gemini_tavily())
    sys.exit(0 if success else 1)
