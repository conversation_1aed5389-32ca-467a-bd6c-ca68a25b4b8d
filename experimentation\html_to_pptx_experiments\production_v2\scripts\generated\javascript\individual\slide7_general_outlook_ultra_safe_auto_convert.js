const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE CANVAS BOUNDARIES
    const SLIDE_WIDTH = 10;
    const SLIDE_HEIGHT = 5.625;
    const SAFE_MARGIN = 0.5;
    const TITLE_Y = 0.4;
    const TITLE_H = 0.5;
    const CONTENT_START_Y = 1.2;
    const MAX_CONTENT_Y = 5.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN);

    // Two-column layout
    const LEFT_COL_X = SAFE_MARGIN;
    const COL_W = 4.4;
    const RIGHT_COL_X = LEFT_COL_X + COL_W + 0.2;

    // Add Slide Title
    slide.addText("Conclusion & Future Outlook: Powering a Sustainable Future", {
        x: LEFT_COL_X,
        y: TITLE_Y,
        w: CONTENT_WIDTH,
        h: TITLE_H,
        fontSize: 20,
        bold: true,
        color: '334155' // text-gray-800
    });

    // --- Left Column: Key Takeaways ---
    let currentY = CONTENT_START_Y;

    // Column Title
    slide.addText("Key Takeaways", {
        x: LEFT_COL_X,
        y: currentY,
        w: COL_W,
        h: 0.4,
        fontSize: 16,
        bold: true,
        color: '0e7490' // text-cyan-700
    });

    // Bottom border for title
    slide.addShape(pptx.shapes.LINE, {
        x: LEFT_COL_X,
        y: currentY + 0.35,
        w: COL_W,
        h: 0,
        line: { color: 'a5f3fc', width: 2 } // border-cyan-200
    });

    currentY += 0.6;

    // List items
    const takeaways = [
        { text: "Substantial growth in 2023, driven by solar and wind power.", icon: "✓", color: "0891b2" },
        { text: "Record investment levels, especially in offshore wind and energy storage.", icon: "✓", color: "0891b2" },
        { text: "Declining LCOE for renewables enhances their market competitiveness.", icon: "✓", color: "0891b2" },
        { text: "Policy support remains crucial for accelerating deployment and achieving climate goals.", icon: "✓", color: "0891b2" },
        { text: "Supply chain resilience and grid modernization are key challenges to address.", icon: "!", color: "ef4444" }
    ];

    takeaways.forEach(item => {
        if (currentY < MAX_CONTENT_Y - 0.3) {
            // Icon
            slide.addText(item.icon, {
                x: LEFT_COL_X,
                y: currentY,
                w: 0.3,
                h: 0.3,
                fontSize: 14,
                bold: true,
                color: item.color,
                valign: 'top'
            });
            // Text
            slide.addText(item.text, {
                x: LEFT_COL_X + 0.3,
                y: currentY,
                w: COL_W - 0.3,
                h: 0.5,
                fontSize: 10,
                color: '374151', // text-gray-700
                valign: 'top'
            });
            currentY += 0.6;
        }
    });

    // --- Right Column: Future Outlook ---
    currentY = CONTENT_START_Y;

    // Vertical separator line
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X - 0.1,
        y: currentY,
        w: 0,
        h: 3.6,
        line: { color: 'e5e7eb', width: 2 } // border-gray-200
    });

    // Column Title
    slide.addText("Future Outlook", {
        x: RIGHT_COL_X,
        y: currentY,
        w: COL_W,
        h: 0.4,
        fontSize: 16,
        bold: true,
        color: '0f766e' // text-teal-700
    });

    // Bottom border for title
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X,
        y: currentY + 0.35,
        w: COL_W,
        h: 0,
        line: { color: '99f6e4', width: 2 } // border-teal-200
    });

    currentY += 0.6;

    // List items
    const outlooks = [
        { text: "Projected Growth: Capacity expected to reach 5000 GW by 2025 (IEA).", icon: "📈" },
        { text: "Emerging Technologies: Green hydrogen, advanced storage, and floating offshore wind will be significant.", icon: "💡" },
        { text: "Market Trends: Rise of corporate PPAs, EV demand, and smart grid integration.", icon: "📊" }
    ];

    outlooks.forEach(item => {
        if (currentY < MAX_CONTENT_Y - 1.5) { // Reserve space for alert box
            // Icon (using text for safety)
            slide.addText(item.icon, {
                x: RIGHT_COL_X,
                y: currentY,
                w: 0.3,
                h: 0.3,
                fontSize: 14,
                color: '0d9488', // text-teal-600
                valign: 'top'
            });
            // Text
            slide.addText(item.text, {
                x: RIGHT_COL_X + 0.3,
                y: currentY,
                w: COL_W - 0.3,
                h: 0.6,
                fontSize: 10,
                color: '374151', // text-gray-700
                valign: 'top'
            });
            currentY += 0.7;
        }
    });

    // Alert Box at the bottom of the right column
    const alertY = MAX_CONTENT_Y - 1.0;
    const alertH = 0.9;

    // Background shape for alert
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: RIGHT_COL_X,
        y: alertY,
        w: COL_W,
        h: alertH,
        fill: { color: 'f0fdfa' } // bg-teal-50
    });

    // Left border for alert
    slide.addShape(pptx.shapes.LINE, {
        x: RIGHT_COL_X,
        y: alertY,
        w: 0,
        h: alertH,
        line: { color: '2dd4bf', width: 4 } // border-teal-500
    });

    // Alert text
    slide.addText("Renewable energy is poised to play an increasingly dominant role in the global energy mix, powering a sustainable and prosperous future for all.", {
        x: RIGHT_COL_X + 0.2,
        y: alertY + 0.1,
        w: COL_W - 0.3,
        h: alertH - 0.2,
        fontSize: 10,
        bold: true,
        color: '134e4a', // text-teal-800
        valign: 'middle'
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide7_general_outlook.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
