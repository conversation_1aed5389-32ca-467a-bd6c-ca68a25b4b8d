<!DOCTYPE html><html><head>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&amp;display=swap" rel="stylesheet">
<style>
    body, html {
        margin: 0;
        padding: 0;
        font-family: 'Roboto', sans-serif;
        background-color: #f0f0f0;
    }
    .slide-container {
        width: 1280px;
        height: 720px;
        background-color: #ffffff;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
    }
    .slide-content {
        width: 100%;
        height: 100%;
        padding: 40px 60px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
    }
    .slide-title {
        font-size: 2.5em;
        font-weight: 700;
        color: #1a237e;
        margin-bottom: 20px;
    }
    .arch-diagram {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 15px;
    }
    .arch-row {
        display: flex;
        gap: 8px;
        align-items: stretch;
    }
    .arch-box {
        flex: 1;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-height: 100px;
    }
    .arch-box img {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;
    }
    .arch-box-title {
        font-size: 0.8em;
        font-weight: 500;
        color: #3c4043;
    }
    .arch-box-subtitle {
        font-size: 0.65em;
        color: #5f6368;
    }
    .arch-row-label {
        writing-mode: vertical-rl;
        transform: rotate(180deg);
        text-align: center;
        font-weight: 700;
        color: #1a237e;
        background-color: #e8f0fe;
        padding: 10px 5px;
        border-radius: 8px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .bottom-content {
        display: flex;
        width: 100%;
        gap: 20px;
        flex-grow: 1;
    }
    .key-interactions {
        flex-basis: 65%;
    }
    .key-interactions-title {
        font-size: 1.1em;
        font-weight: 500;
        color: #1a237e;
        margin-bottom: 10px;
    }
    .key-interactions ul {
        margin: 0;
        padding-left: 20px;
        font-size: 0.8em;
        line-height: 1.5;
        color: #3c4043;
    }
    .takeaway {
        flex-basis: 35%;
        background-color: #e8f0fe;
        border-left: 4px solid #1a73e8;
        padding: 15px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .takeaway-title {
        font-size: 1.1em;
        font-weight: 500;
        color: #1a237e;
        margin-bottom: 5px;
    }
    .takeaway-text {
        font-size: 0.85em;
        line-height: 1.5;
        color: #3c4043;
    }
</style>
</head>
<body>

<div class="slide-container">
    <div class="slide-content">
        <h1 class="slide-title" style="margin-top: 8.8px; margin-bottom: 8px;">Future-State System Architecture on AWS</h1>
        
        <div class="arch-diagram" style="margin-bottom: 8px; font-size: 14.3255px;">
            <!-- Row 1: Edge & Access -->
            <div class="arch-row" style="font-size: 14.1822px;">
                <div class="arch-row-label" style="padding-top: 8px; padding-bottom: 8px; font-size: 14.0404px;">Edge &amp; Access</div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://www.svgrepo.com/show/433909/user-group.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">End Users / Partner Systems</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Networking-Content-Delivery/AWS-Networking-Content-Delivery-Route-53.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon Route 53</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">DNS</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Networking-Content-Delivery/AWS-Networking-Content-Delivery-CloudFront.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon CloudFront</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">CDN</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Security-Identity-Compliance/AWS-Security-Identity-Compliance-WAF.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">AWS WAF</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">Layer 7 Protection</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/App-Integration/AWS-App-Integration-API-Gateway.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon API Gateway</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">REST/HTTP Endpoints</div>
                </div>
            </div>

            <!-- Row 2: Request Processing & Compute -->
            <div class="arch-row" style="font-size: 14.1822px;">
                <div class="arch-row-label" style="padding-top: 8px; padding-bottom: 8px; font-size: 14.0404px;">Compute</div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Compute/AWS-Compute-Lambda.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">AWS Lambda</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">New Microservices</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Networking-Content-Delivery/AWS-Networking-Content-Delivery-Elastic-Load-Balancing.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Application Load Balancer</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">HTTP Routing</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Containers/AWS-Containers-ECS-Anywhere.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon ECS/EKS</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">Refactored Services</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Compute/AWS-Compute-EC2.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon EC2</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">COBOL Runtime (Legacy)</div>
                </div>
            </div>

            <!-- Row 3: Data & Storage -->
            <div class="arch-row" style="font-size: 14.1822px;">
                <div class="arch-row-label" style="padding-top: 8px; padding-bottom: 8px; font-size: 14.0404px;">Data</div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Database/AWS-Database-Aurora.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon Aurora (RDS)</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">Transactional</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Database/AWS-Database-DynamoDB.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon DynamoDB</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">NoSQL / High-Scale</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Storage/AWS-Storage-S3-on-Outposts.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon S3</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">Files, Reports, Archives</div>
                </div>
            </div>

            <!-- Row 4: Integration & Ops -->
            <div class="arch-row" style="font-size: 14.1822px;">
                <div class="arch-row-label" style="padding-top: 8px; padding-bottom: 8px; font-size: 14.0404px;">Integration &amp; Ops</div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Games/AWS-Games-Lumberyard.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon SQS</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">Async Queues</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Management-Governance/AWS-Management-Governance-CloudWatch.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">Amazon CloudWatch</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">Metrics, Logs, Alarms</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Machine-Learning/AWS-Machine-Learning-Lookout-for-Metrics.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">AWS KMS</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">Encryption at Rest</div>
                </div>
                <div class="arch-box" style="padding: 8px; font-size: 14.0404px;">
                    <img src="https://storage.slide-gen.com/aws_icon/Security-Identity-Compliance/AWS-Security-Identity-Compliance-Secrets-Manager.svg" style="width: 18.3444px; height: 18.3444px; font-size: 13.9px;">
                    <div class="arch-box-title" style="font-size: 11.1199px;">AWS Secrets Manager</div>
                    <div class="arch-box-subtitle" style="font-size: 9.03496px;">Credentials Rotation</div>
                </div>
            </div>
        </div>

        <div class="bottom-content" style="font-size: 14.3255px;">
            <div class="key-interactions" style="font-size: 14.1822px;">
                <h2 class="key-interactions-title" style="margin-top: 8.608px; margin-bottom: 8px; font-size: 15.4443px;">Key Interactions &amp; Data Flow</h2>
                <ul style="line-height: 16.6025px; padding-left: 8px; font-size: 11.2322px;">
                    <li style="line-height: 16.5195px; font-size: 11.1199px;">Route 53 directs traffic to CloudFront; WAF inspects requests before they reach the API Gateway.</li>
                    <li style="line-height: 16.5195px; font-size: 11.1199px;">API Gateway routes requests to Lambda for new services or an ALB for containerized and legacy COBOL workloads.</li>
                    <li style="line-height: 16.5195px; font-size: 11.1199px;">Services use Aurora for transactions, DynamoDB for high-velocity data, and S3 for object storage.</li>
                    <li style="line-height: 16.5195px; font-size: 11.1199px;">SQS decouples components, while CloudWatch, KMS, and Secrets Manager provide observability and security.</li>
                </ul>
            </div>
            <div class="takeaway" style="padding: 8px; font-size: 14.1822px;">
                <h2 class="takeaway-title" style="margin-top: 8.608px; font-size: 15.4443px;">Architectural Takeaway</h2>
                <p class="takeaway-text" style="line-height: 17.6401px; margin-top: 8.6px; margin-bottom: 8.6px; font-size: 11.9344px;">This layered, service-oriented architecture enables phased modernization, isolates the legacy core, and improves security, scalability, and resilience.</p>
            </div>
        </div>
    </div>
</div>


</body></html>