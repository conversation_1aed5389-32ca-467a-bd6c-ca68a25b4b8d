const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE DEFAULTS & CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const CONTENT_WIDTH = 10.0 - (2 * SAFE_MARGIN); // 9.4, but we'll use a safer 8.2
    const ICON_TEXT_MARGIN = 0.3;

    // COLOR PALETTE (from HTML analysis)
    const BG_COLOR = '0a192f';
    const ACCENT_COLOR = '64ffda';
    const TEXT_COLOR = 'ccd6f6';
    const TEXT_BOLD_COLOR = 'a8b2d1';

    // Set slide background color
    slide.background = { color: BG_COLOR };

    // TITLE
    slide.addText("Fortifying Our Defenses: A Zero Trust Approach", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16, // Ultra-safe max title size
        color: ACCENT_COLOR,
        bold: true,
        align: 'left',
    });

    // TITLE DIVIDER (using a safe RECTANGLE shape)
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.2, // 120px is ~1.2 inches
        h: 0.03, // 3px is ~0.03 inches
        fill: { color: ACCENT_COLOR },
    });

    // AGENDA CONTENT
    const agendaItems = [
        { bold: "The Challenge:", text: "Current security relies on outdated \"trust but verify\" models, leaving us vulnerable to sophisticated attacks and insider threats." },
        { bold: "The Solution:", text: "Implement a comprehensive Zero Trust security framework, assuming no user or device is inherently trustworthy." },
        { bold: "Key Components:", text: "Strong Identity & Access Management, Device Security, Network Microsegmentation, and Data Protection." },
        { bold: "Cloud-First Strategy:", text: "Leverage cloud services for scalability, cost-effectiveness, and advanced security features." },
        { bold: "Benefits:", text: "Reduced risk of breaches, improved compliance, enhanced data protection, and increased operational efficiency." },
        { bold: "Call to Action:", text: "Invest in a phased Zero Trust implementation to secure our sensitive data and prevent cyberattacks." }
    ];

    // DYNAMIC VERTICAL POSITIONING
    let currentY = CONTENT_START_Y + 0.2; // Start slightly lower after divider
    const ITEM_HEIGHT = 0.6; // Allocate generous space for each item to prevent overlap

    // Function to add text safely and prevent overflow
    function addTextSafely(slide, text, options) {
        if ((options.y + options.h) > MAX_CONTENT_Y) {
            console.warn(`Skipping element to prevent vertical overflow: ${text.substring(0, 30)}...`);
            return false;
        }
        slide.addText(text, options);
        return true;
    }

    // Loop through agenda items and add them to the slide
    agendaItems.forEach(item => {
        const iconX = SAFE_MARGIN;
        const textX = iconX + ICON_TEXT_MARGIN;
        const textW = 8.2 - ICON_TEXT_MARGIN;

        // Add Icon (using ultra-safe text-based checkmark)
        addTextSafely(slide, "✓", {
            x: iconX,
            y: currentY,
            w: 0.2,
            h: 0.4,
            fontSize: 14,
            color: ACCENT_COLOR,
            bold: true,
            valign: 'top'
        });

        // Add Text (using rich text for bolding)
        addTextSafely(slide, [
            { text: item.bold + " ", options: { color: TEXT_BOLD_COLOR, bold: true, fontSize: 10 } },
            { text: item.text, options: { color: TEXT_COLOR, fontSize: 10 } }
        ], {
            x: textX,
            y: currentY,
            w: textW,
            h: ITEM_HEIGHT - 0.1, // Slightly less than item height for padding
            lineSpacing: 18, // ~1.5 line height for 10pt font
            valign: 'top'
        });

        // Increment Y position for the next item
        currentY += ITEM_HEIGHT;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_2_agenda.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
