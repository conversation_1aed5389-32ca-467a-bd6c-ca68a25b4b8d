import os
import firebase_admin
from firebase_admin import credentials, auth
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
import json
from loguru import logger

# Initialize Firebase Admin SDK
def initialize_firebase():
    """Initialize Firebase Admin SDK with service account or default credentials"""
    if not firebase_admin._apps:
        try:
            # Try to use service account key file first
            service_account_path = os.getenv('FIREBASE_SERVICE_ACCOUNT_PATH')
            if service_account_path and os.path.exists(service_account_path):
                cred = credentials.Certificate(service_account_path)
                firebase_admin.initialize_app(cred)
                logger.info("Firebase Admin initialized with service account")
            else:
                # Use default credentials (for Cloud Run)
                firebase_admin.initialize_app()
                logger.info("Firebase Admin initialized with default credentials")
        except Exception as e:
            logger.error(f"Failed to initialize Firebase Admin: {e}")
            raise

# Initialize Firebase on module import
initialize_firebase()

# Security scheme for FastAPI
security = HTTPBearer()

class FirebaseUser:
    def __init__(self, uid: str, email: str, name: Optional[str] = None):
        self.uid = uid
        self.email = email
        self.name = name

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> FirebaseUser:
    """
    Verify Firebase ID token and return user information
    """
    try:
        # Extract token from Authorization header
        id_token = credentials.credentials
        
        # Verify the token
        decoded_token = auth.verify_id_token(id_token)
        
        # Extract user information
        uid = decoded_token['uid']
        email = decoded_token.get('email', '')
        name = decoded_token.get('name', decoded_token.get('display_name', ''))
        
        logger.info(f"Authenticated user: {uid} ({email})")
        
        return FirebaseUser(uid=uid, email=email, name=name)
        
    except auth.InvalidIdTokenError:
        logger.warning("Invalid Firebase ID token")
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication token"
        )
    except auth.ExpiredIdTokenError:
        logger.warning("Expired Firebase ID token")
        raise HTTPException(
            status_code=401,
            detail="Authentication token has expired"
        )
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=401,
            detail="Authentication failed"
        )

# Optional: For endpoints that can work with or without authentication
async def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))) -> Optional[FirebaseUser]:
    """
    Get user if authenticated, otherwise return None
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None
