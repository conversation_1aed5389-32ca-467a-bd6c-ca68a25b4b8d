#!/bin/bash

# Setup staging Cloud Build trigger for pptx-planner
# This creates a new trigger that deploys to staging environment

PROJECT_ID="gen-lang-client-0822415637"
TRIGGER_NAME="pptx-planner-staging-trigger"

echo "🚀 Setting up staging Cloud Build trigger..."
echo "Project: $PROJECT_ID"
echo "Trigger: $TRIGGER_NAME"

# Set the project
gcloud config set project $PROJECT_ID

# Create the staging trigger
echo "📝 Creating staging trigger..."
gcloud builds triggers create github \
    --project=$PROJECT_ID \
    --repo-name=pptx-planner \
    --repo-owner=DuyquanDuc \
    --branch-pattern="^(staging|develop)$" \
    --build-config=cloudbuild_stg.yaml \
    --name=$TRIGGER_NAME \
    --description="Build and deploy to Cloud Run staging service on push to staging/develop branches" \
    --service-account="projects/$PROJECT_ID/serviceAccounts/<EMAIL>" \
    --substitutions="_SERVICE_NAME=pptx-planner-staging,_DEPLOY_REGION=asia-northeast1,_AR_HOSTNAME=asia-northeast1-docker.pkg.dev,_AR_PROJECT_ID=$PROJECT_ID,_AR_REPOSITORY=cloud-run-source-deploy,_PLATFORM=managed"

# Get the trigger ID for reference
echo "📋 Getting trigger details..."
TRIGGER_ID=$(gcloud builds triggers list --project=$PROJECT_ID --filter="name:$TRIGGER_NAME" --format="value(id)")

if [ -n "$TRIGGER_ID" ]; then
    echo "✅ Staging trigger created successfully!"
    echo "🆔 Trigger ID: $TRIGGER_ID"
    
    # Update the cloudbuild_stg.yaml with the actual trigger ID
    echo "📝 Updating cloudbuild_stg.yaml with trigger ID..."
    sed -i "s/STAGING_TRIGGER_ID_PLACEHOLDER/$TRIGGER_ID/g" ../cloudbuild_stg.yaml
    sed -i "s/STAGING_TRIGGER_ID_PLACEHOLDER/$TRIGGER_ID/g" trigger-config-staging.yaml
    
    echo "✅ Configuration files updated with trigger ID!"
else
    echo "❌ Failed to get trigger ID. Please check the trigger creation."
    exit 1
fi

echo ""
echo "🚀 Staging environment setup complete!"
echo ""
echo "📋 Summary:"
echo "  - Trigger Name: $TRIGGER_NAME"
echo "  - Trigger ID: $TRIGGER_ID"
echo "  - Service Name: pptx-planner-staging"
echo "  - Branches: staging, develop"
echo "  - Build Config: cloudbuild_stg.yaml"
echo ""
echo "🔍 Key differences from production:"
echo "  - Service name: pptx-planner-staging (instead of pptx-planner)"
echo "  - GCS Bucket: pptx-planner-storage-staging"
echo "  - Environment label: staging"
echo "  - Triggers on: staging/develop branches"
echo ""
echo "🚀 Next steps:"
echo "1. Create a 'staging' or 'develop' branch in your repository"
echo "2. Push changes to trigger the first staging deployment"
echo "3. Monitor the build: gcloud builds list --limit=5"
echo "4. Access staging service at: https://pptx-planner-staging-[hash]-an.a.run.app"
echo ""
echo "🔧 To view trigger details:"
echo "gcloud builds triggers describe $TRIGGER_ID --project=$PROJECT_ID"
