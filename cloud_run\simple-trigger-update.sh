#!/bin/bash

# Simple approach: Update the trigger by modifying just the deploy step
PROJECT_ID="gen-lang-client-0822415637"
TRIGGER_ID="87b6256e-1f73-4759-a555-593752bd8bc7"

echo "🔧 Updating Cloud Build trigger deploy step..."

# Use gcloud builds triggers update command instead of import
gcloud builds triggers update $TRIGGER_ID \
    --project=$PROJECT_ID \
    --build-config=cloudbuild.yaml

echo "✅ Trigger updated!"
