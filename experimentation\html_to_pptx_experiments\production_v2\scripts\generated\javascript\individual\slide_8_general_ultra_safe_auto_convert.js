const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE POSITIONING & STYLE CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;

    // Colors from HTML analysis
    const BG_COLOR = '0a192f';
    const ACCENT_COLOR = '64ffda';
    const TEXT_COLOR_PRIMARY = 'ccd6f6';
    const TEXT_COLOR_SECONDARY = 'a8b2d1';
    const BORDER_COLOR = '1a3a6e';
    const TABLE_HEADER_FILL = '1a3a6e'; // Using a solid, darker version for contrast

    // Set slide background
    slide.background = { color: BG_COLOR };

    // Add Title
    slide.addText("Investing in a Secure Future", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 16,
        color: ACCENT_COLOR,
        bold: true,
        align: 'left'
    });

    // Add Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.8,
        w: 1.5, // Safe width
        h: 0.04,
        fill: { color: ACCENT_COLOR }
    });

    // --- TWO-COLUMN LAYOUT ---

    // LEFT COLUMN: Cost Table
    const LEFT_COL_X = SAFE_MARGIN;
    const LEFT_COL_W = 4.8;

    const tableRows = [
        // Header Row
        [
            { text: "Category", options: { bold: true, fontSize: 9, color: TEXT_COLOR_SECONDARY, fill: TABLE_HEADER_FILL } },
            { text: "Estimated Cost", options: { bold: true, fontSize: 9, color: TEXT_COLOR_SECONDARY, fill: TABLE_HEADER_FILL } },
            { text: "Notes", options: { bold: true, fontSize: 9, color: TEXT_COLOR_SECONDARY, fill: TABLE_HEADER_FILL } }
        ],
        // Data Rows
        ["Software & Cloud Services", "$50,000/year", "Azure AD, Intune, Cloudflare ZTNA..."],
        ["Hardware", "$5,000", "Minimal, depends on existing infra."],
        ["Personnel Costs", "$150,000/year", "Security team, cloud experts, training"],
        ["Training Costs", "$10,000", "Employee and technical training"],
        ["Consulting Fees", "$20,000", "If using external consultants"],
        // Footer Row (styled differently)
        [
            { text: "Total Estimated Cost", options: { bold: true, fontSize: 9, color: ACCENT_COLOR } },
            { text: "$235,000", options: { bold: true, fontSize: 9, color: ACCENT_COLOR } },
            { text: "", options: {} }
        ]
    ];

    slide.addTable(tableRows, {
        x: LEFT_COL_X,
        y: CONTENT_START_Y,
        w: LEFT_COL_W,
        colW: [1.8, 1.2, 1.8], // Total width = 4.8
        rowH: 0.3, // Ultra-safe row height
        fontSize: 8,
        color: TEXT_COLOR_PRIMARY,
        border: { type: 'solid', pt: 1, color: BORDER_COLOR },
        valign: 'middle'
    });

    // RIGHT COLUMN: ROI List
    const RIGHT_COL_X = 5.4; // LEFT_COL_X + LEFT_COL_W + 0.3 gutter
    const RIGHT_COL_W = 3.4;

    // Vertical Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: 5.25, // Centered in the gutter
        y: CONTENT_START_Y,
        w: 0,
        h: 3.8,
        line: { color: BORDER_COLOR, width: 2 }
    });

    // ROI Title
    slide.addText("Return on Investment (ROI)", {
        x: RIGHT_COL_X,
        y: CONTENT_START_Y,
        w: RIGHT_COL_W,
        h: 0.3,
        fontSize: 12,
        color: TEXT_COLOR_SECONDARY,
        bold: true
    });

    // ROI Content
    const roiItems = [
        { strong: "Reduced Risk of Data Breaches:", text: "Quantify savings from preventing costly breaches." },
        { strong: "Improved Compliance:", text: "Avoid fines and penalties from non-compliance." },
        { strong: "Increased Productivity:", text: "Streamlined access and reduced security-related downtime." },
        { strong: "Enhanced Reputation:", text: "Maintain customer trust and protect brand value." }
    ];

    let currentY = CONTENT_START_Y + 0.4; // Start below ROI title
    const ICON_SIZE = 0.2;
    const ICON_TEXT_GAP = 0.1;
    const ITEM_SPACING = 0.7; // Generous spacing for readability

    roiItems.forEach(item => {
        // Check for vertical overflow before adding new item
        if (currentY + ITEM_SPACING > MAX_CONTENT_Y) return;

        // Add text-based icon for maximum safety and compatibility
        slide.addText("✓", {
            x: RIGHT_COL_X,
            y: currentY,
            w: ICON_SIZE,
            h: ICON_SIZE,
            fontSize: 14,
            color: ACCENT_COLOR,
            bold: true,
            valign: 'top'
        });

        // Add the text content next to the icon
        slide.addText([
            { text: item.strong, options: { fontSize: 9, color: TEXT_COLOR_SECONDARY, bold: true, breakLine: true } },
            { text: item.text, options: { fontSize: 8, color: TEXT_COLOR_PRIMARY } }
        ], {
            x: RIGHT_COL_X + ICON_SIZE + ICON_TEXT_GAP,
            y: currentY,
            w: RIGHT_COL_W - (ICON_SIZE + ICON_TEXT_GAP),
            h: ITEM_SPACING - 0.1, // Height for the text block
            lineSpacing: 12 // Ultra-safe line spacing in points
        });

        currentY += ITEM_SPACING;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_8_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
