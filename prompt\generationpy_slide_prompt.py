import textwrap


#######Daniel's Prompt#######
generationpy_slide_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:
        "{query}"

        You are trying to create a set of slides for a proposal.
        So far you have created several slides, the html code for these slides are provided below.
        You are now creating another slide, the content of which is outlined under "The information that should be included on this slide is as follows:" below.
        Like the title slide, generate this slide in HTML.

        {existing_slides}

        When generating the slide, take into consideration the following points:
        - Make sure to follow a design style that matches the provided example HTML code to maintain consistency across the presentation.
        - Do not include any info from the title slide that does not belong on any other slide, such as the presenter name, their title, the date and the company logo.
        - Titles should be aligned to the top left-hand side of the slide
        - CRITICAL: Content must auto-fit within 720px height. Use these CSS patterns:
          * body: width: 1280px; height: 720px; overflow: hidden; display: flex; flex-direction: column;
          * .slide-content: position: absolute; top: 60px; left: 60px; right: 60px; bottom: 80px; max-height: 580px; display: flex; flex-direction: column; overflow: hidden;
          * .content-area: flex: 1; display: flex; flex-direction: column; overflow: hidden; min-height: 0;
          * Use clamp() for responsive fonts: font-size: clamp(0.8em, 1.5vw, 1em);
          * Use flex-shrink: 0 for titles, flex: 1 for content areas that should expand
          * For content-heavy slides: reduce all font sizes by 20-30%
        - The font size, to make sure that text fits on the slide.
          Titles should be 2.3em, subtitles at around 1.3em and normal text should be around 0.9em.
          If the slide content defined below is only a few key words or short sentences, you can increase the subtitle size up to 1.8, and normal text size up to 1.3.
        - For slides with lots of content, reduce font sizes proportionally to fit within 720px height.
        - Be creative with the slide design. Try your best to use visual elements to both enhance the message and make the slides visually engaging.
        - If relevant, use icons.
            
        This slide will become a template master slide which will define the style of the following slides, so design this slide with great care.
        Do not output any other text other than the html itself.
        IMPORTANT: DO NOT truncate any existing code with /* ... (Existing styles from Title Slide) ... */, you MUST output the full code.
        If your slides are visually appealing but also functional, you will be rewarded with a bonus.

        The information that should be included on this slide is as follows:
        {slide_content}
        """
        )


#######Duy's Prompt#######
generationpy_slide_prompt_duy_v1 = textwrap.dedent(
        """
        You are a tech consultant preparing a slide deck based on the following request:
        "{query}"

        A Professional Designer give you the following css for this Slide creation task as below::
        "{brain_storm}"

        Now you must generate the next slide, using the content defined here:
        {slide_content}

        Instructions:
        Conform to the design style of the Professional Designer.
        Keep it visually appealing and functional.This is a presentation slide, so it should be engaging and easy to read. 
        Prioritize clarity and visual appeal.Especially conventionalvisualization like charts, graphs, and icons.

        Refer to these examples for inspiration, but do not copy:
        {Example_1}, {Example_2}

        Constraints:
        - Minimize the use of animations and transitions.
        - Ensure the slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.
        
        """
        )
generationpy_slide_prompt_duy_v2 = textwrap.dedent("""
        You are a tech consultant preparing a slide deck based on the following request:
        "{query}"

        A Professional Designer give you the following css for this Slide creation task as below::
        "{brain_storm}"

        Now you must generate the next slide in full HTML and Tailwind CSS., using the content defined here:
        {slide_content}
                                                   
        You are provided a tool name "image_tavily" for images/logos/illustrations retrieval.
                                                                             
        Instructions:
        Refer to the design style of the Professional Designer.
        Keep it visually appealing and functional.This is a presentation slide, so it should be engaging and easy to read. 
        Plan for illusatrations (Prioritize simple over complex) /visualizations (Which should prioritize conventional charts and graph over complex)
        Prioritize clarity and visual appeal. Especially conventionalvisualization like charts, graphs, and icons.
                                                   
        Again this is a presentation slides LESS WORDS IS BETTER dont write LONG ASS PARAGRAPH
        

        # ALERT: BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS
                                                   
        Constraints:
        - Minimize the use of animations and transitions.
        - The slides MUST be 720p, and ensure all slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.
                                                   
        Beyond all your mission is to make this BEAUTIFUL
        
        """)
#######Hai's Prompt#######
