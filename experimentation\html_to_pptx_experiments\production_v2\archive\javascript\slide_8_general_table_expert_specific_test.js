
const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();

    // Set slide layout to 16:9 for a widescreen format similar to the HTML.
    pptx.layout = 'LAYOUT_16x9';

    const slide = pptx.addSlide();

    // Set the slide background color and add the subtle pattern from the CSS.
    slide.background = {
        color: '0a192f',
        // The SVG from the CSS background-image is complex to replicate directly.
        // A solid color is a robust alternative.
    };

    // 1. Title and Divider
    slide.addText("Investing in a Secure Future", {
        x: 0.5,
        y: 0.4,
        w: '90%',
        h: 0.5,
        fontSize: 36,
        bold: true,
        color: '64ffda'
    });

    slide.addShape(pptx.shapes.LINE, {
        x: 0.5,
        y: 1.0,
        w: 1.5, // 120px is roughly 1.5 inches in a 1280px wide slide
        h: 0,
        line: { color: '64ffda', width: 3 }
    });

    // 2. Left Column: Cost Table
    // Define table data by extracting from the HTML `<table>` element.
    const tableRows = [
        // Header Row (from <thead>)
        [
            { text: "Category", options: { bold: true, color: 'a8b2d1', fill: { color: '15243b' } } },
            { text: "Estimated Cost", options: { bold: true, color: 'a8b2d1', fill: { color: '15243b' } } },
            { text: "Notes", options: { bold: true, color: 'a8b2d1', fill: { color: '15243b' } } }
        ],
        // Data Rows (from <tbody>)
        [
            { text: "Software & Cloud Services", options: { color: 'ccd6f6' } },
            { text: "$50,000/year", options: { color: '64ffda', bold: true } },
            { text: "Azure AD, Intune, Cloudflare ZTNA, Microsoft Purview", options: { color: 'ccd6f6' } }
        ],
        [
            { text: "Hardware", options: { color: 'ccd6f6' } },
            { text: "$5,000", options: { color: '64ffda', bold: true } },
            { text: "Minimal, depends on existing infrastructure", options: { color: 'ccd6f6' } }
        ],
        [
            { text: "Personnel Costs", options: { color: 'ccd6f6' } },
            { text: "$150,000/year", options: { color: '64ffda', bold: true } },
            { text: "Security team, cloud experts, and training staff", options: { color: 'ccd6f6' } }
        ],
        [
            { text: "Training Costs", options: { color: 'ccd6f6' } },
            { text: "$10,000", options: { color: '64ffda', bold: true } },
            { text: "Employee and technical training programs", options: { color: 'ccd6f6' } }
        ],
        [
            { text: "Consulting Fees", options: { color: 'ccd6f6' } },
            { text: "$20,000", options: { color: '64ffda', bold: true } },
            { text: "If using external implementation consultants", options: { color: 'ccd6f6' } }
        ],
        // Footer Row (from <tfoot>)
        [
            { text: "Total Estimated Cost", options: { color: '64ffda', bold: true, fontSize: 12 } },
            { text: "$235,000", options: { color: '64ffda', bold: true, fontSize: 12 } },
            { text: "", options: {} }
        ]
    ];

    // Add the table to the slide with styling.
    slide.addTable(tableRows, {
        x: 0.5,
        y: 1.5,
        w: 7.0, // Flex: 3
        colW: [2.2, 1.5, 3.3],
        rowH: 0.4,
        fontSize: 10,
        border: { type: 'solid', pt: 1, color: '1a3a6e' },
        autoPage: false,
        margin: 5,
        valign: 'middle'
    });

    // 3. Right Column: ROI List
    // Add a vertical line to separate the columns.
    slide.addShape(pptx.shapes.LINE, {
        x: 7.8,
        y: 1.5,
        w: 0,
        h: 3.5,
        line: { color: '1a3a6e', width: 2 }
    });

    // ROI Title
    slide.addText("Return on Investment (ROI)", {
        x: 8.0,
        y: 1.5,
        w: 4.5,
        h: 0.4,
        fontSize: 18,
        bold: true,
        color: 'a8b2d1'
    });

    // ROI List Items
    const roiItems = [
        { boldText: "Reduced Risk of Data Breaches:", regularText: " Quantify savings from preventing costly breaches." },
        { boldText: "Improved Compliance:", regularText: " Avoid fines and penalties from non-compliance." },
        { boldText: "Increased Productivity:", regularText: " Streamlined access and reduced security-related downtime." },
        { boldText: "Enhanced Reputation:", regularText: " Maintain customer trust and protect brand value." }
    ];

    let currentY = 2.1;
    const iconPath = "https://www.svgrepo.com/show/417127/investment.svg";

    roiItems.forEach(item => {
        // Add icon
        slide.addImage({
            path: iconPath,
            x: 8.0,
            y: currentY,
            w: 0.25,
            h: 0.25
        });

        // Add text with mixed formatting (bold and regular)
        slide.addText([
            { text: item.boldText, options: { bold: true, color: 'a8b2d1', fontSize: 11 } },
            { text: item.regularText, options: { color: 'ccd6f6', fontSize: 11 } }
        ], {
            x: 8.35,
            y: currentY,
            w: 4.5,
            h: 0.6,
            lineSpacing: 22
        });

        currentY += 0.8; // Increment Y position for the next item
    });

    return pptx.writeFile({ fileName: 'generated_presentations/slide_8_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
