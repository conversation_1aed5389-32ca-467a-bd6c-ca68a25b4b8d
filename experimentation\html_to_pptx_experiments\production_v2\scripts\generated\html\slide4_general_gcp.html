<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GCP Presentation - Comprehensive Services</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
        }
        .slide-container {
            width: 1280px;
            height: 720px;
        }
        .service-card {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left-width: 4px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .service-card-header {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        .service-card-header h3 {
            font-size: 1.5rem; /* 24px */
            font-weight: 600;
            color: #14213d;
            margin-left: 0.75rem;
        }
        .service-card-body {
            padding: 1rem;
            flex-grow: 1;
        }
        .service-card-body p {
            font-size: 1.05em;
            line-height: 1.6;
            color: #495057;
            margin-bottom: 1rem;
        }
        .service-list {
            font-size: 0.95em;
            color: #6c757d;
        }
        .benefit-box {
            background-color: #e9f5ff;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-top: auto;
        }
        .benefit-box p {
            font-size: 1.05em;
            color: #005a9e;
            font-weight: 600;
            margin: 0;
        }
        .benefit-box strong {
            color: #003c6b;
        }
    </style>
</head>
<body class="bg-gray-200 flex items-center justify-center min-h-screen">

    <div class="slide-container bg-white shadow-2xl flex flex-col relative overflow-hidden">
        
        <!-- Header -->
        <div class="absolute top-0 left-0 w-full h-24 bg-white flex items-center px-16 z-10 border-b border-gray-200">
            <img src="https://download.logo.wine/logo/Google_Cloud_Platform/Google_Cloud_Platform-Logo.wine.png" alt="GCP Logo" class="h-10 mr-4">
            <h1 class="text-4xl font-bold text-gray-700">GCP: A Comprehensive Suite of Services</h1>
        </div>

        <!-- Slide Content -->
        <div class="slide-content pt-32 px-16 w-full h-full">
            <div class="grid grid-cols-2 grid-rows-2 gap-8 h-[calc(100%-8rem)]">
                
                <!-- Section 1: Compute -->
                <div class="service-card" style="border-left-color: #4285F4;">
                    <div class="service-card-header">
                        <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Compute-Engine.svg" alt="Compute Icon" class="h-10 w-10">
                        <h3>Compute</h3>
                    </div>
                    <div class="service-card-body">
                        <p>Flexible and scalable compute options for running virtual machines, containers, and serverless applications.</p>
                        <p class="service-list"><strong>Services:</strong> Compute Engine, Cloud Run, Kubernetes Engine (GKE)</p>
                        <div class="benefit-box">
                            <p><strong>Benefit:</strong> Cost-effective, high-performance, and easy to manage.</p>
                        </div>
                    </div>
                </div>

                <!-- Section 2: Data Storage & Databases -->
                <div class="service-card" style="border-left-color: #DB4437;">
                    <div class="service-card-header">
                        <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Cloud-SQL.svg" alt="Database Icon" class="h-10 w-10">
                        <h3>Data Storage & Databases</h3>
                    </div>
                    <div class="service-card-body">
                        <p>Secure and reliable storage solutions for all types of data, from structured databases to unstructured objects.</p>
                        <p class="service-list"><strong>Services:</strong> Cloud Storage, Cloud SQL, Cloud Spanner, BigQuery</p>
                        <div class="benefit-box">
                            <p><strong>Benefit:</strong> Highly durable, scalable, and optimized for analytics.</p>
                        </div>
                    </div>
                </div>

                <!-- Section 3: AI & Machine Learning -->
                <div class="service-card" style="border-left-color: #F4B400;">
                    <div class="service-card-header">
                        <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Vertex-AI.svg" alt="AI Icon" class="h-10 w-10">
                        <h3>AI & Machine Learning</h3>
                    </div>
                    <div class="service-card-body">
                        <p>Powerful AI and ML tools for building intelligent applications and gaining insights from data.</p>
                        <p class="service-list"><strong>Services:</strong> Vertex AI, Vision API, Natural Language API</p>
                        <div class="benefit-box">
                            <p><strong>Benefit:</strong> Accelerate innovation, automate tasks, and improve decision-making.</p>
                        </div>
                    </div>
                </div>

                <!-- Section 4: Networking -->
                <div class="service-card" style="border-left-color: #0F9D58;">
                    <div class="service-card-header">
                        <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Virtual-Private-Cloud.svg" alt="Networking Icon" class="h-10 w-10">
                        <h3>Networking</h3>
                    </div>
                    <div class="service-card-body">
                        <p>Secure and reliable networking infrastructure for connecting your applications and users globally.</p>
                        <p class="service-list"><strong>Services:</strong> Virtual Private Cloud (VPC), Cloud Load Balancing, Cloud CDN</p>
                        <div class="benefit-box">
                            <p><strong>Benefit:</strong> High performance, low latency, and enhanced security.</p>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- Footer -->
        <div class="absolute bottom-0 left-0 w-full h-16 bg-gray-100 flex items-center justify-between px-16">
            <span class="text-sm font-semibold text-gray-600">Unlocking Innovation with Google Cloud</span>
            <span class="text-sm font-semibold text-gray-600">Slide 4</span>
        </div>

    </div>

</body>
</html>