const PptxGenJS = require('pptxgenjs');



function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ULTRA-SAFE POSITIONING & SIZING CONSTANTS
    const SAFE_MARGIN = 0.3;
    const CONTENT_START_Y = 1.0;
    const MAX_CONTENT_Y = 4.8;
    const SLIDE_WIDTH = 10.0;
    const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN); // 9.4, but we'll use safer values

    // Color Palette (from HTML analysis)
    const COLOR_BACKGROUND = '0a192f';
    const COLOR_PRIMARY_ACCENT = '64ffda';
    const COLOR_TEXT_PRIMARY = 'ccd6f6';
    const COLOR_TEXT_SECONDARY = 'a8b2d1';
    const COLOR_TEXT_ON_ACCENT = '0a192f';

    // Set slide background
    slide.background = { color: COLOR_BACKGROUND };

    // Main Title
    slide.addText("A Strategic Roadmap to Zero Trust", {
        x: SAFE_MARGIN,
        y: 0.3,
        w: 8.2,
        h: 0.5,
        fontSize: 24, // Title can be larger, safely contained
        color: COLOR_PRIMARY_ACCENT,
        bold: true,
        align: 'left'
    });

    // Title Divider
    slide.addShape(pptx.shapes.RECTANGLE, {
        x: SAFE_MARGIN,
        y: 0.85,
        w: 1.5, // 120px is ~1.5 inches
        h: 0.04,
        fill: { color: COLOR_PRIMARY_ACCENT }
    });

    // --- Gantt Chart Layout ---
    const GANTT_START_Y = 1.4;
    const PHASE_COL_X = SAFE_MARGIN;
    const PHASE_COL_W = 2.5;
    const TIMELINE_COL_X = PHASE_COL_X + PHASE_COL_W + 0.1; // 2.9
    const TIMELINE_COL_W = SLIDE_WIDTH - TIMELINE_COL_X - SAFE_MARGIN; // 10 - 2.9 - 0.3 = 6.8
    const ROW_HEIGHT = 0.7;

    // Timeline Header Text
    const timelineLabels = ["3 Months", "6 Months", "9 Months", "12 Months"];
    const timelineLabelWidth = TIMELINE_COL_W / timelineLabels.length; // 6.8 / 4 = 1.7
    timelineLabels.forEach((label, index) => {
        slide.addText(label, {
            x: TIMELINE_COL_X + (index * timelineLabelWidth),
            y: GANTT_START_Y,
            w: timelineLabelWidth,
            h: 0.2,
            fontSize: 9,
            color: COLOR_TEXT_SECONDARY,
            align: 'center'
        });
    });

    // Timeline Header Divider Line
    slide.addShape(pptx.shapes.LINE, {
        x: TIMELINE_COL_X,
        y: GANTT_START_Y + 0.25,
        w: TIMELINE_COL_W,
        h: 0,
        line: { color: '4A5568', width: 1 } // Mapped from border-gray-700
    });

    // Gantt Chart Data
    const phases = [
        { title: "Phase 1: Assessment & Planning", desc: "Conduct security assessment, define policies, select technologies.", barWidth: 1/4, barOpacity: 'CC' },
        { title: "Phase 2: Identity & Access", desc: "Implement passwordless auth, conditional access, behavioral biometrics.", barWidth: 2/4, barOpacity: 'B3' },
        { title: "Phase 3: Device Security", desc: "Deploy endpoint management, enforce device posture, implement RBI.", barWidth: 3/4, barOpacity: '99' },
        { title: "Phase 4: Network Microsegmentation", desc: "Implement SDP, configure security groups, deploy AI anomaly detection.", barWidth: 4/4, barOpacity: '80' },
        { title: "Phase 5: Data Security & Monitoring", desc: "Implement DLP, encrypt data, and continuously monitor environment.", barWidth: 4/4, barOpacity: '66', text: "Ongoing" }
    ];

    let currentY = GANTT_START_Y + 0.4;

    phases.forEach(phase => {
        // This check guarantees no vertical overflow
        if ((currentY + ROW_HEIGHT) > MAX_CONTENT_Y) {
            console.warn(`Skipping phase "${phase.title}" to prevent vertical overflow.`);
            return;
        }

        // Left Column: Phase Title and Description
        slide.addText(phase.title, {
            x: PHASE_COL_X,
            y: currentY,
            w: PHASE_COL_W,
            h: 0.25,
            fontSize: 11,
            color: COLOR_TEXT_SECONDARY,
            bold: true
        });
        slide.addText(phase.desc, {
            x: PHASE_COL_X,
            y: currentY + 0.2,
            w: PHASE_COL_W,
            h: 0.4,
            fontSize: 8,
            color: COLOR_TEXT_PRIMARY
        });

        // Right Column: Timeline Bar
        const barW = TIMELINE_COL_W * phase.barWidth;
        slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
            x: TIMELINE_COL_X,
            y: currentY + 0.1,
            w: barW,
            h: 0.4,
            fill: { color: COLOR_PRIMARY_ACCENT, transparency: parseInt(phase.barOpacity, 16) * 100 / 255 },
            rectRadius: 0.1
        });

        // Bar Text (e.g., "Ongoing")
        if (phase.text) {
            slide.addText(phase.text, {
                x: TIMELINE_COL_X + 0.1,
                y: currentY + 0.1,
                w: barW - 0.2,
                h: 0.4,
                fontSize: 9,
                color: COLOR_TEXT_ON_ACCENT,
                bold: true,
                align: 'left',
                valign: 'middle'
            });
        }

        currentY += ROW_HEIGHT;
    });

    return pptx.writeFile({ fileName: 'generated/presentations/slide_7_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
