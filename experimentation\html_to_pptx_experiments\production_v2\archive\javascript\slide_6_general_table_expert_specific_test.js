
const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // Set slide background color and pattern
    slide.background = {
        color: "0a192f",
        // The SVG background is complex, so we use a solid color as a base.
        // Adding the pattern directly is not supported, but this maintains the theme.
    };

    // Add Slide Title
    slide.addText("Assembling the Right Team for Success", {
        x: 0.5,
        y: 0.5,
        w: "90%",
        h: 0.75,
        fontSize: 36,
        bold: true,
        color: "64ffda",
        align: 'left',
    });

    // Add Title Divider
    slide.addShape(pptx.shapes.LINE, {
        x: 0.5,
        y: 1.3,
        w: 1.2,
        h: 0,
        line: { color: "64ffda", width: 3 },
    });

    // Define the content for the left column
    const teamItems = [
        {
            icon: { path: "https://www.svgrepo.com/show/417139/security-shield.svg", options: { x: 0.5, y: 1.8, w: 0.3, h: 0.3 } },
            title: "Dedicated Security Team",
            description: "Requires experienced security engineers, architects, and analysts to lead the implementation."
        },
        {
            icon: { path: "https://www.svgrepo.com/show/489925/cloud.svg", options: { x: 0.5, y: 2.5, w: 0.3, h: 0.3 } },
            title: "Cloud Expertise",
            description: "Personnel with deep knowledge of chosen cloud platforms (Azure, AWS, or Google Cloud)."
        },
        {
            icon: { path: "https://www.svgrepo.com/show/415567/card-id-identity.svg", options: { x: 0.5, y: 3.2, w: 0.3, h: 0.3 } },
            title: "IAM Specialists",
            description: "Experts in identity and access management solutions are crucial for the core of Zero Trust."
        },
        {
            icon: { path: "https://www.svgrepo.com/show/417135/presentation.svg", options: { x: 0.5, y: 3.9, w: 0.3, h: 0.3 } },
            title: "Training & Awareness Programs",
            description: [
                { text: "Educate all employees on Zero Trust principles and secure work practices.", options: { bullet: true } },
                { text: "Provide in-depth technical training for IT staff on new technologies.", options: { bullet: true } }
            ]
        },
        {
            icon: { path: "https://www.svgrepo.com/show/331729/management-service.svg", options: { x: 0.5, y: 5.1, w: 0.3, h: 0.3 } },
            title: "Change Management",
            description: "A dedicated team to manage the cultural and operational transition, ensuring user adoption."
        }
    ];

    // --- Left Column Content ---
    let currentY = 1.8;
    const iconX = 0.5;
    const textX = 1.0;
    const textW = 5.0;

    teamItems.forEach(item => {
        // Add Icon
        slide.addImage({
            path: item.icon.path,
            x: iconX,
            y: currentY,
            w: 0.3,
            h: 0.3,
        });

        // Add Title
        slide.addText(item.title, {
            x: textX,
            y: currentY - 0.05, // Align slightly higher
            w: textW,
            h: 0.3,
            fontSize: 12,
            bold: true,
            color: "a8b2d1",
        });

        // Add Description
        if (Array.isArray(item.description)) {
            // Handle sub-list
            let subY = currentY + 0.3;
            item.description.forEach(subItem => {
                slide.addText(subItem.text, {
                    x: textX,
                    y: subY,
                    w: textW,
                    h: 0.3,
                    fontSize: 10,
                    color: "8892b0",
                    bullet: { type: 'disc' },
                    lineSpacing: 14
                });
                subY += 0.4;
            });
            currentY = subY + 0.3; // Update Y position for the next main item
        } else {
            // Handle simple text description
            slide.addText(item.description, {
                x: textX,
                y: currentY + 0.25,
                w: textW,
                h: 0.4,
                fontSize: 11,
                color: "ccd6f6",
                lineSpacing: 16
            });
            currentY += 0.7; // Increment Y for the next item
        }
    });

    // --- Right Column Content ---
    // Add the large, faded team icon
    slide.addImage({
        path: "https://www.svgrepo.com/show/475311/team.svg",
        x: 6.5,
        y: 2.0,
        w: 3.0,
        h: 3.0,
        // PptxGenJS `alpha` property (0-100) simulates CSS opacity
        alpha: 20
    });

    return pptx.writeFile({ fileName: 'generated_presentations/slide_6_general.pptx' });
}

if (require.main === module) {
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}
