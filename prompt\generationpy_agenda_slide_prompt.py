import textwrap


#######Daniel's Prompt#######

generationpy_agenda_slide_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:
        "{query}"

        You are trying to create a set of slides for a proposal.
        So far you have created the title slide, the html code for this slide is below.
        Next, you are creating the executive summary slide. Like the title slide, generate this slide in HTML.

        Title slide HTML:
        ```html
        {title_slide_html}
        ```

        Take into consideration the following points:
        - Make sure to follow a design style that matches the provided example HTML code to maintain consistency across the presentation.
        - DO NOT include the presenter name, their title, the date and any company logos on this slide. This is to save space.
        - Titles should be aligned to the top left-hand side of the slide
        - The font size, to make sure that text fits on the slide. 
        Titles should be 2.3em, subtitles at around 1.3em and normal text should be around 0.9em.
        If the slide content defined below is only a few key words or short sentences, you can increase the subtitle size up to 1.8, and normal text size up to 1.3.
        - Be creative with the slide design. Try your best to use visual elements to both enhance the message and make the slides visually engaging.
        - If you are displaying the slide content in a content grid, always set 
            grid-template-columns: auto auto;
            grid-template-rows: auto auto;
        - If relevant, use icons.
            
        This slide will become a template master slide which will define the style of the following slides, so design this slide with great care.
        Do not output any other text other than the html itself.
        IMPORTANT: DO NOT truncate any existing code with /* ... (Existing styles from Title Slide) ... */, you MUST output the full code.
        If your slides are visually appealing but also functional, you will be rewarded with a bonus.

        The information that should be included on this slide is as follows:
        {slide_content}
        """
        )

#######Duy's Prompt#######
generationpy_agenda_slide_prompt_duy_v1 = textwrap.dedent(
        """
        You’ve finished the title slide.
        Create the Agenda slide in full HTML.

        A Professional Designer give you the following css for this Slide creation task as below::
        "{brain_storm}"

        Requirements
        - Conform to the design style of the Professional Designer.

        Output ONLY the complete HTML—nothing else.

        Slide content to include
        {slide_content}

        Reference for inspiration (do not copy):
        {Example_1}
        {Example_2}

        Constraints:
        Minimize the use of animations and transitions.
        Output only the final HTML for the new slide.
        """
        )
generationpy_agenda_slide_prompt_duy_v2 = textwrap.dedent(
        """
        You’ve finished the title slide.
        Create the Agenda slide in full HTML and Tailwind CSS.

        A Professional Designer give you the following css for this Slide creation task as below::
        "{brain_storm}"

        Requirements
        - Conform to the design style of the Professional Designer.

        You are provided a tool name "image_tavily" for images/logos/illustrations retrieval.

        Output ONLY the complete HTML—nothing else.
        
        The designer had done his job
        now you only need to pay EXTREME ATTENTION to the position of the element inside the HTML. And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        # ALERT: AGAIN BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS
        Slide content to include
        {slide_content}

        Constraints:
        - Minimize the use of animations and transitions.
        - The slides MUST be 720p, and ensure all slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.


        Beyond all your mission is to make this BEAUTIFUL
        """
        )
#######Hai's Prompt#######
