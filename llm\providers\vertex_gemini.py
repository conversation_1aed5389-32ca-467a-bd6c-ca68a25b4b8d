"""
Vertex AI LLM Provider using Google GenAI SDK

This provider uses the Google GenAI SDK configured for Vertex AI infrastructure,
providing ThinkingConfig support while maintaining Vertex AI reliability.

Key Features:
- ThinkingConfig support with configurable token budget
- Vertex AI infrastructure (aiplatform.googleapis.com, not direct Gemini API)
- Robust error handling and retry mechanisms
- Support for both text and multimodal (text + images) generation
- Tavily image search tool integration for enhanced content generation
- Production-ready input validation and resource management

Architecture:
- Uses google.genai.Client(vertexai=True) for Vertex AI backend
- Service account authentication (no API keys required)
- Exponential backoff retry with jitter for reliability
- Consistent response format with token counting
"""

import os
import asyncio
import random
import io
from typing import List, Union, Dict, Any, Optional
from pathlib import Path

from PIL import Image
from loguru import logger

# Import Tavily image search tool
try:
    from llm.providers.tavily_image_search import image_tavily
    TAVILY_AVAILABLE = True
except ImportError:
    TAVILY_AVAILABLE = False
    image_tavily = None

# Import Google GenAI SDK for Vertex AI with ThinkingConfig support
try:
    import google.genai as genai
    from google.genai import types
    from google.genai.errors import ServerError
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False
    genai = None
    types = None
    ServerError = None

# Import traditional Vertex AI for fallback error handling
try:
    from google.api_core.exceptions import ResourceExhausted, ServiceUnavailable
    VERTEX_AI_ERRORS_AVAILABLE = True
except ImportError:
    VERTEX_AI_ERRORS_AVAILABLE = False
    ResourceExhausted = None
    ServiceUnavailable = None

# Configuration constants
DEFAULT_PROJECT_ID = "gen-lang-client-**********"
DEFAULT_LOCATION = "global"
DEFAULT_MODEL = "gemini-2.5-pro"
DEFAULT_THINKING_BUDGET = 128
MAX_QUERY_LENGTH = 100000
MAX_IMAGE_COUNT = 10
MAX_RETRY_DELAY = 60

# Constants for content types
DEFAULT_IMAGE_MIME = "image/jpeg"



class Vertex_Gemini_LLM:
    """
    Production-ready Vertex AI LLM provider using Google GenAI SDK.

    This provider combines the reliability of Vertex AI infrastructure with
    the advanced features of the Google GenAI SDK, including ThinkingConfig support.

    Features:
        - ThinkingConfig with configurable token budget
        - Vertex AI infrastructure (aiplatform.googleapis.com)
        - Robust error handling with exponential backoff
        - Input validation and resource limits
        - Support for text and multimodal generation
        - Production-ready logging and monitoring

    Args:
        api_key: Not used (Vertex AI uses service account authentication)
        model: Model name (default: gemini-2.5-pro)
        temperature: Sampling temperature (0.0 to 1.0)
        max_retries: Maximum retry attempts for failed requests
        base_backoff_delay: Base delay for exponential backoff (seconds)
        project_id: Google Cloud project ID
        location: Vertex AI location/region
        enable_tavily: Enable Tavily image search tools (default: True)
    """

    def __init__(
        self,
        api_key: str = None,
        model: str = DEFAULT_MODEL,
        temperature: float = 0.0,
        max_retries: int = 2,
        base_backoff_delay: float = 10.0,
        project_id: str = DEFAULT_PROJECT_ID,
        location: str = DEFAULT_LOCATION,
        thinking_budget: int = DEFAULT_THINKING_BUDGET,
        enable_tavily: bool = True
    ):
        """Initialize the Vertex AI LLM provider."""
        # Validate inputs
        self._validate_init_params(model, temperature, max_retries, base_backoff_delay, thinking_budget)

        # Store configuration
        self.model_name = model
        self.temperature = temperature
        self.max_retries = max_retries
        self.base_backoff_delay = base_backoff_delay
        self.project_id = project_id
        self.location = location
        self.thinking_budget = thinking_budget
        self.enable_tavily = enable_tavily and TAVILY_AVAILABLE
        self.api_key = api_key  # Not used, kept for compatibility

        # Initialize client and thinking config
        self._check_dependencies()
        self._init_client()
        self._init_thinking_config()

        # Log initialization status
        tavily_status = "enabled" if self.enable_tavily else "disabled"
        logger.info(f"✅ Vertex AI provider initialized: {model} @ {location}")
        logger.info(f"🔧 Tavily image search: {tavily_status}")

    def _validate_init_params(self, model: str, temperature: float, max_retries: int,
                            base_backoff_delay: float, thinking_budget: int) -> None:
        """Validate initialization parameters."""
        if not model or not model.strip():
            raise ValueError("Model name cannot be empty")
        if not 0.0 <= temperature <= 1.0:
            raise ValueError("Temperature must be between 0.0 and 1.0")
        if max_retries < 0:
            raise ValueError("Max retries must be non-negative")
        if base_backoff_delay <= 0:
            raise ValueError("Base backoff delay must be positive")
        if thinking_budget < 0:
            raise ValueError("Thinking budget must be non-negative")

    def is_tavily_enabled(self) -> bool:
        """Check if Tavily image search is enabled and available."""
        return self.enable_tavily and TAVILY_AVAILABLE

    def _check_dependencies(self) -> None:
        """Check that required dependencies are available."""
        if not GENAI_AVAILABLE:
            raise ImportError(
                "google.genai is not available. Please install: pip install google-genai"
            )

    def _init_client(self) -> None:
        """Initialize the Google GenAI client configured for Vertex AI."""
        try:
            self._setup_authentication()

            # Initialize Google GenAI client configured for Vertex AI
            self.client = genai.Client(
                vertexai=True,
                project=self.project_id,
                location=self.location
            )
            self.provider_type = "genai_vertex_ai"

        except Exception as e:
            logger.error(f"Failed to initialize Vertex AI client: {e}")
            raise RuntimeError(f"Vertex AI client initialization failed: {e}")

    def _model_path(self) -> str:
        """Return the fully-qualified model path used for Vertex AI backend.
        Keeping this in one place improves readability and future changes.
        """
        return f"publishers/google/models/{self.model_name}"

    def _init_thinking_config(self) -> None:
        """Initialize ThinkingConfig if available."""
        if not (GENAI_AVAILABLE and types):
            self.thinking_config = None
            logger.warning("ThinkingConfig not available - google.genai types missing")
            return

        try:
            self.thinking_config = types.ThinkingConfig(thinking_budget=self.thinking_budget)
            logger.info(f"✅ ThinkingConfig enabled: {self.thinking_budget} tokens")
        except Exception as e:
            self.thinking_config = None
            logger.warning(f"ThinkingConfig initialization failed: {e}")



    def _setup_authentication(self) -> None:
        """Setup authentication for Vertex AI."""
        # Ensure no direct API key is set to prevent fallback
        if os.getenv('GEMINI_API_KEY'):
            logger.debug("Removing GEMINI_API_KEY to enforce Vertex AI service-account auth.")
            del os.environ['GEMINI_API_KEY']

        # Validate project ID
        if not self.project_id:
            raise ValueError("GOOGLE_CLOUD_PROJECT or project_id is required for Vertex AI")

        # Detect environment and configure authentication
        is_cloud_run = bool(os.getenv('K_SERVICE'))
        has_gcp_project = bool(os.getenv('GOOGLE_CLOUD_PROJECT'))

        if is_cloud_run or has_gcp_project:
            # Production: Use default service account
            if 'GOOGLE_APPLICATION_CREDENTIALS' in os.environ:
                del os.environ['GOOGLE_APPLICATION_CREDENTIALS']

            # Use project ID from environment if not explicitly set
            if not self.project_id or self.project_id == DEFAULT_PROJECT_ID:
                env_project = os.getenv('GOOGLE_CLOUD_PROJECT')
                if env_project:
                    self.project_id = env_project
        else:
            # Development: Handle service account file
            self._setup_local_credentials()

    def _setup_local_credentials(self) -> None:
        """Setup local development credentials."""
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if not credentials_path:
            return

        if not os.path.isabs(credentials_path):
            # Convert relative path to absolute path
            current_dir = Path.cwd()
            for parent in [current_dir] + list(current_dir.parents):
                json_file = parent / Path(credentials_path).name
                if json_file.exists():
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(json_file)
                    logger.debug(f"Using service account: {json_file}")
                    break
            else:
                logger.warning(f"Service account file not found: {credentials_path}")

    async def call(self, query: str) -> Dict[str, Any]:
        """
        Generate text response for a given query.

        Args:
            query: The input text prompt

        Returns:
            Dictionary containing 'text' and token count information

        Raises:
            ValueError: If query is invalid
            RuntimeError: If API call fails after retries
        """
        return await self._call_vertex_ai(query)

    async def call_with_images(self, query: str, images: Union[List[Image.Image], Image.Image]) -> Dict[str, Any]:
        """
        Generate text response for a query with images.

        Args:
            query: The input text prompt
            images: Single PIL Image or list of PIL Images

        Returns:
            Dictionary containing 'text' and token count information

        Raises:
            ValueError: If query or images are invalid
            RuntimeError: If API call fails after retries
        """
        return await self._call_vertex_ai_with_images(query, images)

    async def _call_vertex_ai(self, query: str) -> Dict[str, Any]:
        """Internal method for text-only generation via Vertex AI."""
        # Input validation
        self._validate_query(query)

        # Configure generation settings
        config = self._create_generation_config()

        # Make API call with retries
        response = await self._api_call_with_retries(
            self.client.models.generate_content,
            model=self._model_path(),
            contents=query,
            config=config
        )

        # Return formatted response
        return self._format_response(response)

    def _validate_query(self, query: str) -> None:
        """Validate input query."""
        if not query or not query.strip():
            raise ValueError("Query cannot be empty")
        if len(query) > MAX_QUERY_LENGTH:
            raise ValueError(f"Query too long (max {MAX_QUERY_LENGTH:,} characters)")

    def _create_generation_config(self, include_tools: bool = True):
        """Create generation configuration with thinking config and optional tools.

        Args:
            include_tools: Whether to include Tavily image search tools
        """
        config_args = {"temperature": self.temperature}

        if self.thinking_config:
            config_args["thinking_config"] = self.thinking_config

        # Add Tavily tools if enabled and available
        if include_tools and self.enable_tavily and image_tavily:
            config_args["tools"] = [image_tavily]

        return types.GenerateContentConfig(**config_args)

    def _format_response(self, response) -> Dict[str, Any]:
        """Format API response into standard format and log tool usage."""
        # Log if the LLM made any tool calls (including Tavily)
        self._log_tool_calls(response)

        return {
            'text': response.text,
            'input_token_count': getattr(response, 'input_token_count', 0),
            'output_token_count': getattr(response, 'output_token_count', 0)
        }

    def _log_tool_calls(self, response) -> None:
        """Log any tool calls made by the LLM, particularly Tavily image searches."""
        if not hasattr(response, 'candidates') or not response.candidates:
            return

        for candidate in response.candidates:
            if not (hasattr(candidate, 'content') and hasattr(candidate.content, 'parts')):
                continue

            for part in candidate.content.parts:
                if not (hasattr(part, 'function_call') and part.function_call):
                    continue

                function_name = part.function_call.name
                if function_name == 'image_tavily':
                    args = dict(part.function_call.args) if part.function_call.args else {}
                    search_term = args.get('search_term', 'unknown')
                    logger.info(f"🤖 LLM called Tavily function with search term: '{search_term}'")

    async def _call_vertex_ai_with_images(self, query: str, images: Union[List[Image.Image], Image.Image]) -> Dict[str, Any]:
        """Internal method for multimodal generation via Vertex AI."""
        # Input validation
        self._validate_query(query)
        validated_images = self._validate_and_prepare_images(images)

        # Configure generation settings (no tools for multimodal calls)
        config = self._create_generation_config(include_tools=False)

        # Prepare content with images
        content_parts = [query]
        for img in validated_images:
            img_bytes = self._image_to_byte_array(img)
            content_parts.append(types.Part.from_bytes(img_bytes, mime_type=DEFAULT_IMAGE_MIME))

        # Make API call with retries
        response = await self._api_call_with_retries(
            self.client.models.generate_content,
            model=self._model_path(),
            contents=content_parts,
            config=config
        )

        # Return formatted response
        return self._format_response(response)

    def _validate_and_prepare_images(self, images: Union[List[Image.Image], Image.Image]) -> List[Image.Image]:
        """Validate and prepare images for processing."""
        if not images:
            raise ValueError("Images cannot be empty")

        # Ensure images is a list
        if not isinstance(images, list):
            images = [images]

        # Validate image count
        if len(images) > MAX_IMAGE_COUNT:
            raise ValueError(f"Too many images (max {MAX_IMAGE_COUNT})")

        # Validate image types
        for i, img in enumerate(images):
            if not isinstance(img, Image.Image):
                raise ValueError(f"Image {i} is not a PIL Image object")

        return images

    def _image_to_byte_array(self, image: Image.Image) -> bytes:
        """Convert PIL Image to byte array in JPEG format."""
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='JPEG', quality=85, optimize=True)
        return img_byte_arr.getvalue()

    async def _api_call_with_retries(self, func, *args, **kwargs):
        """
        Execute API call with exponential backoff retry mechanism.

        Args:
            func: The API function to call
            *args, **kwargs: Arguments to pass to the function

        Returns:
            API response

        Raises:
            RuntimeError: If all retries are exhausted
            ValueError: If wrong API endpoint is detected
        """
        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                # Execute the API call in a separate thread
                response = await asyncio.to_thread(func, *args, **kwargs)
                return response

            except Exception as e:
                last_exception = e
                error_str = str(e).lower()

                # Critical: Detect wrong API endpoint usage
                if "generativelanguage.googleapis.com" in error_str:
                    logger.error("🚨 Wrong API endpoint detected: using direct Gemini API instead of Vertex AI")
                    raise ValueError(f"Wrong API endpoint: {e}")

                # Check for authentication errors
                if "api key" in error_str or "authentication" in error_str:
                    logger.error("🚨 Authentication error in Vertex AI provider")
                    raise RuntimeError(f"Authentication failed: {e}")

                # Handle retryable errors
                if self._is_retryable_error(e) and attempt < self.max_retries:
                    delay = self._calculate_retry_delay(attempt)
                    logger.warning(f"API call failed (attempt {attempt + 1}/{self.max_retries + 1}): {e}")
                    logger.info(f"Retrying in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)
                else:
                    # Log final failure
                    logger.error(f"API call failed after {attempt + 1} attempts: {e}")
                    break

        # All retries exhausted
        raise RuntimeError(f"API call failed after {self.max_retries + 1} attempts: {last_exception}")

    def _is_retryable_error(self, error: Exception) -> bool:
        """Check if an error is retryable."""
        # Handle Google GenAI SDK errors
        if GENAI_AVAILABLE and ServerError and isinstance(error, ServerError):
            return error.code in [503, 429, 500]

        # Handle traditional Vertex AI errors
        if VERTEX_AI_ERRORS_AVAILABLE and isinstance(error, (ResourceExhausted, ServiceUnavailable)):
            return True

        # Handle generic network/timeout errors
        error_str = str(error).lower()
        retryable_patterns = [
            "timeout", "connection", "network", "unavailable",
            "overloaded", "rate limit", "quota"
        ]
        return any(pattern in error_str for pattern in retryable_patterns)

    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate delay for retry with exponential backoff and jitter."""
        base_delay = self.base_backoff_delay * (2 ** attempt)
        max_delay = min(base_delay, MAX_RETRY_DELAY)
        jitter = random.uniform(0, max_delay * 0.1)  # Add up to 10% jitter
        return max_delay + jitter


