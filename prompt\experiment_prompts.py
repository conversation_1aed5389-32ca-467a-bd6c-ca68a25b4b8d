import textwrap

##################################### Daniel Templates ##########################################################

reviewerpy_slide_review_prompt_daniel_v1 = textwrap.dedent("""
        You are a senior front-end software engineer reviewing a junior engineer's work.
        He has written some HTML which is supposed to show one slide of a powerpoint.

        You have been provided with the HTML code and also a rendering of the code as an image.

        Look at the image then validate the following criteria.
        1. Make sure that text, visual elements and content blocks are completely contained within the slide and not cut off at the bottom of the slide. 
        If this criteria is not met, reduce the vertical padding/spacing between visual elements, titles, subtitles and content blocks OR reduce the font size of the text component to meet the criteria.
        You can reduce the padding by changing the padding or gap parameters, or the margin-bottom parameter of any titles.

        2. Make sure that visual elements do NOT overlap with each other e.g. the company logo overlaps with slide content.
        If anything is overlapping, MAKE SURE to reposition or adjust the size of the frontmost element.

        Do NOT make changes to the code if the above criteria is met.
        If code changes need to be made, only output the improved HTML code, do not output any other text.
        If the code meets all of the criteria, simply output <OK>.

        The HTML code is provided below:
        {code}
        """)

reviewerpy_slide_fix_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a senior front-end software engineer reviewing a junior engineer's work.
        He has written some HTML which is supposed to show one slide of a powerpoint.
        You have been provided with the HTML code and also a rendering of the code as an image.
        As you can see, slide components, visual elements or text is overflowing from the bottom of the slide.

        The HTML code is provided below:
        {code}

        You must do the following:
        1. Think about how your can fix the HTML code to make the perfect powerpoint slide.
        Some things you can do are: reduce the vertical padding/spacing between visual elements, titles, subtitles and content blocks OR reduce the font size of the text components.
        You can reduce the padding by changing the padding or gap parameters, or the margin-bottom parameter of any titles.

        2. Make the changes and output ONLY the improved HTML code
        """
        )

slide_fix_orchestrator_prompt = textwrap.dedent("""
        You are a slide fixing orchestrator. Analyze the PowerPoint slide and decide the best fix strategy.

        AVAILABLE TOOLS:
        1. OVERFLOW_CHECKER - Fixes content that's cut off at bottom of slide (720px height limit)
        2. REVIEWER_FIX - General HTML/CSS improvements, layout fixes, and element positioning
        3. CUSTOM_FIX - Apply specific user-requested changes OR regenerate failed slides

        SLIDE TO ANALYZE:
        - HTML Code: {code}
        - User Request: {user_instructions}
        - Slide Image: [Provided for visual analysis]

        ANALYSIS INSTRUCTIONS:
        1. FIRST: Check if this is an ERROR SLIDE that failed to generate properly
           - Look for error messages like "Error rendering slide", "RESOURCE_EXHAUSTED", "Error 429", "KeyError", etc.
           - If it's an error slide, use CUSTOM_FIX (which will automatically regenerate the slide)
        2. Look at the slide image to identify visual issues
        3. Check if content overflows beyond the slide boundaries
        4. Look for layout problems (overlapping elements, poor spacing)
        5. Consider the user's specific request and what they want changed

        RESPOND WITH EXACTLY THIS FORMAT:

        TOOLS_TO_USE:
        - OVERFLOW_CHECKER: [YES/NO]
        - REVIEWER_FIX: [YES/NO]
        - CUSTOM_FIX: [YES/NO]

        EXECUTION_ORDER:
        1. [Tool name or NONE]
        2. [Tool name or NONE]
        3. [Tool name or NONE]

        REASONING:
        [Brief explanation of why you chose these tools. If it's an error slide, mention that regeneration is needed]
        """)

slide_fix_custom_prompt = textwrap.dedent("""
        You are a senior front-end developer fixing a PowerPoint slide based on user feedback.

        USER REQUEST: {user_instructions}

        CURRENT HTML CODE:
        {code}

        INSTRUCTIONS:
        1. Analyze the user's request and understand what they want changed
        2. Make the necessary modifications to the HTML/CSS code
        3. Ensure the slide still fits within 1280x720 dimensions
        4. Maintain good visual design and readability

        OUTPUT ONLY the improved HTML code, no other text.
        """)
 
generationpy_title_slide_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:

        "{query}"

        You are trying to create a set of slides for a proposal.
        The first slide you want to create is the title slide.
        Generate the title slide in HTML.

        Take into consideration the following points:
        - Choose a style that is both visually appealing and functional; befitting of a proposal from a top-tier tech consulting company.
        - What colour and design would be appropriate, especially for the background?
        - What font type should you use?
        - What should the size of the page be, to accurately reflect powerpoint slides? The slides must be 720p
        - The title font should be around 3.0em, and the subtitle around 1.8em, otherwise it is too big.
        - Make sure to include an empty footer e.g. 
            <div class="footer-bar w-full mt-auto relative">
                <div class="absolute bottom-2 right-4 text-white text-sm"></div>
            </div>
            
        This slide will become a template master slide which will define the style of the following slides, so design this slide with great care.
        Do not output any other text other than the html itself.
        If your slides are visually appealing but also functional, you will be rewarded with a bonus.

        The information that should be included on this slide is as follows:
        {slide_content}
        """)

generationpy_agenda_slide_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:
        "{query}"

        You are trying to create a set of slides for a proposal.
        So far you have created the title slide, the html code for this slide is below.
        Next, you are creating the executive summary slide. Like the title slide, generate this slide in HTML.

        Title slide HTML:
        ```html
        {title_slide_html}
        ```

        Take into consideration the following points:
        - Make sure to follow a design style that matches the provided example HTML code to maintain consistency across the presentation.
        - DO NOT include the presenter name, their title, the date and any company logos on this slide. This is to save space.
        - Titles should be aligned to the top left-hand side of the slide
        - The font size, to make sure that text fits on the slide. 
        Titles should be 2.3em, subtitles at around 1.3em and normal text should be around 0.9em.
        If the slide content defined below is only a few key words or short sentences, you can increase the subtitle size up to 1.8, and normal text size up to 1.3.
        - Be creative with the slide design. Try your best to use visual elements to both enhance the message and make the slides visually engaging.
        - If you are displaying the slide content in a content grid, always set 
            grid-template-columns: auto auto;
            grid-template-rows: auto auto;
        - If relevant, use icons.
            
        This slide will become a template master slide which will define the style of the following slides, so design this slide with great care.
        Do not output any other text other than the html itself.
        IMPORTANT: DO NOT truncate any existing code with /* ... (Existing styles from Title Slide) ... */, you MUST output the full code.
        If your slides are visually appealing but also functional, you will be rewarded with a bonus.

        The information that should be included on this slide is as follows:
        {slide_content}
        """
        )

generationpy_slide_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:
        "{query}"

        You are trying to create a set of slides for a proposal.
        So far you have created several slides, the html code for these slides are provided below.
        You are now creating another slide, the content of which is outlined under "The information that should be included on this slide is as follows:" below.
        Like the title slide, generate this slide in HTML.

        {existing_slides}

        When generating the slide, take into consideration the following points:
        - Make sure to follow a design style that matches the provided example HTML code to maintain consistency across the presentation.
        - Do not include any info from the title slide that does not belong on any other slide, such as the presenter name, their title, the date and the company logo.
        - Titles should be aligned to the top left-hand side of the slide
        - CRITICAL: Content must auto-fit within 720px height. Use these CSS patterns:
          * body: width: 1280px; height: 720px; overflow: hidden; display: flex; flex-direction: column;
          * .slide-content: position: absolute; top: 60px; left: 60px; right: 60px; bottom: 80px; max-height: 580px; display: flex; flex-direction: column; overflow: hidden;
          * .content-area: flex: 1; display: flex; flex-direction: column; overflow: hidden; min-height: 0;
          * Use clamp() for responsive fonts: font-size: clamp(0.8em, 1.5vw, 1em);
          * Use flex-shrink: 0 for titles, flex: 1 for content areas that should expand
          * For content-heavy slides: reduce all font sizes by 20-30%
        - The font size, to make sure that text fits on the slide.
          Titles should be 2.3em, subtitles at around 1.3em and normal text should be around 0.9em.
          If the slide content defined below is only a few key words or short sentences, you can increase the subtitle size up to 1.8, and normal text size up to 1.3.
        - For slides with lots of content, reduce font sizes proportionally to fit within 720px height.
        - Be creative with the slide design. Try your best to use visual elements to both enhance the message and make the slides visually engaging.
        - If relevant, use icons.
            
        This slide will become a template master slide which will define the style of the following slides, so design this slide with great care.
        Do not output any other text other than the html itself.
        IMPORTANT: DO NOT truncate any existing code with /* ... (Existing styles from Title Slide) ... */, you MUST output the full code.
        If your slides are visually appealing but also functional, you will be rewarded with a bonus.

        The information that should be included on this slide is as follows:
        {slide_content}
        """
        )

plannerpy_brainstorm_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:

        "{query}"

        Before starting work on the request, you need to brainstorm.
        From a technical perspective, how could something like this be done? 
        Please use the following pointers to guide your thought process:
        - What is the most cutting-edge way to do this?
        - How can this be done using cloud services?
        """
        )

plannerpy_slide_content_prompt_daniel_v1 = textwrap.dedent(
            """
            You are a tech consultant, and you have been given the following request:

            "{query}"

            After consulting with a senior software engineer, he has provided you the following approach to build such a system:
            "{brainstorm_response}"

            Based on the advice of the senior software engineer, you have planned out your presentation:
            "{outline_response}"

            CRITICAL INSTRUCTIONS:
            1) You MUST follow the outline structure EXACTLY - do not add extra slides beyond what's in the outline
            2) If the original request specified a number of slides (e.g., "1 slide", "3 slides"), you MUST respect that limit
            3) Create content ONLY for the slides mentioned in the outline above
            4) Do NOT create additional slides even if you think more content would be helpful

            Following the plan you have created above, and referencing the technical advice of the senior software engineer,
            describe the content that will appear on EACH slide in detail.

            Pay extra attention to the following points:
            1) If a diagram or image should go on a slide (e.g. an infrastructure diagram, organization chart or a GANTT chart etc.),
            you must describe it with enough detail such that someone reading the description would be able to reproduce it perfectly.

            2) This slide content plan will be passed on to another person, so the slide descriptions must be as precise and specific as possible.

            3) Think carefully about whether or not the needs of the client are being met with this proposal.

            4) Make sure to include the content that should appear on the title slide.

            5) STICK TO THE OUTLINE - Do not exceed the number of slides specified in the outline or original request.

            If this proposal is successful, you will get a big raise!

            IMPORTANT: Make sure to separate the content of each slide with the following markers <Slide X START> and <Slide X END>, where X represents the slide number.
            REMEMBER: Only create slides that are mentioned in the outline above. Do not add extra slides.
            """
        )



##################################### Duy Templates ##########################################################
#outline change
plannerpy_outline_prompt_duy_v1 = textwrap.dedent(
            """
           You are a tech consultant tasked with the following request:

                "{query}"

                🔒 Strict Instructions:
                If the request specifies a slide count (e.g., "1 slide", "3 slides", "single slide") — you must create exactly that many. No more, no less.

                Always structure the outline in pure Markdown — no extra commentary or explanation.

                Follow this slide format exactly:

                Slide 1: Title Slide

                Slide 2: Agenda / Index

                Each slide with 2 small points is enough do not add too much text

                Only include slides listed in the outline — do not add extra slides, even if helpful.

                ✅ Presentation Name
                {template_slides}

                📌 Example
                Website Redesign Proposal

                {example_slides}

                Reference Examples:
                Example 1: {Example_1}
                Example 2: {Example_2}
            """
        )
# This one changed
plannerpy_brainstorm_prompt_duy_v1 = textwrap.dedent(
    """You are a consultant, and you have been given the following request:

        "{query}"

        You are tasked to plan out the elements of an HTTPS presentation, including both Design Features and Technical Specifications.

        For Design Features, specify:

        The visual style or theme (e.g., Modern, Classic, Scientific, Corporate, Futuristic, etc.)

        Use of consistent branding (colors, typography, logos)

        Color palette (avoiding unattractive pallete as this is for presentation) Output a suitable color pallete

        Typography (hierarchy, font choice, spacing)

        Interactivity (hover effects, transitions, tooltips)

        Responsiveness, if relevant

        For Technical Specifications, ensure:

        Dimensions are always 1280x720px

        3+ Master Templates are created for reuse

        Use of Chart.js for interactive charts

        Use of FontAwesome icons for visual clarity

        Styling is done with Tailwind CSS

        Optional: performance enhancements (lazy loading, optimized assets)

        Present the output in two labeled sections:
        🎨 Design Features and 📐 Technical Specifications.
        Use concise bullet points, professional tone, and include any best practices.
        
        """)
#This one changed
plannerpy_slide_content_prompt_duy_v1 = textwrap.dedent(
            """
            You are a tech consultant working on the following client request:

                "{query}"

                After consulting a senior designer, you received the following design approach:

                "{brainstorm_response}"

                Based on advice from a senior software engineer, you developed this slide outline:

                "{outline_response}"

                🛑 CRITICAL INSTRUCTIONS:
                You MUST follow the exact outline — no adding or removing slides.

                If the request limits the number of slides (e.g., "1 slide", "3 slides"), you MUST respect it. Default is 5 slides if not specified.

                Slide 1 must always be the Title Slide

                Slide 2 must always be the Agenda / Index

                Create slide content only for the slides listed in the outline.

                Do NOT add extra slides or content, even if helpful.

                ✍️ Your Task:
                Write detailed content for each slide following the outline exactly. For each slide:

                Be specific and concise — it’s a presentation, not a report.

                Clearly describe any diagrams, images, or visuals, so they can be recreated by someone else.

                Ensure all client needs are addressed.

                Separate each slide using these tags:
                <Slide X START> and <Slide X END>, where X is the slide number.

            Examples:
            Example 1: {Example_1}
            Example 2: {Example_2}

            """
        )
### This one fewshot 
generationpy_slide_prompt_duy_v1 = textwrap.dedent(
        """
        You are a tech consultant preparing a slide deck based on the following request:
        "{query}"

        You’ve already created several slides, provided below:
        {existing_slides}

        Now you must generate the next slide, using the content defined here:
        {slide_content}

        Instructions:
        Match the visual style of the existing HTML slides exactly.

        Do not reuse title slide content (e.g., presenter name, date, logo).

        Title must be top-left aligned.

        Use appropriate font sizing:

        Title: 2.3em

        Subtitle: ~1.3em (up to 1.8em if content is short)

        Body text: ~0.9em (up to 1.3em if content is short)

        Make the slide visually engaging. Use layout, icons, and design creatively.

        This will become a master slide — make it clean and well-styled.

        Do not include placeholder comments like /* ... */ — output full HTML only.

        Refer to these examples for inspiration, but do not copy:
        {Example_1}, {Example_2}

        Output only the final HTML for the new slide.
        """
        )
### This one fewshots too
generationpy_agenda_slide_prompt_duy_v1 = textwrap.dedent(
        """
        You’ve finished the title slide (HTML below).
        Create the Agenda slide in full HTML.

        html
        Copy
        Edit
        {title_slide_html}
        Requirements

        Match the title-slide style exactly for visual consistency.

        Omit presenter name, date, and logos on this slide.

        Layout

        Title top-left.

        Font sizes: title 2.3em; subtitle ≈ 1.3em (up to 1.8em if content is short); body ≈ 0.9em (up to 1.3em).

        If using a content grid:

        cpp
        Copy
        Edit
        grid-template-columns: auto auto;
        grid-template-rows:    auto auto;
        Make it visually engaging—icons, graphics, creative layout encouraged.

        This becomes the master template, so code cleanly; no “/ … /” truncations.

        Output ONLY the complete HTML—nothing else.

        Slide content to include
        {slide_content}

        Reference for inspiration (do not copy):
        {Example_1}
        {Example_2}
        """
        )
### This one fewshots too
generationpy_title_slide_prompt_duy_v1 = textwrap.dedent(
        """
        You’re a tech consultant creating a proposal slide deck for:

        "{query}"

        Start by generating the Title Slide in full HTML.

        🔧 Requirements:
        Style should be clean, modern, and professional — suitable for a top-tier tech consultancy.

        Use a visually appealing background (color, gradient, or minimalist design).

        Font should be professional and web-safe (e.g., Inter, Helvetica, Arial).

        Slide size must be 1280×720px (720p).

        Font sizes:

        Title: 3.0em

        Subtitle: 1.8em

        Include an empty footer exactly like this:

        html
        Copy
        Edit
        <div class="footer-bar w-full mt-auto relative">
        <div class="absolute bottom-2 right-4 text-white text-sm"></div>
        </div>
        This is your master template slide, so design it with great care.

        Output only the HTML code — no explanations or comments.

        Do not truncate code with /* ... */.

        📄 Slide content to include:
        {slide_content}

        💡 Reference examples (do not copy):
        Example 1: {Example_1}
        Example 2: {Example_2}
        """)
##################################### Hai Templates ##########################################################

plannerpy_outline_prompt_haint_v1 = textwrap.dedent(
            """
            You are a tech consultant, and you have been given the following request:

            "{query}"

            IMPORTANT: If the request specifies a number of slides (e.g., "1 slide", "3 slides", "single slide"), you MUST create exactly that many slides. Do not exceed the requested number.

            You are an expert in crafting concise, structured presentation outlines. Generate an outline in pure Markdown following this structure **exactly**—no extra words, no commentary:

            ### Presentation Name

            {template_slides}

            ---

            ### Example

            ### Website Redesign Proposal

            {example_slides}
            """
        )

##################################### Improved Fix Templates ##########################################################

# Ultra-aggressive overflow fix prompt with style reference support
overflow_fix_ultra_aggressive_prompt = textwrap.dedent("""
        You are a professional presentation designer fixing a slide that has content overflow issues.

        Current HTML that needs fixing:
        {current_html}

        {style_context}

        CRITICAL REQUIREMENTS:
        1. MAINTAIN VISUAL QUALITY: This slide must look professional and beautiful, not cramped or ugly
        2. PRESERVE DESIGN CONSISTENCY: Keep the same visual style, colors, fonts, and layout approach as reference slides
        3. FIT WITHIN 720px HEIGHT: Ensure all content fits properly without overflow

        SMART OPTIMIZATION STRATEGIES (apply intelligently):

        🎨 DESIGN-FIRST APPROACH:
        - Keep the slide visually appealing and professional
        - Maintain proper spacing and visual hierarchy
        - Use the same color scheme: Old Lace background (#FDF5E6), brown/tan colors, Montserrat/Open Sans fonts
        - Preserve the slide-container structure and Tailwind CSS classes

        📐 LAYOUT OPTIMIZATION:
        - Use CSS Grid or Flexbox more efficiently (2-column layouts, compact grids)
        - Optimize padding/margins: reduce by 20-30% but keep visual balance
        - Use responsive font sizing with clamp() functions
        - Implement proper overflow handling with CSS

        📝 CONTENT OPTIMIZATION (only if needed):
        - Condense bullet points intelligently (keep meaning, reduce words)
        - Use icons or visual elements to replace text where appropriate
        - Group related items together
        - Prioritize most important information

        🔧 TECHNICAL FIXES:
        - Apply proper CSS height constraints: max-height: 720px; overflow: hidden;
        - Use flex-shrink and flex-grow appropriately
        - Implement responsive typography that scales properly
        - Ensure proper box-sizing and positioning

        IMPORTANT: The result should be a beautiful, professional slide that maintains the design quality of the reference slides while fitting within the height constraint.

        Output only the complete, fixed HTML code with no additional text.
        """)

# Style-aware reviewer prompt for overflow-safe fixes
reviewer_fix_overflow_safe_prompt = textwrap.dedent("""
        You are reviewing a PowerPoint slide for improvements. The overflow has already been handled by a previous tool.

        User Request: {user_instructions}

        Current HTML:
        {current_html}
        {style_context}

        CRITICAL STRUCTURAL REQUIREMENTS:
        1. MUST include Tailwind CSS: <script src="https://cdn.tailwindcss.com"></script>
        2. MUST use slide-container class structure exactly like reference slides
        3. MUST include Font Awesome and Google Fonts links

        IMPORTANT: Do NOT modify font sizes or content positioning as overflow has already been fixed.
        Focus only on:
        - Applying exact structure from reference slides (Tailwind + slide-container)
        - Color scheme improvements (MUST use brown/tan colors from reference)
        - Text clarity and readability
        - Visual design enhancements that don't affect sizing

        Copy the exact HTML structure and CSS approach from reference slides.

        Output only the improved HTML code, no other text.
        """)

# Style-aware reviewer prompt for general fixes
reviewer_fix_general_prompt = textwrap.dedent("""
        You are a senior front-end developer improving a PowerPoint slide based on user feedback.

        User Request: {user_instructions}

        Current HTML:
        {current_html}
        {style_context}

        CRITICAL STRUCTURAL REQUIREMENTS:
        1. MUST include Tailwind CSS: <script src="https://cdn.tailwindcss.com"></script>
        2. MUST use slide-container class structure exactly like reference slides
        3. MUST include Font Awesome: <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        4. MUST include Google Fonts: <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600;700&family=Open+Sans:wght@300;400&display=swap" rel="stylesheet">

        STYLING REQUIREMENTS:
        - MUST use Old Lace background (#FDF5E6) with texture overlay in slide-container
        - MUST use brown (#8B4513) for titles, tan (#D2B48C) for subheadings
        - MUST use Montserrat for headings, Open Sans for body text
        - DO NOT create custom CSS - use the exact same structure as reference slides
        - DO NOT use blue colors (#0056b3, #007bff)

        IMPORTANT: Copy the exact HTML structure, CSS classes, and styling approach from the reference slides. Do not reinvent the styling - use what already works.

        Output only the improved HTML code, no other text.
        """)

# High-quality comprehensive fix prompt (based on generation.py patterns)
slide_fix_comprehensive_prompt = textwrap.dedent("""
        You are a tech consultant improving a slide deck based on the following request:
        "{user_instructions}"

        A Professional Designer provided the following design guidance for this slide improvement task:
        {style_context}

        Current slide HTML that needs improvement:
        {current_html}

        Instructions:
        - Follow the design style of the Professional Designer exactly
        - Keep it visually appealing and functional - this is a presentation slide, so it should be engaging and easy to read
        - Prioritize clarity and visual appeal, especially conventional visualization like charts, graphs, and icons
        - Use proper Tailwind CSS classes and maintain the slide-container structure
        - Ensure the slide content fits within a height of 720px
        - LESS WORDS IS BETTER - don't write long paragraphs
        - BE EXTREMELY CAREFUL ABOUT ELEMENT POSITIONS and choose the right size of elements
        - Maintain exact visual consistency with the reference slides

        IMPROVEMENT FOCUS:
        - Apply the exact color scheme from reference slides (Old Lace background, brown/tan colors)
        - Match fonts and typography (Montserrat for headings, Open Sans for body)
        - Enhance visual design and layout organization
        - Add visual elements (icons, graphics) where appropriate
        - Ensure professional presentation quality

        Constraints:
        - Minimize the use of animations and transitions
        - Ensure the slide content fits within a height of 720px
        - Output only the final HTML for the improved slide

        Your mission is to make this BEAUTIFUL while maintaining exact consistency with the reference design.
        """)

# High-quality slide regeneration prompts (based on generation.py patterns)
slide_regeneration_with_content_prompt = textwrap.dedent("""
        You are a tech consultant preparing a slide deck based on the following request:
        "{user_prompt}"

        A Professional Designer provided the following design guidance for this slide creation task:
        {style_context}

        You are regenerating a slide that failed to generate properly. Use the original slide specification:
        {original_slide_content}

        Instructions:
        - Follow the design style of the Professional Designer exactly
        - Keep it visually appealing and functional - this is a presentation slide, so it should be engaging and easy to read
        - Prioritize clarity and visual appeal, especially conventional visualization like charts, graphs, and icons
        - Use proper Tailwind CSS classes and maintain the slide-container structure
        - Ensure the slide content fits within a height of 720px
        - LESS WORDS IS BETTER - don't write long paragraphs
        - BE EXTREMELY CAREFUL ABOUT ELEMENT POSITIONS and choose the right size of elements

        Constraints:
        - Minimize the use of animations and transitions
        - Ensure the slide content fits within a height of 720px
        - Output only the final HTML for the new slide

        Your mission is to make this BEAUTIFUL while maintaining exact consistency with the reference design.
        """)

slide_regeneration_basic_prompt = textwrap.dedent("""
        You are a tech consultant preparing a slide deck based on the following request:
        "{user_instructions}"

        A Professional Designer provided the following design guidance for this slide creation task:
        {style_context}

        The previous slide generation failed with an error. Please generate a new slide that addresses the user's request.

        Instructions:
        - Follow the design style of the Professional Designer exactly
        - Keep it visually appealing and functional - this is a presentation slide, so it should be engaging and easy to read
        - Prioritize clarity and visual appeal, especially conventional visualization like charts, graphs, and icons
        - Use proper Tailwind CSS classes and maintain the slide-container structure
        - Ensure the slide content fits within a height of 720px
        - LESS WORDS IS BETTER - don't write long paragraphs
        - BE EXTREMELY CAREFUL ABOUT ELEMENT POSITIONS and choose the right size of elements

        Constraints:
        - Minimize the use of animations and transitions
        - Ensure the slide content fits within a height of 720px
        - Output only the final HTML for the new slide

        Your mission is to make this BEAUTIFUL while maintaining exact consistency with the reference design.
        """)
