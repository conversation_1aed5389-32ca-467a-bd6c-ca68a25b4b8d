from typing import List, Union
from PIL import Image
import random
import io
import asyncio
import os
from pathlib import Path
from loguru import logger

# Import Google GenAI SDK for Vertex AI with ThinkingConfig support
try:
    import google.genai as genai
    from google.genai import types
    from google.genai.errors import ServerError
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False
    genai = None
    types = None
    ServerError = None

# Import traditional Vertex AI for fallback error handling
try:
    from google.api_core.exceptions import ResourceExhausted, ServiceUnavailable
    VERTEX_AI_ERRORS_AVAILABLE = True
except ImportError:
    VERTEX_AI_ERRORS_AVAILABLE = False
    ResourceExhausted = None
    ServiceUnavailable = None

# Configuration constants
DEFAULT_PROJECT_ID = "gen-lang-client-**********"
DEFAULT_LOCATION = "asia-northeast1"
DEFAULT_MODEL = "gemini-2.5-pro"
DEFAULT_THINKING_BUDGET = 8492
MAX_QUERY_LENGTH = 100000
MAX_IMAGE_COUNT = 10
MAX_RETRY_DELAY = 60

# Constants for content types
DEFAULT_IMAGE_MIME = "image/png"

class Vertex_HTML_Translator_LLM:
    """
    Vertex AI LLM provider for HTML-to-PowerPoint translation using Google GenAI SDK.

    This provider uses the Google GenAI SDK configured for Vertex AI infrastructure,
    optimized specifically for HTML translation tasks without tools.

    Key features:
    - Uses Vertex AI infrastructure (aiplatform.googleapis.com, not direct Gemini API)
    - NO TOOLS (no image_tavily or other tools) - pure text generation
    - ThinkingConfig support with configurable token budget
    - Robust error handling and retry mechanisms
    - Support for both text and multimodal (text + images) generation
    - Optimized for HTML-to-PowerPoint translation tasks
    - Production-ready input validation and resource management

    Architecture:
    - Uses google.genai.Client(vertexai=True) for Vertex AI backend
    - Service account authentication (no API keys required)
    - Exponential backoff retry with jitter for reliability
    - Consistent response format with token counting
    """

    def __init__(self, project_id: str = DEFAULT_PROJECT_ID,
                 location: str = DEFAULT_LOCATION,
                 model: str = DEFAULT_MODEL,
                 temperature: float = 0.2,
                 max_retries: int = 2,
                 base_backoff_delay: float = 10.0,
                 thinking_budget: int = DEFAULT_THINKING_BUDGET):
        """Initialize the Vertex AI HTML Translator LLM provider."""
        # Validate inputs
        self._validate_init_params(model, temperature, max_retries, base_backoff_delay, thinking_budget)

        # Store configuration
        self.project_id = project_id
        self.location = location
        self.model_name = model
        self.temperature = temperature
        self.max_retries = max_retries
        self.base_backoff_delay = base_backoff_delay
        self.max_backoff_delay = 45.0
        self.thinking_budget = thinking_budget

        # Initialize client and thinking config
        self._check_dependencies()
        self._init_client()
        self._init_thinking_config()

        # Log initialization status
        logger.info(f"✅ Vertex AI HTML Translator initialized: {model} @ {location}")
        logger.info(f"🔧 ThinkingConfig: {'enabled' if self.thinking_config else 'disabled'}")

    def _validate_init_params(self, model: str, temperature: float, max_retries: int,
                            base_backoff_delay: float, thinking_budget: int) -> None:
        """Validate initialization parameters."""
        if not model or not model.strip():
            raise ValueError("Model name cannot be empty")
        if not 0.0 <= temperature <= 1.0:
            raise ValueError("Temperature must be between 0.0 and 1.0")
        if max_retries < 0:
            raise ValueError("Max retries must be non-negative")
        if base_backoff_delay <= 0:
            raise ValueError("Base backoff delay must be positive")
        if thinking_budget < 0:
            raise ValueError("Thinking budget must be non-negative")

    def _check_dependencies(self) -> None:
        """Check that required dependencies are available."""
        if not GENAI_AVAILABLE:
            raise ImportError(
                "google.genai is not available. Please install: pip install google-genai"
            )

    def _init_client(self) -> None:
        """Initialize the Google GenAI client configured for Vertex AI."""
        try:
            self._setup_authentication()

            # Initialize Google GenAI client configured for Vertex AI
            self.client = genai.Client(
                vertexai=True,
                project=self.project_id,
                location=self.location
            )
            self.provider_type = "genai_vertex_ai"

        except Exception as e:
            logger.error(f"Failed to initialize Vertex AI client: {e}")
            raise RuntimeError(f"Vertex AI client initialization failed: {e}")

    def _model_path(self) -> str:
        """Return the fully-qualified model path used for Vertex AI backend."""
        return f"publishers/google/models/{self.model_name}"

    def _init_thinking_config(self) -> None:
        """Initialize ThinkingConfig if available."""
        if not (GENAI_AVAILABLE and types):
            self.thinking_config = None
            logger.warning("ThinkingConfig not available - google.genai types missing")
            return

        try:
            self.thinking_config = types.ThinkingConfig(thinking_budget=self.thinking_budget)
            logger.info(f"✅ ThinkingConfig enabled: {self.thinking_budget} tokens")
        except Exception as e:
            self.thinking_config = None
            logger.warning(f"ThinkingConfig initialization failed: {e}")

    def _setup_authentication(self):
        """
        Setup Google Cloud authentication by resolving the service account path
        """
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if credentials_path:
            # Convert relative path to absolute path
            if not os.path.isabs(credentials_path):
                # Find the workspace root (where the JSON file should be)
                current_dir = Path.cwd()
                workspace_root = None

                # Look for the JSON file in current directory and parent directories
                for parent in [current_dir] + list(current_dir.parents):
                    json_file = parent / Path(credentials_path).name
                    if json_file.exists():
                        workspace_root = parent
                        break

                if workspace_root:
                    absolute_path = workspace_root / Path(credentials_path).name
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(absolute_path)
                    logger.info(f"Resolved service account path: {absolute_path}")
                else:
                    logger.warning(f"Service account file not found: {credentials_path}")
            else:
                logger.info(f"Using absolute service account path: {credentials_path}")
        else:
            logger.info("No GOOGLE_APPLICATION_CREDENTIALS found, using Cloud Run default service account authentication")

    async def _api_call_with_retries(self, func, *args, **kwargs):
        """
        Execute API call with exponential backoff retry mechanism.

        Args:
            func: The API function to call
            *args, **kwargs: Arguments to pass to the function

        Returns:
            API response

        Raises:
            RuntimeError: If all retries are exhausted
            ValueError: If wrong API endpoint is detected
        """
        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                # Execute the API call in a separate thread
                response = await asyncio.to_thread(func, *args, **kwargs)
                return response

            except Exception as e:
                last_exception = e
                error_str = str(e).lower()

                # Critical: Detect wrong API endpoint usage
                if "generativelanguage.googleapis.com" in error_str:
                    logger.error("🚨 Wrong API endpoint detected: using direct Gemini API instead of Vertex AI")
                    raise ValueError(f"Wrong API endpoint: {e}")

                # Check for authentication errors
                if "api key" in error_str or "authentication" in error_str:
                    logger.error("🚨 Authentication error in Vertex AI provider")
                    raise RuntimeError(f"Authentication failed: {e}")

                # Handle retryable errors
                if self._is_retryable_error(e) and attempt < self.max_retries:
                    delay = self._calculate_retry_delay(attempt)
                    logger.warning(f"API call failed (attempt {attempt + 1}/{self.max_retries + 1}): {e}")
                    logger.info(f"Retrying in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)
                else:
                    # Log final failure
                    logger.error(f"API call failed after {attempt + 1} attempts: {e}")
                    break

        # All retries exhausted
        raise RuntimeError(f"API call failed after {self.max_retries + 1} attempts: {last_exception}")

    def _is_retryable_error(self, error: Exception) -> bool:
        """Check if an error is retryable."""
        # Handle Google GenAI SDK errors
        if GENAI_AVAILABLE and ServerError and isinstance(error, ServerError):
            return error.code in [503, 429, 500]

        # Handle traditional Vertex AI errors
        if VERTEX_AI_ERRORS_AVAILABLE and isinstance(error, (ResourceExhausted, ServiceUnavailable)):
            return True

        # Handle generic network/timeout errors
        error_str = str(error).lower()
        retryable_patterns = [
            "timeout", "connection", "network", "unavailable",
            "overloaded", "rate limit", "quota"
        ]
        return any(pattern in error_str for pattern in retryable_patterns)

    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate delay for retry with exponential backoff and jitter."""
        base_delay = self.base_backoff_delay * (2 ** attempt)
        max_delay = min(base_delay, MAX_RETRY_DELAY)
        jitter = random.uniform(0, max_delay * 0.1)  # Add up to 10% jitter
        return max_delay + jitter

    async def call(self, query: str):
        """
        Generate text response for a given query.
        Pure text generation call - NO TOOLS for HTML translation tasks.

        Args:
            query: The input text prompt

        Returns:
            Dictionary containing 'text' and token count information

        Raises:
            ValueError: If query is invalid
            RuntimeError: If API call fails after retries
        """
        return await self._call_vertex_ai(query)

    async def _call_vertex_ai(self, query: str) -> dict:
        """Internal method for text-only generation via Vertex AI."""
        # Input validation
        self._validate_query(query)

        # Configure generation settings (no tools for HTML translation)
        config = self._create_generation_config()

        # Make API call with retries
        response = await self._api_call_with_retries(
            self.client.models.generate_content,
            model=self._model_path(),
            contents=query,
            config=config
        )

        # Return formatted response
        return self._format_response(response)

    def _validate_query(self, query: str) -> None:
        """Validate input query."""
        if not query or not query.strip():
            raise ValueError("Query cannot be empty")
        if len(query) > MAX_QUERY_LENGTH:
            raise ValueError(f"Query too long (max {MAX_QUERY_LENGTH:,} characters)")

    def _create_generation_config(self):
        """Create generation configuration with thinking config (no tools)."""
        config_args = {"temperature": self.temperature}

        if self.thinking_config:
            config_args["thinking_config"] = self.thinking_config

        return types.GenerateContentConfig(**config_args)

    def _format_response(self, response) -> dict:
        """Format API response into standard format."""
        return {
            'text': response.text,
            'input_token_count': getattr(response, 'input_token_count', 0),
            'output_token_count': getattr(response, 'output_token_count', 0)
        }

    async def call_with_images(self, query: str, images: List[Union[str, Image.Image]]):
        """
        Generate text response for a query with images.
        (Though HTML translator typically won't need this)

        Args:
            query: The input text prompt
            images: List of image file paths or PIL Image objects

        Returns:
            Dictionary containing 'text' and token count information

        Raises:
            ValueError: If query or images are invalid
            RuntimeError: If API call fails after retries
        """
        return await self._call_vertex_ai_with_images(query, images)

    async def _call_vertex_ai_with_images(self, query: str, images: List[Union[str, Image.Image]]) -> dict:
        """Internal method for multimodal generation via Vertex AI."""
        # Input validation
        self._validate_query(query)
        validated_images = self._validate_and_prepare_images(images)

        # Configure generation settings (no tools for HTML translation)
        config = self._create_generation_config()

        # Prepare content with images
        content_parts = [query]
        for img in validated_images:
            img_bytes = self._image_to_byte_array(img)
            content_parts.append(types.Part.from_bytes(img_bytes, mime_type=DEFAULT_IMAGE_MIME))

        # Make API call with retries
        response = await self._api_call_with_retries(
            self.client.models.generate_content,
            model=self._model_path(),
            contents=content_parts,
            config=config
        )

        # Return formatted response
        return self._format_response(response)

    def _validate_and_prepare_images(self, images: List[Union[str, Image.Image]]) -> List[Image.Image]:
        """Validate and prepare images for processing."""
        if not images:
            raise ValueError("Images cannot be empty")

        # Validate image count
        if len(images) > MAX_IMAGE_COUNT:
            raise ValueError(f"Too many images (max {MAX_IMAGE_COUNT})")

        validated_images = []
        for i, img in enumerate(images):
            if isinstance(img, str):
                # Load image from file path
                try:
                    with open(img, 'rb') as f:
                        image_data = f.read()
                    pil_image = Image.open(io.BytesIO(image_data))
                    validated_images.append(pil_image)
                except Exception as e:
                    raise ValueError(f"Failed to load image {i} from path '{img}': {e}")
            elif isinstance(img, Image.Image):
                validated_images.append(img)
            else:
                raise ValueError(f"Image {i} is not a valid type (expected str path or PIL Image)")

        return validated_images

    def _image_to_byte_array(self, image: Image.Image) -> bytes:
        """Convert PIL Image to byte array in PNG format."""
        img_byte_arr = io.BytesIO()
        # Convert RGBA/P to RGB for better compatibility
        if image.mode in ("RGBA", "P"):
            image = image.convert("RGB")
        image.save(img_byte_arr, format='PNG', optimize=True)
        return img_byte_arr.getvalue()

    def get_model_info(self):
        """
        Return information about this LLM provider
        """
        return {
            "provider": "vertex_html_translator",
            "model": self.model_name,
            "temperature": self.temperature,
            "tools": "none",
            "purpose": "HTML-to-PowerPoint translation",
            "project_id": self.project_id,
            "location": self.location,
            "sdk": "google-genai",
            "backend": "vertex_ai",
            "thinking_config": "enabled" if self.thinking_config else "disabled"
        }
