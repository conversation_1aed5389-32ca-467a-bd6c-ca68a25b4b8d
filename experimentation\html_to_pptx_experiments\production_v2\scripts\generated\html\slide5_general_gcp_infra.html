<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GCP Presentation - Infrastructure and Tech Stack</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://gojs.net/latest/release/go.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
        }
        .slide-container {
            width: 1280px;
            height: 720px;
        }
    </style>
</head>
<body class="bg-gray-200 flex items-center justify-center min-h-screen">

    <div class="slide-container bg-white shadow-2xl flex flex-col relative overflow-hidden">
        
        <!-- Header -->
        <div class="absolute top-0 left-0 w-full h-24 bg-white flex items-center px-16 z-10 border-b border-gray-200">
            <img src="https://download.logo.wine/logo/Google_Cloud_Platform/Google_Cloud_Platform-Logo.wine.png" alt="GCP Logo" class="h-10 mr-4">
            <h1 class="text-4xl font-bold text-gray-700">GCP Infrastructure and Tech Stack</h1>
        </div>

        <!-- Slide Content -->
        <div class="slide-content pt-28 px-12 w-full h-full flex">
            <div class="flex-grow w-2/3 pr-6">
                <div id="myDiagramDiv" style="width: 100%; height: 520px; background-color: #f9fafb; border-radius: 8px; border: 1px solid #e5e7eb;"></div>
            </div>
            <div class="w-1/3 pl-6 border-l border-gray-200 flex flex-col justify-between">
                <div>
                    <h2 class="text-2xl font-semibold text-[#14213d] mb-4 pb-2 border-b-2 border-[#fca311]">Key Components</h2>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <img src="https://img.icons8.com/color/48/000000/geography.png" alt="Regions" class="h-8 w-8 mr-3 mt-1"/>
                            <div>
                                <h3 class="font-bold text-gray-800 text-lg">Regions and Zones</h3>
                                <p class="text-gray-600 text-base">Global coverage for high availability and low latency.</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <img src="https://img.icons8.com/color/48/000000/security-shield-green.png" alt="Security" class="h-8 w-8 mr-3 mt-1"/>
                            <div>
                                <h3 class="font-bold text-gray-800 text-lg">Built-in Security</h3>
                                <p class="text-gray-600 text-base">Multi-layered security, encryption, and compliance.</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                             <div class="flex items-center mr-3 mt-1">
                                <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/kubernetes.svg" alt="Kubernetes" class="h-8 w-8"/>
                                <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/tensorflow.svg" alt="TensorFlow" class="h-8 w-8 ml-2"/>
                            </div>
                            <div>
                                <h3 class="font-bold text-gray-800 text-lg">Open Source Commitment</h3>
                                <p class="text-gray-600 text-base">Leverage leading open-source tech like Kubernetes & TensorFlow.</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <p class="text-gray-800 text-base leading-relaxed">
                            This diagram shows a typical GCP e-commerce architecture. <strong>Cloud CDN</strong> and <strong>Load Balancing</strong> ensure high performance. <strong>Compute Engine</strong> hosts web servers, while <strong>Cloud SQL</strong> and <strong>Spanner</strong> manage product and order data. <strong>Cloud Storage</strong> holds images, with <strong>Dataflow</strong> and <strong>BigQuery</strong> providing powerful analytics.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="absolute bottom-0 left-0 w-full h-16 bg-gray-100 flex items-center justify-between px-16">
            <span class="text-sm font-semibold text-gray-600">Unlocking Innovation with Google Cloud</span>
            <span class="text-sm font-semibold text-gray-600">Slide 5</span>
        </div>
    </div>

    <script>
        function init() {
            const $ = go.GraphObject.make;

            const myDiagram =
                $(go.Diagram, "myDiagramDiv", {
                    "undoManager.isEnabled": true,
                    layout: $(go.LayeredDigraphLayout, { direction: 0, layerSpacing: 60, columnSpacing: 30 })
                });

            myDiagram.nodeTemplate =
                $(go.Node, "Auto",
                    { locationSpot: go.Spot.Center },
                    $(go.Shape, "RoundedRectangle", {
                        fill: "#ffffff",
                        stroke: "#d1d5db",
                        strokeWidth: 2,
                        portId: "",
                        fromLinkable: true, toLinkable: true,
                        fromLinkableDuplicates: true, toLinkableDuplicates: true,
                        cursor: "pointer"
                    }),
                    $(go.Panel, "Vertical", { margin: 10 },
                        $(go.Picture,
                            { width: 40, height: 40, margin: new go.Margin(0, 0, 8, 0) },
                            new go.Binding("source")),
                        $(go.TextBlock,
                            { font: "600 14px Open Sans", stroke: "#374151", textAlign: "center" },
                            new go.Binding("text"))
                    )
                );

            myDiagram.linkTemplate =
                $(go.Link,
                    { routing: go.Link.Orthogonal, corner: 10, reshapable: true, toShortLength: 4 },
                    $(go.Shape, { strokeWidth: 2, stroke: "#4b5563" }),
                    $(go.Shape, { toArrow: "Standard", fill: "#4b5563", stroke: null })
                );

            myDiagram.groupTemplate =
                $(go.Group, "Vertical",
                    {
                        selectionObjectName: "PANEL",
                        computesBoundsAfterDrag: true,
                        computesBoundsIncludingLinks: false,
                        layout: $(go.LayeredDigraphLayout, { direction: 0, layerSpacing: 40, columnSpacing: 20, setsPortSpots: false })
                    },
                    $(go.Panel, "Auto",
                        $(go.Shape, "RoundedRectangle", { fill: "rgba(128,128,128,0.1)", stroke: "#d1d5db", strokeWidth: 1.5, strokeDashArray: [4, 4] }),
                        $(go.Placeholder, { padding: 20 })
                    ),
                    $(go.TextBlock,
                        { alignment: go.Spot.Bottom, font: "bold 16px Open Sans", stroke: "#4b5563", margin: 8 },
                        new go.Binding("text", "key"))
                );

            myDiagram.model = new go.GraphLinksModel({
                nodeDataArray: [
                    // Frontend
                    { key: "CDN", text: "Cloud CDN", source: "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Cloud-CDN.svg", group: "Frontend" },
                    { key: "LB", text: "Cloud Load Balancing", source: "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Cloud-Load-Balancing.svg", group: "Frontend" },
                    { key: "WebServers", text: "Compute Engine\n(Web Servers)", source: "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Compute-Engine.svg", group: "Frontend" },
                    // Backend
                    { key: "SQL", text: "Cloud SQL\n(Product Catalog)", source: "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Cloud-SQL.svg", group: "Backend" },
                    { key: "Spanner", text: "Cloud Spanner\n(Order Management)", source: "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Cloud-Spanner.svg", group: "Backend" },
                    { key: "Storage", text: "Cloud Storage\n(Images)", source: "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Cloud-Storage.svg", group: "Backend" },
                    // Analytics
                    { key: "Dataflow", text: "Dataflow", source: "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-Dataflow.svg", group: "Analytics" },
                    { key: "BigQuery", text: "BigQuery", source: "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/gcp_svg/gcp_svg/Google-Cloud-Platform-GCP-BigQuery.svg", group: "Analytics" },
                    // Groups
                    { key: "Frontend", isGroup: true },
                    { key: "Backend", isGroup: true },
                    { key: "Analytics", isGroup: true }
                ],
                linkDataArray: [
                    { from: "CDN", to: "LB" },
                    { from: "LB", to: "WebServers" },
                    { from: "WebServers", to: "SQL" },
                    { from: "WebServers", to: "Spanner" },
                    { from: "WebServers", to: "Storage" },
                    { from: "SQL", to: "Dataflow" },
                    { from: "Spanner", to: "Dataflow" },
                    { from: "Dataflow", to: "BigQuery" }
                ]
            });
        }
        window.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>